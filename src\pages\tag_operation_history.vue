<template>
    <div class="tableData">
        <h3>{{ route.meta.title }}</h3>
        <div class="topMenu">
            <div class="menuBox">
                <el-input style="display: none;"></el-input>
                <el-input class="menu" placeholder="标签名称" v-model="curTagName" clearable
                    @change="getHistoryData"></el-input>
            </div>
            <div class="menuBox">
                <el-date-picker v-model="time1" type="datetimerange" range-separator="To" start-placeholder="创建开始时间"
                    end-placeholder="创建结束时间" @change="getHistoryData" />
            </div>
        </div>
        <el-table v-loading="load" class="table" :data="historyData" border 
            :row-style="{ height: '18px' }">
            <el-table-column prop="tag_name" label="标签名称"  />
            <el-table-column prop="category_name" label="分类"  />
            <el-table-column prop="category_name_or" label="标签原分类" />
            <el-table-column prop="source" label="来源" />
            <el-table-column prop="operation_type" label="操作" width="100" />
            <el-table-column prop="create_time" label="时间" width="200"  />
        </el-table>
        <Pagination style="margin-top: 10px; display: flex; justify-content: flex-end;" :currentPage="historyDataPage"
            :pageSize="historyDataPageSize" :total="historyDataTotal" @current-change="handleHistoryPageChange"
            @size-change="handleHistorySizeChange" />
    </div>
    <el-dialog v-model="dialogVisible" title="病例内容" width="50%" draggable>
        <template #default>
            <el-scrollbar max-height="400px">
                <p>{{ currentContent }}</p>
            </el-scrollbar>
        </template>
    </el-dialog>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { post, get } from '@/utils/request.js'
import dayjs from 'dayjs'
import c from "@/utils/config";
import Pagination from '@/components/Pagination.vue';
import { useRoute } from 'vue-router';
import { baseUrl } from '../utils/baseUrl';

const route = useRoute();

// 标签历史记录
const load = ref(false);
const time1 = ref([]);
const curTagName = ref('');
const historyData = ref([]);
const historyDataPage = ref(1);
const historyDataPageSize = ref(10);
const historyDataTotal = ref(0);
// 处理每页记录数变化
const handleHistorySizeChange = (size) => {
  historyDataPageSize.value = size;
  historyDataPage.value = 1; // 重置为第一页
  getHistoryData();
};

// 处理页码变化
const handleHistoryPageChange = (page) => {
  historyDataPage.value = page;
  getHistoryData();
};
const getHistoryData = () =>{
    load.value = true;
    // 处理时间筛选
    let create_time__gte = ''
    let create_time__lte = ''
    if (Array.isArray(time1.value) && time1.value.length === 2) {
        create_time__gte = dayjs(time1.value[0]).format('YYYY-MM-DD HH:mm:ss')
        create_time__lte = dayjs(time1.value[1]).format('YYYY-MM-DD HH:mm:ss')
    }

  let url = `${baseUrl.url8}/tag/getTagOperationLog`
  post(url, JSON.stringify({
        page: historyDataPage.value, 
        pageSize: historyDataPageSize.value,
        name: curTagName.value,
        start_date: create_time__gte,
        end_date: create_time__lte
    }))
    .then(res => {
        historyData.value = res.data.list
        historyDataTotal.value = res.data.total
        load.value = false;
    })
    .catch(err => {
        console.log(err)
        load.value = false;
    })
}

// 更新状态
onMounted(() => {
    getHistoryData()
})
onUnmounted(() => {
})
</script>
<style scoped>
.tableData {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    background-color: #ffffff;
    padding: 10px;
    box-sizing: border-box;
}
h3 {
    height: 40px;
}

.topMenu {
    display: flex;
    justify-content: flex-start;
    margin-bottom: 10px;
    flex-wrap: wrap;
    gap: 5px;
}

.menuBox {
    display: flex;
    justify-content: flex-start;
    margin-bottom: 10px;
    flex-wrap: wrap;
    gap: 5px;
}

.table {
    flex: 1;
    overflow-y: auto;
}

.tag_list {
    padding: 24px;
}

.tag_lists {
    height: 600px;
    overflow-y: scroll;
}

.menu {
    width: 200px;
}

.pass_select {
    display: flex;
    justify-content: center;
    align-items: center;
}
</style>