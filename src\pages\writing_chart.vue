<template>
  <el-container class="container">
    <el-header class="title">
      <img src="@/assets/svg/writing_chart.svg" alt="">
      <h1>Coolmed case</h1>
    </el-header>
    <el-container class="content">
      <el-aside class="left" width="240px">
        <div class="menu">
          <el-input style="display: none;"></el-input>
          <el-select v-model="curRoom" placeholder="请选择科室" style="width: 100%" @change="changeRoom">
            <el-option v-for="item in roomList" :key="item.id" :label="item.item_name" :value="item.item_no" />
          </el-select>
          <el-select v-show="curRoom" v-model="curDoctor" placeholder="请选择医生" style="width: 100%" @change="changeDoctor"
            clearable>
            <el-option v-for="item in doctorList" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
          <el-input v-model="patientName" style="width: 100%" placeholder="搜索患者姓名">
            <template #prefix>
              <el-icon class="el-input__icon">
                <search />
              </el-icon>
            </template>
          </el-input>
        </div>
        <div class="list">
          <div class="list-title">患者列表</div>
          <el-scrollbar class="patient-list" v-loading="patientListLoad">
            <el-menu style="border: none !important;" :default-active="activeIndex">
              <el-menu-item v-for="patient in filtertientList" @click="selectUser(patient)" :key="patient.pid"
                :index="patient.pid">
                <span>{{ patient.pat_name + '(主治医生：' + patient.doctor_name + ')' }}</span>
              </el-menu-item>
            </el-menu>
          </el-scrollbar>
        </div>
      </el-aside>

      <el-main class="right">
        <div class="top_menu">
          <span>{{ rightTitle }}</span>
          <span><el-icon>
              <Refresh />
            </el-icon>刷新</span>
        </div>
        <el-scrollbar>
          <div class="charts-content">
            <!-- 病历书写情况 -->
            <div class="chart-item" v-show="!activeIndex">
              <div class="top">
                <h4>病历书写情况</h4>
                <div>
                  <el-date-picker @change="getWriteList" v-model="writeTime" type="datetimerange" :shortcuts="shortcuts"
                    start-placeholder="开始时间" end-placeholder="结束时间" format="YYYY-MM-DD HH:mm:ss"
                    date-format="YYYY/MM/DD ddd" time-format="A hh:mm:ss" />
                </div>
              </div>
              <div class="chart-body">
                <div class="select-box">
                  <div v-for="item, index in writeList" :key="index" class="bubble-box"
                    :class="{ active: curSelect === index }" @click="changeSelect(index)">
                    <span>{{ item.name }}</span>
                    <p>{{ item.value }}</p>
                  </div>
                </div>
                <div class="charts">
                  <div v-show="!curDoctor" class="department"></div>
                  <div class="caseType"></div>
                </div>
              </div>
            </div>
            <!-- AI病历占比及内容修改率变化趋势-->
            <div class="chart-item" v-show="!activeIndex">
              <div class="top">
                <h4>AI病历占比及内容修改率变化趋势</h4>
                <div class="other-menu">
                  <div class="complete-menu" @click="(e) => changeTrendStatus(e)">
                    <span :class="{ active: curTrendStatus === 1 }">AI病历占比</span>
                    <span :class="{ active: curTrendStatus === 0 }">内容修改率</span>
                  </div>
                </div>
                <div>
                  <el-date-picker @change="getAIRate" v-model="aiTime" type="datetimerange" start-placeholder="开始时间" :shortcuts="shortcuts"
                    end-placeholder="结束时间" format="YYYY-MM-DD HH:mm:ss" date-format="YYYY/MM/DD ddd"
                    time-format="A hh:mm:ss" />
                </div>
              </div>
              <div class="chart-body">
                <div class="charts1">
                  <div class="ai_trend"></div>
                </div>
              </div>
            </div>
            <!-- 医生使用情况 -->
            <div class="chart-item" v-show="!curRoom || !activeIndex">
              <div class="top">
                <h4>医生使用情况</h4>
                <div>
                  <el-date-picker @change="getDoctorUse" v-model="useTime" type="datetimerange" start-placeholder="开始时间" :shortcuts="shortcuts"
                    end-placeholder="结束时间" format="YYYY-MM-DD HH:mm:ss" date-format="YYYY/MM/DD ddd"
                    time-format="A hh:mm:ss" />
                </div>
              </div>
              <div class="chart-body">
                <div class="use-data">{{ `共${doctorUseChartData.doctorNum}位医生使用，累计上传${doctorUseChartData.total}份病历` }}
                </div>
                <div class="charts1">
                  <div class="doctor_use_chart"></div>
                </div>
              </div>
            </div>
            <!-- 病历书写趋势 -->
            <div class="chart-item" v-show="!activeIndex">
              <div class="top">
                <h4>病历书写趋势</h4>
                <div>
                  <el-date-picker @change="getWritingTrends" v-model="trendsTime" type="datetimerange" :shortcuts="shortcuts"
                    start-placeholder="开始时间" end-placeholder="结束时间" format="YYYY-MM-DD HH:mm:ss"
                    date-format="YYYY/MM/DD ddd" time-format="A hh:mm:ss" />
                </div>
              </div>
              <div class="chart-body">
                <div class="charts1">
                  <div class="writing_trends"></div>
                </div>
              </div>
            </div>
            <!-- 任务处理时间分布 -->
            <div class="chart-item" v-show="!activeIndex">
              <div class="top">
                <h4>任务处理时间分布</h4>
                <div>
                  <el-date-picker @change="getTimeDistribution" v-model="timeDistributionTime" type="datetimerange" :shortcuts="shortcuts"
                    start-placeholder="开始时间" end-placeholder="结束时间" format="YYYY-MM-DD HH:mm:ss"
                    date-format="YYYY/MM/DD ddd" time-format="A hh:mm:ss" />
                </div>
              </div>
              <div class="chart-body">
                <div class="charts1">
                  <div class="time_distribution"></div>
                </div>
              </div>
            </div>
            <!-- 病历列表 -->
            <div class="chart-item" v-show="curRoom">
              <div class="top">
                <h4>病历列表</h4>
                <div class="other-menu">
                  <div class="complete-menu" @click="(e) => changeStatus(e)">
                    <span :class="{ active: curStatus === 1 }">已完成</span>
                    <span :class="{ active: curStatus === 0 }">未完成</span>
                  </div>
                </div>
              </div>
              <div class="chart-body">
                <div class="list-menu">
                  <el-select v-show="curRoom" v-model="curDoctor" placeholder="请选择医生" style="width: 240px"
                    @change="getDataAgain" clearable>
                    <el-option v-for="item in doctorList" :key="item.id" :label="item.name" :value="item.id" />
                  </el-select>
                  <el-input v-model="curPatientName" style="width: 240px" placeholder="搜索患者姓名" clearable
                    @change="getDataAgain" />
                  <el-input v-model="curAdvice" style="width: 240px" placeholder="搜索关联医嘱" clearable
                    @change="getDataAgain" />
                  <el-input v-model="curCheck" style="width: 240px" placeholder="搜索关联检验检查" clearable
                    @change="getDataAgain" />
                  <el-date-picker @change="getDataAgain" v-model="tableTime" type="datetimerange" :shortcuts="shortcuts"
                    start-placeholder="开始时间" end-placeholder="结束时间" format="YYYY-MM-DD HH:mm:ss"
                    date-format="YYYY/MM/DD ddd" time-format="A hh:mm:ss" />
                </div>

                <div class="tables">
                  <el-table class="table" :data="tableData" border style="width: 100%" max-height="600">
                    <el-table-column width="120" prop="department" label="科室" />
                    <el-table-column width="90" prop="doctor_id" label="所属医生">
                      <template #default="scope">
                        <span>{{scope.row.doctor_id ? doctorList.find(item => item.id === scope.row.doctor_id)?.name :
                          '未分配'}}</span>
                      </template>
                    </el-table-column>
                    <el-table-column width="90" prop="pat_name" label="患者姓名" />
                    <el-table-column width="90" prop="inpno" label="住院号" />
                    <el-table-column width="120" prop="state" label="病历类型" sortable />
                    <el-table-column width="100" prop="update_time" label="病历时间" />
                    <el-table-column width="100" prop="create_time" label="创建时间" />
                    <el-table-column prop="create_reason" label="创建原因" />
                    <el-table-column width="110" prop="real_status" label="病历状态" sortable>
                      <template #default="scope">
                        <span :style="{ color: scope.row.real_status === '待上传' ? '#ff7d7d' : '#b36dff' }">{{
                          scope.row.real_status
                        }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column prop="medicalData" label="医疗数据">
                      <template #default="scope">
                        <p>{{ scope.row.jy_content }}</p>
                        <p>{{ scope.row.jc_content }}</p>
                      </template>
                    </el-table-column>
                    <el-table-column  width="60" label="操作">
                      <template #default="scope">
                        <el-link type="primary" @click="goToDetail(scope.row)">详情</el-link>
                      </template>
                    </el-table-column>
                  </el-table>
                  <Pagination style=" display: flex; justify-content: flex-end;" :currentPage="currentPage"
                    :pageSize="pageSize" :total="total" :pageSizes="[20, 50, 100]" @current-change="handleCurrentChange"
                    @size-change="handleSizeChange" />
                </div>
              </div>
            </div>
          </div>
        </el-scrollbar>
      </el-main>
    </el-container>
  </el-container>
  <el-dialog v-model="dialogVisible" width="50%" :close-on-click-modal="false">
    <el-tabs v-model="dialogTab">
      <el-scrollbar style="height: 600px;">
        <el-tab-pane label="任务详情" name="content">
          <div>
            <div style="margin-bottom: 24px;">
              <div style="font-weight:bold;margin-bottom:8px;">医嘱内容</div>
              <p>{{ dialogRow.yz_content }}</p>
            </div>
            <div style="margin-bottom: 24px;">
              <div style="font-weight:bold;margin-bottom:8px;">检验报告</div>
              <p>{{ dialogRow.jy_content }}</p>
            </div>
            <div style="margin-bottom: 24px;">
              <div style="font-weight:bold;margin-bottom:8px;">检查报告</div>
              <p>{{ dialogRow.jc_content }}</p>
            </div>
            <div>
              <div style="font-weight:bold;margin-bottom:8px;">分析结果</div>
              <p>{{ dialogRow.create_reason }}</p>
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane label="病历内容" name="detail">
          <div v-html="formatDocument(dialogRow.document)"></div>
        </el-tab-pane>
      </el-scrollbar>
    </el-tabs>
  </el-dialog>
</template>

<script setup>
import { computed, ref, onMounted, watch, watchEffect } from 'vue'
import * as echarts from 'echarts'
import Pagination from '@/components/Pagination.vue';
import dayjs from 'dayjs'
import { baseUrl } from '../utils/baseUrl';


const api1 = baseUrl.h_test_zhixuee
const api = baseUrl.url10
onMounted(() => {
  getRoomList()
  getWriteList()
  getDoctorUse()
  getWritingTrends()
  getTimeDistribution()
  getAIRate()
})


// -------------------左侧功能----------------------------
const curRoom = ref(0)
const roomList = ref([{ item_name: '全部科室', id: 0, item_no: 0 }])
const rightTitle = ref('全院')
const getRoomList = () => {
  fetch(api1 + `/v1/api/statistics/department_list`, {
    method: 'GET',
    headers: {
      'x-api-key': 'API655c18174462a06ede7ffbfa5aca4270c645b9b78ca30817e3a1bd496f34e04b',
      'Authorization': 'Bearer 6787c1fa54fdf2.403222624tH0FXJlIo'
    },
  })
    .then(response => response.json())
    .then(res => {
      roomList.value = roomList.value.concat(res.data)
    })
    .catch(error => {
    });
}
const getPatientList = () => {
  patientListLoad.value = true
  let params = {
    dept_id: curRoom.value,
    doctor_id: curDoctor.value,
  }
  const filteredParams = Object.entries(params).filter(([key, value]) => value !== '' && value !== undefined && value !== null);
  const queryString = new URLSearchParams(filteredParams).toString();
  fetch(api1 + `/v1/api/statistics/patient_list?${queryString}`, {
    method: 'GET',
    headers: {
      'x-api-key': 'API655c18174462a06ede7ffbfa5aca4270c645b9b78ca30817e3a1bd496f34e04b',
      'Authorization': 'Bearer 6787c1fa54fdf2.403222624tH0FXJlIo'
    },
  })
    .then(response => response.json())
    .then(res => {
      patientList.value = res.data
      patientListLoad.value = false
    })
    .catch(error => {
      patientListLoad.value = false

    });
}


const changeRoom = () => {
  curDoctor.value = null
  curPatientName.value = ''
  activeIndex.value = null

  getWritingTrends()
  getTimeDistribution()
  getPatientList()

  getWriteList()
  if (curRoom.value === 0) {
    patientList.value = []
    tableData.value = []
  } else {
    doctorList.value = roomList.value.find(item => item.item_no === curRoom.value).doctors
    getTaskList()
  }
}
const changeDoctor = () => {
  curPatientName.value = ''
  activeIndex.value = null
  getPatientList()
  getTaskList()
  getWriteList()
  getWritingTrends()
  getTimeDistribution()
}

const activeIndex = ref(null)
const selectUser = (item) => {
  activeIndex.value = item.pid
  curPatientName.value = item.pat_name
  getTaskList()
}
const getTaskList = () => {
  // 处理时间筛选
  let start_time = ''
  let end_time = ''
  if (Array.isArray(tableTime.value) && tableTime.value.length === 2) {
    start_time = dayjs(tableTime.value[0]).format('YYYY-MM-DD HH:mm:ss')
    end_time = dayjs(tableTime.value[1]).format('YYYY-MM-DD HH:mm:ss')
  }
  let params = {
    dept_id: curRoom.value,
    doctor_id: curDoctor.value,
    pat_name: curPatientName.value,
    jy_keyword: curCheck.value,
    yz_keyword: curAdvice.value,
    table_tag: curStatus.value,
    start_time,
    end_time,
    page: currentPage.value,
    page_size: pageSize.value,
  }
  const filteredParams = Object.entries(params).filter(([key, value]) => value !== '' && value !== undefined && value !== null);
  const queryString = new URLSearchParams(filteredParams).toString();
  fetch(api1 + `/v1/api/statistics/task_list?${queryString}`, {
    method: 'GET',
    headers: {
      'x-api-key': 'API655c18174462a06ede7ffbfa5aca4270c645b9b78ca30817e3a1bd496f34e04b',
      'Authorization': 'Bearer 6787c1fa54fdf2.403222624tH0FXJlIo'
    },
  })
    .then(response => response.json())
    .then(res => {
      tableData.value = res.data.records
      total.value = res.data.page_info.total_count
    })
    .catch(error => {
    });

}
const getDataAgain = () => {
  if (!curPatientName.value) {
    activeIndex.value = null
  }
  // getPatientList()
  currentPage.value = 1
  getTaskList()
}
const curDoctor = ref()
const doctorList = ref([
  { name: '医生一', value: '1' },
  { name: '医生二', value: '2' },
])
const patientName = ref('')
const filtertientList = computed(() => {
  if (!patientName.value.trim()) {
    return patientList.value;
  }

  const searchTerm = patientName.value.trim().toLowerCase();
  return patientList.value.filter(patient =>
    patient.pat_name.toLowerCase().includes(searchTerm)
  );
})

const patientList = ref([])
const patientListLoad = ref(false)

// -----------------------右侧功能-----------------------------
// 病历书写情况
const shortcuts = [
  {
    text: '近一周',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
      return [start, end]
    },
  },
  {
    text: '近一个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
      return [start, end]
    },
  },
  {
    text: '近三个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
      return [start, end]
    },
  },
]
const curSelect = ref(0)
const writeTime = ref([])


const useTime = ref([])
const trendsTime = ref([])
const timeDistributionTime = ref([])
const aiTime = ref([])

const getAIRate = () => {
  if (curTrendStatus.value === 1) {
    getOccupancyRate()
  } else {
    getModificationRate()
  }
}

const getModificationRate = () => {
  // 处理时间筛选
  let start_time = ''
  let end_time = ''
  if (Array.isArray(aiTime.value) && aiTime.value.length === 2) {
    start_time = dayjs(aiTime.value[0]).format('YYYY-MM-DD HH:mm:ss')
    end_time = dayjs(aiTime.value[1]).format('YYYY-MM-DD HH:mm:ss')
  }
  let params = {
    start_time,
    end_time,
  }
  const filteredParams = Object.entries(params).filter(([key, value]) => value !== '' && value !== undefined && value !== null);
  const queryString = new URLSearchParams(filteredParams).toString();
  fetch(api1 + `/v1/api/statistics/ai_modification_rate?${queryString}`, {
    method: 'GET',
    headers: {
      'x-api-key': 'API655c18174462a06ede7ffbfa5aca4270c645b9b78ca30817e3a1bd496f34e04b',
      'Authorization': 'Bearer 6787c1fa54fdf2.403222624tH0FXJlIo'
    },
  })
    .then(response => response.json())
    .then(res => {
      console.log(res);
      aiTrendData.x = res.data.map(item => item.date)
      aiTrendData.y = res.data.map(item => item.modification_rate.replace('%', ''))
      initAiTrendChart()
    })
    .catch(error => {
    });
}
const getOccupancyRate = () => {
  // 处理时间筛选
  let start_time = ''
  let end_time = ''
  if (Array.isArray(aiTime.value) && aiTime.value.length === 2) {
    start_time = dayjs(aiTime.value[0]).format('YYYY-MM-DD HH:mm:ss')
    end_time = dayjs(aiTime.value[1]).format('YYYY-MM-DD HH:mm:ss')
  }
  let params = {
    start_time,
    end_time,
  }
  const filteredParams = Object.entries(params).filter(([key, value]) => value !== '' && value !== undefined && value !== null);
  const queryString = new URLSearchParams(filteredParams).toString();
  fetch(api1 + `/v1/api/statistics/ai_occupancy_rate?${queryString}`, {
    method: 'GET',
    headers: {
      'x-api-key': 'API655c18174462a06ede7ffbfa5aca4270c645b9b78ca30817e3a1bd496f34e04b',
      'Authorization': 'Bearer 6787c1fa54fdf2.403222624tH0FXJlIo'
    },
  })
    .then(response => response.json())
    .then(res => {
      aiTrendData.x = res.data.map(item => item.date)
      aiTrendData.y = res.data.map(item => item.occupancy_rate.replace('%', ''))
      initAiTrendChart()
    })
    .catch(error => {
    });
}
const changeSelect = (index) => {
  curSelect.value = index
  getWriteList()
}
const writeList = ref([
  { name: '累计创建病历数', value: 0, more: '1111111' },
  { name: '累计上传病历数', value: 0, more: '1111111' },
  // { name: 'AI病历占有率', value: '26.6%', more: '1111111' },
  // { name: 'AI病历修改率', value: '19.5%', more: '1111111' },
])
const getWriteList = () => {
  // 处理时间筛选
  let start_time = ''
  let end_time = ''
  if (Array.isArray(writeTime.value) && writeTime.value.length === 2) {
    start_time = dayjs(writeTime.value[0]).format('YYYY-MM-DD HH:mm:ss')
    end_time = dayjs(writeTime.value[1]).format('YYYY-MM-DD HH:mm:ss')
  }
  let params = {
    dept_id: curRoom.value,
    doctor_id: curDoctor.value,
    start_time,
    end_time,
  }
  const filteredParams = Object.entries(params).filter(([key, value]) => value !== '' && value !== undefined && value !== null);
  const queryString = new URLSearchParams(filteredParams).toString();

  fetch(api1 + `/v1/api/statistics/task_count?${queryString}`, {
    method: 'GET',
    headers: {
      'x-api-key': 'API655c18174462a06ede7ffbfa5aca4270c645b9b78ca30817e3a1bd496f34e04b',
      'Authorization': 'Bearer 6787c1fa54fdf2.403222624tH0FXJlIo'
    },
  })
    .then(response => response.json())
    .then(res => {
      const { create_count, upload_count, task_info, task_state_info, upload_info, upload_state_info } = res.data
      writeList.value[0].value = create_count
      writeList.value[1].value = upload_count
      if (curSelect.value === 0) {
        if (curRoom.value === 0) {
          departmentData.title = '科室对比'
          departmentData.x = task_info.map(item => item.dept_name)
        } else {
          departmentData.title = '医生对比'
          departmentData.x = task_info.map(item => item.doctor_name)
        }
        departmentData.y = task_info.map(item => item.count)
        caseTypeData.x = task_state_info.map(item => item.state)
        caseTypeData.y = task_state_info.map(item => item.count)
        initDepartmentChart()
        initCaseTypeChart()
      } else if (curSelect.value === 1) {
        if (curRoom.value === 0) {
          departmentData.title = '科室对比'
          departmentData.x = upload_info.map(item => item.dept_name)
        } else {
          departmentData.title = '医生对比'
          departmentData.x = upload_info.map(item => item.doctor_name)
        }
        departmentData.y = upload_info.map(item => item.count)
        caseTypeData.x = upload_state_info.map(item => item.state)
        caseTypeData.y = upload_state_info.map(item => item.count)
        initDepartmentChart()
        initCaseTypeChart()
      }
    })
    .catch(error => {
    });
}

const getDoctorUse = () => {
  // 处理时间筛选
  let start_time = ''
  let end_time = ''
  if (Array.isArray(useTime.value) && useTime.value.length === 2) {
    start_time = dayjs(useTime.value[0]).format('YYYY-MM-DD HH:mm:ss')
    end_time = dayjs(useTime.value[1]).format('YYYY-MM-DD HH:mm:ss')
  }
  let params = {
    start_time,
    end_time,
  }
  const filteredParams = Object.entries(params).filter(([key, value]) => value !== '' && value !== undefined && value !== null);
  const queryString = new URLSearchParams(filteredParams).toString();
  fetch(api1 + `/v1/api/statistics/doctor_usage?${queryString}`, {
    method: 'GET',
    headers: {
      'x-api-key': 'API655c18174462a06ede7ffbfa5aca4270c645b9b78ca30817e3a1bd496f34e04b',
      'Authorization': 'Bearer 6787c1fa54fdf2.403222624tH0FXJlIo'
    },
  })
    .then(response => response.json())
    .then(res => {
      const { doctor_usage } = res.data
      doctorUseChartData.x = doctor_usage.map(item => item.doctor_name)
      doctorUseChartData.y = doctor_usage.map(item => {
        doctorUseChartData.total += item.count
        return item.count
      })
      doctorUseChartData.doctorNum = doctorUseChartData.x.length
      initDoctorUseChart()
    })
    .catch(error => {
    });
}

const getWritingTrends = () => {
  // 处理时间筛选
  let start_time = ''
  let end_time = ''
  if (Array.isArray(trendsTime.value) && trendsTime.value.length === 2) {
    start_time = dayjs(trendsTime.value[0]).format('YYYY-MM-DD HH:mm:ss')
    end_time = dayjs(trendsTime.value[1]).format('YYYY-MM-DD HH:mm:ss')
  }
  let params = {
    dept_id: curRoom.value,
    doctor_id: curDoctor.value,
    start_time,
    end_time,
  }
  const filteredParams = Object.entries(params).filter(([key, value]) => value !== '' && value !== undefined && value !== null);
  const queryString = new URLSearchParams(filteredParams).toString();
  fetch(api1 + `/v1/api/statistics/task_trends?${queryString}`, {
    method: 'GET',
    headers: {
      'x-api-key': 'API655c18174462a06ede7ffbfa5aca4270c645b9b78ca30817e3a1bd496f34e04b',
      'Authorization': 'Bearer 6787c1fa54fdf2.403222624tH0FXJlIo'
    },
  })
    .then(response => response.json())
    .then(res => {
      const { upload_info, write_info } = res.data
      writingTrendsData.x = write_info.map(item => item.create_time_day)
      writingTrendsData.task = write_info.map(item => item.count)
      writingTrendsData.upload = write_info.map(item => 0)
      writingTrendsData.x.forEach((time, index) => {
        upload_info.forEach(item => {
          if (time.includes(item.create_time_day)) {
            writingTrendsData.upload[index] += item.count
          }
        })
      })
      initWritingTrendsChart()
    })
}

const getTimeDistribution = () => {
  // 处理时间筛选
  let start_time = ''
  let end_time = ''
  if (Array.isArray(timeDistributionTime.value) && timeDistributionTime.value.length === 2) {
    start_time = dayjs(timeDistributionTime.value[0]).format('YYYY-MM-DD HH:mm:ss')
    end_time = dayjs(timeDistributionTime.value[1]).format('YYYY-MM-DD HH:mm:ss')
  }
  let params = {
    dept_id: curRoom.value,
    doctor_id: curDoctor.value,
    start_time,
    end_time,
  }
  const filteredParams = Object.entries(params).filter(([key, value]) => value !== '' && value !== undefined && value !== null);
  const queryString = new URLSearchParams(filteredParams).toString();
  fetch(api1 + `/v1/api/statistics/task_time_distribution?${queryString}`, {
    method: 'GET',
    headers: {
      'x-api-key': 'API655c18174462a06ede7ffbfa5aca4270c645b9b78ca30817e3a1bd496f34e04b',
      'Authorization': 'Bearer 6787c1fa54fdf2.403222624tH0FXJlIo'
    },
  })
    .then(response => response.json())
    .then(res => {
      const { upload_time_distribution, write_time_distribution } = res.data
      for (let i = 0; i < 24; i++) {
        timeDistributionData.x[i] = `${i}-${i + 1}点`
        timeDistributionData.task[i] = 0
        timeDistributionData.upload[i] = 0
      }

      timeDistributionData.x.forEach((time, index) => {

        write_time_distribution.forEach(item => {
          item.create_time_hour = item.create_time_hour.replace('0', '')
          if (time.includes(item.create_time_hour)) {
            timeDistributionData.task[index] += item.count
          }
        })
        upload_time_distribution.forEach(item => {
          item.create_time_hour = item.create_time_hour.replace('0', '')
          if (time.includes(item.create_time_hour)) {
            timeDistributionData.upload[index] += item.count
          }
        })
      })
      initTimeDistributionChart()
    })
    .catch(error => {
    });
}
const departmentData = {
  title: '科室对比',
  x: [],
  y: []
}
const initDepartmentChart = () => {
  const dom = document.querySelector('.department')
  const isEmpty = departmentData.x.length === 0
  let fontSize = departmentData.x.length > 10 ? 12 : 12
  let singleTextLength = departmentData.x.length > 10 ? 4 : 4
  if (dom) {
    let chart = null
    let chartInstance = echarts.getInstanceByDom(dom);
    if (!chartInstance) {
      chart = echarts.init(dom)
    }else {
      chart = chartInstance
    }
    
    chart.setOption({
      title: { text: departmentData.title, left: 10, top: 0, textStyle: { fontSize: 14, fontWeight: 400 } },
      tooltip: { trigger: 'axis' }, // 添加这一行
      grid: { left: 40, right: 20, top: 40, bottom: 40 },
      xAxis: {
        type: 'category',
        data: departmentData.x,
        axisLabel: {
          interval: 0,
          fontSize,
          formatter: function (value) {
            // 每行最多显示6个字，超出换行
            return value.length > singleTextLength
              ? value.slice(0, singleTextLength) + '\n' + value.slice(singleTextLength)
              : value
          }
        }
      },
      yAxis: { type: 'value' },
      series: [{
        data: departmentData.y,
        type: 'bar',
        barWidth: 30,
        itemStyle: { color: '#409EFF' }
      }],
      graphic: isEmpty ? [{
        type: 'text',
        left: 'center',
        top: 'middle',
        style: {
          text: '暂无数据',
          fontSize: 20,
          fill: '#999'
        }
      }] : []
    }, { replaceMerge: ['graphic'] })
    window.addEventListener('resize', () => {
      chart.resize()
    })
  }
}
const caseTypeData = {
  title: '病历类型分布',
  x: [],
  y: []
}
const initCaseTypeChart = () => {
  const dom = document.querySelector('.caseType')
  const isEmpty = caseTypeData.x.length === 0
  let fontSize = caseTypeData.x.length > 10 ? 8 : 12
  let singleTextLength = caseTypeData.x.length > 10 ? 3 : 5
  if (dom) {
    let chart = null
    let chartInstance = echarts.getInstanceByDom(dom);
    if (!chartInstance) {
    }else {
      chartInstance.dispose()
      // chart = chartInstance
    }
    chart = echarts.init(dom)
    chart.setOption({
      title: { text: caseTypeData.title, left: 10, top: 0, textStyle: { fontSize: 14, fontWeight: 400 } },
      tooltip: { trigger: 'axis' }, // 添加这一行
      grid: { left: 40, right: 20, top: 40, bottom: 40 },
      xAxis: {
        type: 'category', data: caseTypeData.x,
        axisLabel: {
          interval: 0,
          fontSize, // 设置x轴文字更小
          formatter: function (value) {
            // 每行最多显示4个字，分四行
            if (value.length > singleTextLength * 3) {
              return value.slice(0, singleTextLength) + '\n' + value.slice(singleTextLength, singleTextLength * 2) + '\n' + value.slice(singleTextLength * 2, singleTextLength * 3) + '\n' + value.slice(9)
            } else if (value.length > singleTextLength * 2) {
              return value.slice(0, singleTextLength) + '\n' + value.slice(singleTextLength, singleTextLength * 2) + '\n' + value.slice(singleTextLength * 2)
            } else if (value.length > singleTextLength) {
              return value.slice(0, singleTextLength) + '\n' + value.slice(singleTextLength)
            } else {
              return value
            }
          }
        }
      },
      yAxis: { type: 'value' },
      series: [{
        data: caseTypeData.y,
        type: 'bar',
        barWidth: 30,
        itemStyle: { color: '#409EFF' }
      }],
      graphic: isEmpty ? [{
        type: 'text',
        left: 'center',
        top: 'middle',
        style: {
          text: '暂无数据',
          fontSize: 20,
          fill: '#999'
        }
      }] : []
    }, { replaceMerge: ['graphic'] })
    console.log(chart);
    
    window.addEventListener('resize', () => {
      chart.resize()
    })
  }
}
// 示例数据
const aiTrendData = {
  x: [],
  y: []
}
const initAiTrendChart = () => {
  const dom = document.querySelector('.ai_trend')
  const isEmpty = aiTrendData.x.length === 0
  if (dom) {
    let chart = null
    let chartInstance = echarts.getInstanceByDom(dom);
    if (!chartInstance) {
      chart = echarts.init(dom)
    }else {
      chart = chartInstance
    }
    
    chart.setOption({
      tooltip: {
        trigger: 'axis',
        formatter: function (params) {
          const item = params[0]
          return `${item.axisValue}<br/>AI病历占比: ${item.data}%`
        }
      },
      grid: { left: 40, right: 20, top: 20, bottom: 40 },
      xAxis: {
        type: 'category',
        data: aiTrendData.x, // 例：['3-1','3-3',...]
        axisLabel: { fontSize: 12 }
      },
      yAxis: {
        type: 'value',
        min: 0,
        max: 40,
        axisLabel: {
          formatter: '{value}%'
        }
      },
      series: [{
        name: 'AI病历占比',
        type: 'line',
        data: aiTrendData.y, // 例：[10.2, 9.1, ...]
        smooth: true,
        symbol: 'none',
        lineStyle: { color: '#409EFF', width: 3 },
        areaStyle: { opacity: 0 }
      }],
      graphic: isEmpty ? [{
        type: 'text',
        left: 'center',
        top: 'middle',
        style: {
          text: '暂无数据',
          fontSize: 20,
          fill: '#999'
        }
      }] : []
    }, { replaceMerge: ['graphic'] })
    window.addEventListener('resize', () => {
      chart.resize()
    })
  }
}


const doctorUseChartData = {
  doctorNum: 0,
  total: 0,
  x: [],
  y: []
}
const initDoctorUseChart = () => {
  const dom = document.querySelector('.doctor_use_chart')
  const isEmpty = doctorUseChartData.x.length === 0

  if (dom) {
    let chart = null
    let chartInstance = echarts.getInstanceByDom(dom);
    if (!chartInstance) {
      chart = echarts.init(dom)
    }else {
      chart = chartInstance
    }
    chart.setOption({
      grid: { left: 40, right: 20, top: 20, bottom: 40 },
      tooltip: { trigger: 'axis' }, // 添加这一行
      xAxis: { type: 'category', data: doctorUseChartData.x, axisLabel: { interval: 0 } },
      yAxis: { type: 'value', min: 0, max: 100 },
      series: [{
        data: doctorUseChartData.y,
        type: 'bar',
        barWidth: 30,
        itemStyle: { color: '#409EFF' }
      }],
      graphic: isEmpty ? [{
        type: 'text',
        left: 'center',
        top: 'middle',
        style: {
          text: '暂无数据',
          fontSize: 20,
          fill: '#999'
        }
      }] : []
    }, { replaceMerge: ['graphic'] })
    window.addEventListener('resize', () => {
      chart.resize()
    })
  }
}
const writingTrendsData = {
  x: [],
  task: [],
  upload: []
}
const initWritingTrendsChart = () => {
  const dom = document.querySelector('.writing_trends')
  const isEmpty =
    writingTrendsData.x.length === 0 ||
    (writingTrendsData.task.every(v => v === 0) && writingTrendsData.upload.every(v => v === 0));

  if (dom) {
    let chart = null
    let chartInstance = echarts.getInstanceByDom(dom);
    if (!chartInstance) {
      chart = echarts.init(dom)
    }else {
      chart = chartInstance
    }
    chart.setOption({
      tooltip: {
        trigger: 'axis'
      },
      legend: {
        data: ['任务数', '上传数'],
        bottom: 0
      },
      grid: { left: 40, right: 20, top: 20, bottom: 40 },
      xAxis: {
        type: 'category',
        data: writingTrendsData.x
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: '任务数',
          type: 'line',
          data: writingTrendsData.task,
          smooth: false,
          symbol: 'circle',
          symbolSize: 8,
          lineStyle: { color: '#409EFF' },
          itemStyle: { color: '#409EFF' }
        },
        {
          name: '上传数',
          type: 'line',
          data: writingTrendsData.upload,
          smooth: false,
          symbol: 'circle',
          symbolSize: 8,
          lineStyle: { color: '#34c759' },
          itemStyle: { color: '#34c759' }
        }
      ],
      graphic: isEmpty ? [{
        type: 'text',
        left: 'center',
        top: 'middle',
        style: {
          text: '暂无数据',
          fontSize: 20,
          fill: '#999'
        }
      }] : []
    }, { replaceMerge: ['graphic'] })
    window.addEventListener('resize', () => {
      chart.resize()
    })
  }
}
const timeDistributionData = {
  x: [],
  task: [],
  upload: []
}
const initTimeDistributionChart = () => {
  const dom = document.querySelector('.time_distribution')
  const isEmpty = timeDistributionData.x.length === 0

  if (dom) {
    let chart = null
    let chartInstance = echarts.getInstanceByDom(dom);
    if (!chartInstance) {
      chart = echarts.init(dom)
    }else {
      chart = chartInstance
    }
    chart.setOption({
      tooltip: {
        trigger: 'axis'
      },
      legend: {
        data: ['书写', '上传'],
        bottom: 0
      },
      grid: { left: 40, right: 20, top: 20, bottom: 40 },
      xAxis: {
        type: 'category',
        data: timeDistributionData.x,
        axisLabel: {
          interval: 0 // 关键：每个时间段都显示
        }
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: '书写',
          type: 'line',
          data: timeDistributionData.task,
          smooth: false,
          symbol: 'circle',
          symbolSize: 8,
          lineStyle: { color: '#409EFF' },
          itemStyle: { color: '#409EFF' }
        },
        {
          name: '上传',
          type: 'line',
          data: timeDistributionData.upload,
          smooth: false,
          symbol: 'circle',
          symbolSize: 8,
          lineStyle: { color: '#34c759' },
          itemStyle: { color: '#34c759' }
        }
      ],
      graphic: isEmpty ? [{
        type: 'text',
        left: 'center',
        top: 'middle',
        style: {
          text: '暂无数据',
          fontSize: 20,
          fill: '#999'
        }
      }] : []
    }, { replaceMerge: ['graphic'] })
    window.addEventListener('resize', () => {
      chart.resize()
    })
  }
}

const tableData = ref([])
const filteredTableData = computed(() => {
  return tableData.value.filter((item) => {
    // 科室筛选
    const roomMatch = !curRoomName.value || curRoomName.value === '0' || item.room.includes(curRoomName.value) || item.room === curRoomName.value;
    // 医生姓名筛选
    const doctorMatch = !curDoctorName.value || item.doctor.includes(curDoctorName.value);
    // 患者姓名筛选
    const patientMatch = !curPatientName.value || item.pat_name.includes(curPatientName.value);
    // 关联医嘱筛选
    const adviceMatch = !curAdvice.value || item.yz_content.includes(curAdvice.value);
    // 关联检验检查筛选
    const checkMatch = !curCheck.value || item.medicalData.includes(curCheck.value);

    return roomMatch && doctorMatch && patientMatch && adviceMatch && checkMatch;
  });
});

const curTrendStatus = ref(1)
const changeTrendStatus = (e) => {
  if (e.target.innerText === 'AI病历占比') {
    curTrendStatus.value = 1
  } else {
    curTrendStatus.value = 0
  }
  getAIRate()
}
const curStatus = ref(1)
const changeStatus = (e) => {
  if (e.target.innerText === '已完成') {
    curStatus.value = 1
  } else {
    curStatus.value = 0
  }
  getTaskList()
}

const curRoomName = ref('')
const curDoctorName = ref('')
const curPatientName = ref('')
const curAdvice = ref('')
const curCheck = ref('')
const tableTime = ref([])

const currentPage = ref(1)
const pageSize = ref(20)
const total = ref()

const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  getTaskList()
}
const handleCurrentChange = (val) => {
  currentPage.value = val
  getTaskList()

}

const dialogVisible = ref(false)
const dialogTab = ref('content')
const dialogRow = ref({})
const goToDetail = (row) => {
  dialogRow.value = row
  dialogVisible.value = true
  dialogTab.value = 'content'
}

watchEffect(() => {
  if (curRoom.value === 0) {
    rightTitle.value = '全院使用情况统计'
    curDoctor.value = null
  } else {
    rightTitle.value = '科室名称使用情况统计'
  }
  if (curDoctor.value) {
    rightTitle.value = '医生名称使用情况统计'
  }
  if (activeIndex.value) {
    rightTitle.value = '患者名称病历书写情况'
  }
})
function formatDocument(doc) {
  if (!doc) return '';
  let obj = doc;
  if (typeof doc === 'string') {
    try {
      obj = JSON.parse(doc);
    } catch {
      return doc; // 不是JSON就原样返回
    }
  }
  // 优化缩进和换行，key加粗，内容自动换行，缩进更明显，移除空格，序号换行
  function pretty(obj, indent = 0) {
    if (typeof obj !== 'object' || obj === null) return String(obj);
    if (Array.isArray(obj)) {
      return obj.map(item => pretty(item, indent + 1)).join('<br>');
    }
    return Object.entries(obj).map(([k, v]) => {
      if (typeof v === 'object' && v !== null) {
        return `<div style="padding-left:${indent * 24}px; font-weight:bold;">${k}：</div>` + pretty(v, indent + 1);
      } else {
        // 移除所有空格
        let content = String(v).replace(/\s+/g, '');
        // 将（1）（2）等序号前加换行
        content = content.replace(/(\（\d+\）)/g, '<br>$1');
        // 将以符号或非汉字/字母结尾后跟1. 1、①、一、等序号前加换行（包括行首）
        content = content.replace(
          /([^\u4e00-\u9fa5a-zA-Z0-9]|^)((\d+[\.、])|[①②③④⑤⑥⑦⑧⑨⑩]|[一二三四五六七八九十][\.、])/g,
          '$1<br>$2'
        );
        // 将类似2025-06-19的日期前加换行（包括行首和符号后）
        content = content.replace(
          /([^\d]|^)(\d{4}-\d{2}-\d{2})/g,
          '$1<br>$2'
        );
        // 保留原有换行符，内容缩进更明显
        content = content
          .replace(/\\n/g, '<br>' + '&nbsp;'.repeat((indent + 1) * 4))
          .replace(/\n/g, '<br>' + '&nbsp;'.repeat((indent + 1) * 4));
        return `<div style="padding-left:${indent * 24}px;"><b>${k}：</b>${content}</div>`;
      }
    }).join('');
  }
  return pretty(obj);
}
</script>

<style scoped>
.container {
  width: 100%;
  height: 100%;


  .title {
    height: 80px;
    width: 100%;
    background-color: #fff;
    display: flex;
    align-items: center;
    padding: 10px;
    box-sizing: border-box;
    gap: 10px;
    border-bottom: 1px solid #f0f0f0;

    img {
      background-size: 50%;
    }
  }

  .content {
    height: calc(100% - 80px);
    display: flex;

    .left {
      background-color: #fff;
      /* padding: 20px; */

      .menu {
        display: flex;
        flex-direction: column;
        gap: 10px;
        padding: 20px;
        height: 160px;
      }

      .list {
        height: calc(100% - 200px);

        .list-title {
          font-size: 16px;
          font-weight: bold;
          padding: 0px 0px 10px 20px;
        }
      }
    }

    .right {
      overflow: hidden;
      padding-right: 0;

      .top_menu {
        line-height: 30px;
        height: 30px;
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;

        span {
          font-weight: bold;
        }

        span:nth-child(2) {
          font-size: 18px;
          color: #2d74ff;
          cursor: pointer;
          display: flex;
          align-items: center;
          gap: 5px;
        }
      }

      .charts-content {
        width: 100%;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        gap: 20px;
        /* padding: 20px 0; */

        .chart-item {
          width: 100%;
          /* height: 300px; */
          background-color: #fff;
          /* margin-bottom: 20px; */
          border-radius: 10px;
          padding: 20px;
          box-sizing: border-box;
          display: flex;
          flex-direction: column;

          .top {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 20px;

            .other-menu {
              flex: 1;

              .complete-menu {
                background-color: #f0f2f5;
                border-radius: 5px;
                display: flex;
                padding: 5px;
                float: right;

                span {
                  padding: 5px 10px;
                  border-radius: 5px;
                  text-align: center;

                }

                .active {
                  background-color: #2d74ff;
                  color: #fff;
                }
              }
            }

            .time-menu {
              width: 120px;
              height: 30px;
              background-color: rgb(249, 250, 251);
              border-radius: 5px;
              display: flex;
              padding: 5px;

              span {}
            }
          }

          .chart-body {
            .select-box {
              width: 100%;
              display: flex;
              gap: 20px;
              margin-top: 10px;

              .bubble-box {
                cursor: pointer;
                background: #f0f2f5;
                border: 1px solid #f0f2f5;
                border-radius: 10px;
                padding: 24px;
                position: relative;
                width: 240px;
                box-sizing: border-box;
                margin-bottom: 16px;

                span {
                  color: rgb(102, 102, 102);
                  padding: 0 0 10px 0;
                  position: relative;

                }

                p {
                  display: block;
                  font-size: 30px;
                  line-height: 30px;
                  margin-top: 5px;
                  font-weight: bold;
                }
              }

              .bubble-box.active::after {
                content: "";
                position: absolute;
                left: 50%;
                bottom: -10px;
                transform: translateX(-50%) rotateZ(45deg);
                transform-origin: center;
                width: 20px;
                height: 20px;
                background: #e6edfd;
                /* 三角形 */
                border-right: 1px solid #b5cfff;
                border-bottom: 1px solid #b5cfff;
                z-index: 1;
              }

              .bubble-box.active {
                background: #e6edfd;
                border: 1px solid #b5cfff;
              }

            }

            .use-data {
              width: 100%;
              padding: 10px;
              background-color: red;
              border: 1px solid rgb(151, 184, 255);
              background-color: rgb(230, 238, 255);
              box-sizing: border-box;
              border-radius: 5px;
              margin: 10px 0;

            }

            .list-menu {
              display: flex;
              gap: 5px;
              padding: 10px 0;
            }

            .charts {
              display: flex;
              gap: 20px;
              height: 240px;
              width: 100%;
              border-radius: 10px;
              border: 1px solid rgb(151, 184, 255);
              padding: 10px;
              box-sizing: border-box;
            }

            .department,
            .caseType {
              flex: 1;
              height: 100%;
              min-width: 0;
            }

            .charts1 {
              display: flex;
              gap: 20px;
              height: 240px;
              width: 100%;
              border-radius: 10px;
              padding: 10px;
              box-sizing: border-box;
              flex-direction: column;
            }

            .tables {
              width: 100%;
            }

            .doctor_use_chart,
            .writing_trends,
            .time_distribution,
            .ai_trend {
              flex: 1;
              height: 100%;
              min-width: 0;
            }
          }
        }

        .chart-item:last-child {
          margin-bottom: 20px;
        }
      }
    }
  }

}

/* ::v-deep .el-menu-item:hover {
  background-color: #e6f0ff !important;
  color: #2d74ff !important;
} */
</style>