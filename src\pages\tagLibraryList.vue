<template>
  <div class="tag-library-container">
    <!-- 顶部切换功能 -->
    <el-tabs class="tag-tabs" v-model="activeTab" @tab-click="handleTabClick" type="card">
      <el-tab-pane class="tag-tab" label="新标签" name="new-tags">
        <TagSection
          title="新标签"
          :showSearch="true"
          searchPlaceholder="搜索新标签"
          :searchValue="pendingTagSearch"
          @update:searchValue="val => pendingTagSearch = val"
        >
          <template #actions>
            <template v-if="!isMultiSelectMode">
              <el-button type="primary" :icon="Refresh" circle @click="refreshFetchPendingTags" />
              <el-button type="primary" @click="enableMultiSelect('classify')">批量归类</el-button>
              <el-button type="primary" :disabled="allPendingTagsLoading" @click="dealPengdingTags">处理标签{{ rate }}</el-button>
            </template>
            <template v-else>
              <el-button @click="cancelMultiSelect">取消</el-button>
              <el-button type="primary" @click="confirmMultiSelect(selectedTags, 'pending')" v-if="selectedTags.length > 0">
                {{ multiSelectMode === 'classify' ? '选择分类' : '确认弃用' }}({{ selectedTags.length }})
              </el-button>
            </template>
          </template>
        </TagSection>
        <TagsGrid :tags="pendingTags" :isMultiSelectMode="isMultiSelectMode" :activeTagId="activeTagId"
          :loading="pendingTagsLoading" :isTagSelected="isTagSelected" @toggleTagSelection="toggleTagSelection"
          @showTagActions="showTagActions">
          <template #actions="{ tag }">
            <button class="action-btn classify-btn" @click.stop="classifyTag(tag.id)">分类</button>
            <button class="action-btn abandon-btn" @click.stop="abandonTag(tag.id)">弃用</button>
          </template>
        </TagsGrid>
        <!-- 分页组件 -->
        <Pagination :current-page="pendingTagsPage" :page-size="pendingTagsPageSize" :total="pendingTagsTotal"
          @size-change="handlePendingTagsSizeChange" @current-change="handlePendingTagsPageChange" />
      </el-tab-pane>
      
      <el-tab-pane class="tag-tab" label="人工处理" name="manual-process">
        <TagSection
          title="人工处理"
          :showSearch="true"
          searchPlaceholder="搜索人工处理"
          :searchValue="newPendingTagSearch"
          @update:searchValue="val => newPendingTagSearch = val"
        >
          <template #actions>
            <template v-if="!newIsMultiSelectMode">
              <el-button type="primary" @click="newEnableMultiSelect('classify')">批量归类</el-button>
              <el-button type="danger" @click="newEnableMultiSelect('abandon')">批量弃用</el-button>
            </template>
            <template v-else>
              <el-button @click="newCancelMultiSelect">取消</el-button>
              <el-button type="primary" @click="confirmMultiSelect(newSelectedTags, 'newPending')" v-if="newSelectedTags.length > 0">
                {{ newMultiSelectMode === 'classify' ? '选择分类' : '确认弃用' }}({{ newSelectedTags.length }})
              </el-button>
            </template>
          </template>
        </TagSection>

        <TagsGrid :tags="newPendingTags" :isMultiSelectMode="newIsMultiSelectMode" :activeTagId="activeTagId"
          :loading="newPendingTagsLoading" :isTagSelected="newIsTagSelected" @toggleTagSelection="newToggleTagSelection"
          @showTagActions="showTagActions">
          <template #actions="{ tag }">
            <button class="action-btn classify-btn" @click.stop="newClassifyTag(tag.id)">分类</button>
            <button class="action-btn abandon-btn" @click.stop="newDeletePendingTag(tag.id, tag.content)">弃用</button>
          </template>
        </TagsGrid>

        <!-- 分页组件 -->
        <Pagination :current-page="newPendingTagsPage" :page-size="newPendingTagsPageSize" :total="newPendingTagsTotal"
          @size-change="newHandlePendingTagsSizeChange" @current-change="newHandlePendingTagsPageChange" />
      </el-tab-pane>
      
      <el-tab-pane class="tag-tab" label="过滤词" name="filter-words">
        <TagSection
          title="过滤词"
          :showSearch="true"
          searchPlaceholder="搜索过滤词"
          :searchValue="searchFrequentTag"
          @update:searchValue="val => searchFrequentTag = val"
        >
          <template #actions>
            <el-button type="primary" @click="toggleMatchingTagsList">筛选相同标签</el-button>
            <el-button type="primary" @click="openAddTagDialog('filter')">新增标签</el-button>
            <el-button type="warning" @click="showDeleteHistory">删除记录</el-button>
          </template>
        </TagSection>

        <TagsGrid :tags="filteredFrequentTags" @filter-keys="openFilterKeys" :isTagSelected="isTagSelected">
          <template #end="{ tag }">
            <el-icon class="delete-icon" @click="confirmRemoveFilterTag(tag.id, tag.content)">
              <Close />
            </el-icon>
          </template>
          <template #actions="{ tag }">
            <button class="action-btn classify-btn"
              @click.stop="confirmRestoreFrequentTag(tag.id, tag.content)">恢复</button>
            <button class="action-btn abandon-btn"
              @click.stop="confirmRemoveFrequentTag(tag.id, tag.content)">弃用</button>
          </template>
        </TagsGrid>
      </el-tab-pane>
      
      <el-tab-pane class="tag-tab" label="弃用标签" name="abandoned-tags">
        <TagSection
          title="弃用标签"
          :showSearch="true"
          searchPlaceholder="搜索弃用标签"
          :searchValue="searchAbandonedTag"
          @update:searchValue="val => searchAbandonedTag = val"
        >
          <template #actions>
            <el-button type="primary" @click="toggleFrequentTagsList">筛选相同标签</el-button>
            <el-button type="success" @click="downLoad">标签下载</el-button>
            <el-button type="primary" @click="openAddTagDialog('deprecated')">新增标签</el-button>
          </template>
        </TagSection>

        <TagsGrid :tags="filteredAbandonedTags" :activeTagId="activeTagId" :loading="pendingTagsLoading"
          :isTagSelected="isTagSelected" @showTagActions="showTagActions">
          <template #actions="{ tag }">
            <button class="action-btn classify-btn"
              @click.stop="confirmRestoreFrequentTag(tag.id, tag.content)">恢复</button>
            <button class="action-btn abandon-btn"
              @click.stop="confirmRemoveFrequentTag(tag.id, tag.content)">弃用</button>
          </template>
        </TagsGrid>

      </el-tab-pane>
    </el-tabs>
    

    <!-- 弹窗区 -->
    <!-- 新增过滤词弹窗/新增弃用标签弹窗 -->
    <el-dialog
      v-model="addTagDialogVisible"
      :title="addTagDialogType === 'filter' ? '新增过滤词' : '新增弃用标签'"
      width="30%"
      :before-close="() => addTagDialogVisible = false"
    >
      <div class="add-tag-dialog">
        <el-form>
          <el-form-item label="标签内容" required>
            <el-input v-model="addTagInput" placeholder="请输入标签内容" />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="addTagDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitAddTag">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 删除记录弹窗 -->
    <el-dialog v-model="deleteHistoryVisible" title="过滤词删除记录" width="40%"
      :before-close="() => deleteHistoryVisible = false">
      <div class="delete-history-dialog">
        <!-- 删除记录列表 -->
        <div class="delete-history-content">
          <div v-for="(tag, index) in filteredDeleteHistory" :key="index" class="history-item">
            <div class="history-date-line">
              <span class="history-date">{{ tag.update_time.split(' ')[0] }}</span>
              <span class="history-time">{{ tag.update_time.split(' ')[1] }}</span>
              <span class="history-status">删除了</span>
              <div class="history-content-row">
                <div class="tag-block">
                  <span>{{ tag.name }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 没有记录时的提示 -->
          <div v-if="filteredDeleteHistory.length === 0" class="no-history">
            <span>暂无删除记录</span>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 筛选相同弃用标签弹窗 -->
    <el-dialog v-model="abandonedTagsVisible" title="弃用标签" width="50%"
      :before-close="() => abandonedTagsVisible = false">
      <div class="abandoned-tags-dialog">
        <!-- 搜索框 -->
        <div class="search-bar">
          <el-input placeholder="搜索标签内容" v-model="abandonedTagsSearch" clearable></el-input>
        </div>

        <!-- 单双标签分组 -->
        <div class="abandoned-tags-content">
          <template v-for="(item, index) in filteredAbandonedTag" :key="index">
            <div class="abandoned-tags-date">{{ item.name + item.children.length + '个' }}
              <span class="select-all">全选
                <el-checkbox v-model="item.selected" :indeterminate="getAbandonedIndeterminate(item)"
                  style="margin-right: 10px;" @change="changeCheck(1, index, filteredAbandonedTag)" />
                <el-button type="danger" size="small" @click="batchDeleteTag(index)">批量删除</el-button>
              </span>
            </div>
            <div class="abandoned-tags-grid">
              <template v-for="(tag, index1) in item.children" :key="index1">
                <div class="tag-item">
                  <input type="checkbox" v-model="tag.selected" :value="tag.selected"
                    @change="changeCheck(2, index, filteredAbandonedTag)" />
                  <span>{{ tag.name }}</span>
                  <el-icon class="restore-icon" @click="delectTag(tag.name)">
                    <Close />
                  </el-icon>
                </div>
              </template>
            </div>
          </template>
        </div>
      </div>
    </el-dialog>

    <!-- 筛选相同过滤词弹窗 -->
    <el-dialog v-model="matchingTagsVisible" title="过滤词" width="50%" :before-close="() => matchingTagsVisible = false">
      <div class="matching-tags-dialog">
        <!-- 搜索框 -->
        <div class="search-bar">
          <el-input placeholder="搜索标签内容" v-model="matchingTagsSearch" clearable></el-input>
        </div>
        <span class="select-all">全选
          <el-checkbox v-model="matchingDialogSeleced" :indeterminate="matchingDialogIndeterminate"
            style="margin-right: 10px;" @change="changeMatchingCheckAll" />
          <el-button type="danger" size="small" @click="batchDeleteMatchingTag()">批量删除</el-button>
        </span>
        <!-- 单双标签分组 -->
        <div class="matching-tags-content" v-loading="matchingDialogLoading">
          <template v-for="(item, index) in filteredMatchingTag" :key="index">
            <div style="display: flex; justify-content: space-between; align-items: center;">
              <div style="display: flex;  align-items: center;">
                <el-checkbox v-model="item.selected" size="large" @change="changeMatchingCheck()" />
                <span class="matching-tags-date" @click="openFilterKeys(item.word)">{{ item.word }} </span>
              </div>
              <el-icon class="restore-icon" @click="delectMatchingTag(item.id)">
                <Close />
              </el-icon>
            </div>
            <div class="matching-tags-grid">
              <div>
                <h4>单标签</h4>
                <div class="matching-item">
                  <template v-for="(tag, index1) in item.tag" :key="index1">
                    <div class="tag-item">
                      <span>{{ tag }}</span>
                    </div>
                  </template>
                </div>
              </div>
              <div>
                <h4>多标签</h4>
                <div class="matching-item">
                  <template v-for="(tag, index1) in item.tags" :key="index1">
                    <div class="tag-item">
                      <span>{{ tag }}</span>
                    </div>
                  </template>
                </div>
              </div>
            </div>
          </template>
        </div>
      </div>
    </el-dialog>

    <!-- 新标签分类选择弹窗/人工处理分类选择弹窗 -->
    <el-dialog
      v-model="classifyDialogVisible"
      title="选择分类"
      width="40%"
      height="500px"
      :before-close="() => classifyDialogVisible = false"
    >
      <div class="category-search">
        <el-input v-model="searchClassifyCategory" placeholder="搜索分类名称" prefix-icon="Search" clearable></el-input>
      </div>
      <div class="classify-tree-container" v-loading="classifyLoad">
        <el-tree
          ref="categoryTreeRef"
          :data="categoryTreeData"
          :props="categoryTreeProps"
          node-key="id"
          :filter-node-method="filterCategoryNode"
          highlight-current
          @node-click="handleCategorySelect"
        >
          <template #default="{ node, data }">
            <span class="custom-tree-node">
              <span>{{ node.label }}</span>
              <span v-if="data.description" class="node-description">
                ({{ data.description }})
              </span>
            </span>
          </template>
        </el-tree>
      </div>
      <template #footer>
        <span class="dialog-footer" >
          <el-button @click="classifyDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleClassifyConfirm" :disabled="classifyLoad">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 弃用匹配词查询弹窗 -->
    <el-dialog v-model="filterKeyVisible" title="弃用匹配词查询" width="70%" :before-close="() => filterKeyVisible = false"
      v-loading="filterKeyLoading">
      <el-input placeholder="搜索关键词" v-model="filterKeySearch" clearable size="small"
        style="width: 200px; margin-right: auto;" @change="getFilterKeyData"></el-input>
      <el-button size="small" type="primary" style="float: right;" @click="downLoadFilterKeyData">下载弃用匹配词</el-button>
      <div class="filterKey-form">
        <ul>
          <li v-for="item, index in filterKeyData" :key="index">
            {{ item }}
            <el-icon class="restore-icon" style="cursor: pointer;" @click="addTag(item)">
              <Plus />
            </el-icon>
          </li>
        </ul>
      </div>
      <!-- 分页组件 -->

      <Pagination
        :current-page="filterKeyPage" :page-size="filterKeyPageSize" :total="filterKeyTotal"
        @size-change="handleFilterKeySizeChange" @current-change="handleFilterKeyPageChange"
      />
    </el-dialog>


    <!-- 处理标签弹窗 -->
    <DealTags
      ref="dealTagsRef"
      :tags="allPendingTags.slice(0, 10)"
      :apiUrl="c.py_api_url"
    />  
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { post } from '@/utils/request.js'
import { ElMessage, ElMessageBox, ElCheckbox } from 'element-plus';
import { Refresh, Close } from '@element-plus/icons-vue'

import c from "@/utils/config";
import * as XLSX from 'xlsx';
import TagSection from '@/components/TagSection.vue'
import TagsGrid from '@/components/TagsGrid.vue'
import Pagination from '@/components/Pagination.vue';
import DealTags from '@/components/DealTags.vue'
import { baseUrl } from '../utils/baseUrl';


const api_url = c.api_url;

// const api_url = baseUrl.url8

// ===================== 0. 切换功能相关 =====================
// 当前激活的标签页
const activeTab = ref('new-tags');

// 处理标签页切换
const handleTabClick = (tab) => {
  console.log('切换到标签页:', tab.props.name);
  // 可以根据需要在这里添加切换逻辑
};


// ===================== 1. 标签分类相关 =====================
// 从接口获取标签分类数据
const categories = ref([]);
// 获取标签分类树结构
const fetchCategoryTree = async () => {
  try {
    const result = await post(`${api_url}/tag/getCategoryTree`, JSON.stringify({}));

    console.log(result)

    if (result.error_code === 0 && result.data) {
      // 处理返回的数据，添加必要的属性
      const processedCategories = result.data.map(category => {
        // 处理主分类
        const categoryChildren = [];
        // 如果原始数据中有子分类，处理这些子分类
        if (category.children && category.children.length > 0) {
          category.children.forEach(child => {
            // 处理子分类
            categoryChildren.push({
              id: child.id,
              name: child.name,
              pid: child.pid,
              type: child.type || 1, // 默认类型
              typeName: child.type === 2 ? '替换标签' : '对比标签', // 根据类型设置名称
              tags: child.tags || [], // 如果没有tags属性，使用空数组
              description: child.description || ''
            });
          });
        }

        return {
          id: category.id,
          name: category.name,
          pid: category.pid || 0,
          description: category.description,
          count: categoryChildren.length,
          children: categoryChildren
        };
      });

      // 注意：我们在前端展示对比标签和替换标签时，直接在子分类下都显示两种标签类型的区域
      // 标签类型通过标签本身的type属性来区分，而非创建不同类型的子分类
      categories.value = processedCategories;


    } else {
      ElMessage.error('获取标签分类数据失败');
    }
  } catch (error) {
    console.error('获取标签分类数据异常:', error);
    ElMessage.error('获取标签分类数据异常');
  }
};
// 分类树属性定义
const categoryTreeProps = {
  label: 'name',
  children: 'children'
};
// 分类树数据，基于 categories.value 转换而来
const categoryTreeData = computed(() => {
  // 深拷贝分类数据，不包含标签数组
  return categories.value.map(category => {
    const categoryData = {
      id: category.id,
      name: category.name,
      pid: category.pid || 0,
      description: category.description || ''
    };

    // 处理子分类，但不包含标签数组
    if (category.children && category.children.length > 0) {
      categoryData.children = category.children.map(child => ({
        id: child.id,
        name: child.name,
        pid: child.pid,
        description: child.description || '',
        isLeaf: true // 标记为叶子节点
      }));
    }

    return categoryData;
  });
});
// 父分类选项，从 getCategoryTree 接口获取的数据中提取
const parentCategoryOptions = computed(() => {
  // 筛选可用的父分类选项
  // 在编辑模式下，需要排除当前编辑的分类及其子分类
  if (isEditMode.value && newCategory.value.id) {
    // 排除自身及其子分类，避免循环引用
    return categories.value
      .filter(category => category.id !== newCategory.value.id)
      .map(category => ({
        name: category.name,  // key 使用 name
        pid: category.id      // value 使用 pid
      }));
  }

  // 新增模式下返回全部分类
  return categories.value.map(category => ({
    name: category.name,  // key 使用 name
    pid: category.id      // value 使用 pid
  }));
});
// 新增分类弹窗显示状态
const newCategory = ref({
  id: null, // 编辑时使用的ID
  pid: 0, // 默认上级分类为无（pid=0）
  name: '',
  description: ''
});
// 是否处于编辑模式
const isEditMode = ref(false);
// 标签树节点过滤器
const filterCategoryNode = (value, data) => {
  if (!value) return true;
  return data.name.toLowerCase().includes(value.toLowerCase()) ||
    (data.description && data.description.toLowerCase().includes(value.toLowerCase()));
};
// 处理分类选择
const handleCategorySelect = (data) => {
  // 如果是可选分类，记录当前选中的分类 ID
  selectedCategoryId.value = data.id;
  console.log('选择分类:', data.name, 'ID:', data.id);
};

// ===================== 2. 标签数据获取与分页相关 =====================
// 待处理标签
const pendingTags = ref([]); // 待处理标签数据
const pendingTagsLoading = ref(false); // 加载状态
const pendingTagsPage = ref(1); // 当前页码
const pendingTagsPageSize = ref(100); // 每页记录数
const pendingTagsTotal = ref(0); // 总记录数
const pendingTagSearch = ref('');// 待处理标签搜索
// 获取待处理标签
const refreshFetchPendingTags = () => {
  pendingTagsPage.value = 1;
  fetchPendingTags();
}
const fetchPendingTags = async () => {
  if (pendingTagsLoading.value) return;

  pendingTagsLoading.value = true;
  try {
    const result = await post(`${api_url}/tag/getPendingNewTags`, JSON.stringify({
      page: pendingTagsPage.value,
      pageSize: pendingTagsPageSize.value,
      searchText: pendingTagSearch.value,
      type: "1",
      is_all: "2"
    }));
    if (result.error_code === 0 && result.data) {
      // 处理返回的数据，添加selected属性以支持多选功能
      const newTags = result.data.list.map(tag => {
        // 检查当前标签是否在选中数组中
        const isSelected = isMultiSelectMode.value && selectedTags.value.includes(tag.id);

        return {
          id: tag.id,
          content: tag.name, // 将name存入content以兼容现有代码
          name: tag.name,    // 保留原始属性
          description: tag.description || '',
          create_time: tag.create_time,
          selected: isSelected  // 根据选中数组设置状态
        };
      });

      // 设置数据和总数
      pendingTags.value = newTags;
      pendingTagsTotal.value = result.data.total || 0;

    } else {
      ElMessage.error('获取待处理标签数据失败');
    }
  } catch (error) {
    console.error('获取待处理标签异常:', error);
    ElMessage.error('获取待处理标签异常');
  } finally {
    pendingTagsLoading.value = false;
  }
};
// 处理每页记录数变化
const handlePendingTagsSizeChange = (size) => {
  pendingTagsPageSize.value = size;
  pendingTagsPage.value = 1; // 重置为第一页
  fetchPendingTags();
};
// 处理页码变化
const handlePendingTagsPageChange = (page) => {
  pendingTagsPage.value = page;
  fetchPendingTags();
};
// 重置待处理标签列表
const resetPendingTags = () => {
  pendingTags.value = [];
  pendingTagsPage.value = 1;
  pendingTagsTotal.value = 0;
  pendingTagSearch.value = '';
  fetchPendingTags();
};

// 人工处理标签
const newPendingTags = ref([]); // 待处理标签数据
const newPendingTagsLoading = ref(false); // 加载状态
const newPendingTagSearch = ref(''); // 搜索内容


const newPendingTagsPage = ref(1); // 当前页码
const newPendingTagsPageSize = ref(100); // 每页记录数
const newPendingTagsTotal = ref(0); // 总记录数
// 获取新待处理标签
const newFetchPendingTags = async () => {
  if (newPendingTagsLoading.value) return;

  newPendingTagsLoading.value = true;
  try {
    const result = await post(`${api_url}/tag/getPendingTempTags`, JSON.stringify({
      page: newPendingTagsPage.value,
      pageSize: newPendingTagsPageSize.value,
      searchText: newPendingTagSearch.value,
      type: "1",
      is_all: "2"
    }));
    if (result.error_code === 0 && result.data) {
      // 处理返回的数据，添加selected属性以支持多选功能
      const newTags = result.data.list.map(tag => {
        // 检查当前标签是否在选中数组中
        const isSelected = isMultiSelectMode.value && selectedTags.value.includes(tag.id);

        return {
          id: tag.id,
          content: tag.name, // 将name存入content以兼容现有代码
          name: tag.name,    // 保留原始属性
          description: tag.description || '',
          create_time: tag.create_time,
          selected: isSelected  // 根据选中数组设置状态
        };
      });

      // 设置数据和总数
      newPendingTags.value = newTags;
      newPendingTagsTotal.value = result.data.total || 0;
    } else {
      ElMessage.error('获取待处理标签数据失败');
    }
  } catch (error) {
    console.error('获取待处理标签异常:', error);
    ElMessage.error('获取待处理标签异常');
  } finally {
    newPendingTagsLoading.value = false;
  }
};
// 处理每页记录数变化
const newHandlePendingTagsSizeChange = (size) => {
  newPendingTagsPageSize.value = size;
  newPendingTagsPage.value = 1; // 重置为第一页
  newFetchPendingTags();
};

// 处理页码变化
const newHandlePendingTagsPageChange = (page) => {
  newPendingTagsPage.value = page;
  newFetchPendingTags();
};

// 弃用标签
const abandonedTags = ref([]);
// 获取弃用标签数据
const fetchAbandonedTags = async () => {
  try {
    const result = await post(`${api_url}/tag/getDiscardedTags`, JSON.stringify({}));

    if (result.error_code === 0) {
      // 将API返回的数据转换为需要的格式
      abandonedTags.value = result.data.map(item => ({
        id: item.id,
        content: item.name,
        description: item.description,
        date: item.create_time.split(' ')[0]  // 取日期部分作为分组依据
      }));
    } else {
      ElMessage.error(result.msg || '获取弃用标签失败');
    }
  } catch (error) {
    console.error('获取弃用标签异常:', error);
    ElMessage.error('获取弃用标签失败');
  }
};

// 弃用标签数据
const frequentTags = ref([]);
// 获取弃用标签数据
const fetchFrequentTags = async () => {
  try {
    const result = await post(`${api_url}/tag/getMatchingWords`, JSON.stringify({}));

    if (result.error_code === 0) {
      // 将接口数据映射为需要的格式
      frequentTags.value = result.data.map(item => ({
        id: item.id,
        content: item.name,
        description: item.description,
        create_time: item.create_time
      }));

    } else {
      ElMessage.error(result.msg || '获取弃用标签失败');
    }
  } catch (error) {
    console.error('获取弃用标签异常:', error);
    ElMessage.error('获取弃用标签失败');
  }
};


// ===================== 3. 多选与批量操作相关 =====================
// 多选相关状态
const isMultiSelectMode = ref(false); // 是否处于多选模式
const multiSelectMode = ref(''); // 多选模式类型：classify或abandon
const selectedTags = ref([]); // 已选中的标签 ID 数组
// 启用多选模式
const enableMultiSelect = (mode) => {

  isMultiSelectMode.value = true;
  multiSelectMode.value = mode;
  selectedTags.value = []; // 清空已选标签列表

  // 重置所有标签的选中状态
  pendingTags.value.forEach(tag => {
    tag.selected = false;
  });
};

// 取消多选模式
const cancelMultiSelect = () => {
  isMultiSelectMode.value = false;
  multiSelectMode.value = '';
  selectedTags.value = []; // 清空已选标签列表

  // 重置所有标签的选中状态
  pendingTags.value.forEach(tag => {
    tag.selected = false;
  });
};

// 切换标签选中状态
const toggleTagSelection = (tagId) => {
  console.log('toggleTagSelection', tagId);

  // 查找对应的标签
  const tag = pendingTags.value.find(t => t.id === tagId);
  if (tag) {
    // 设置新的状态 - 取反当前状态
    const newState = !tag.selected;
    tag.selected = newState;

    // 更新已选标签列表
    if (newState) {
      if (!selectedTags.value.includes(tagId)) {
        selectedTags.value.push(tagId);
      }
    } else {
      selectedTags.value = selectedTags.value.filter(id => id !== tagId);
    }
  }
};

// 检查标签是否被选中
const isTagSelected = (tagId) => {
  return pendingTags.value.find(t => t.id === tagId)?.selected || false;
};
// 确认多选操作
const confirmMultiSelect = (data, type) => {
  if (data.length === 0) {
    ElMessage.warning('请至少选择一个标签');
    return;
  }
  classifyDialogType.value = type;

  
  if (multiSelectMode.value === 'classify' || newMultiSelectMode.value === 'classify') {
    // 批量分类操作 - 打开分类选择对话框
    // 记录当前选中的标签ID数组
    currentBatchClassifyTagIds = [...data];
    selectedCategoryId.value = null; // 重置选中的分类

    // 重置搜索内容
    searchClassifyCategory.value = '';
    if (categoryTreeRef.value) {
      categoryTreeRef.value.filter('');
    }

    // 显示分类对话框
    classifyDialogVisible.value = true;
  } else if (multiSelectMode.value === 'abandon') {
    // 批量弃用操作
    batchAbandonTags();
  } else if (newMultiSelectMode.value === 'abandon') {
    // 批量弃用操作
    newBatchAbandonTags();
  }
};
// 存储当前批量分类的标签ID数组
let currentBatchClassifyTagIds = [];
// 批量弃用标签
const batchAbandonTags = async () => {
  deletePendingTag(selectedTags.value);
};

// 新待处理标签多选
const newIsMultiSelectMode = ref(false); // 是否处于多选模式
const newMultiSelectMode = ref(''); // 多选模式类型：classify或abandon
const newSelectedTags = ref([]); // 已选中的标签 ID 数组
// 启用多选模式
const newEnableMultiSelect = (mode) => {
  newIsMultiSelectMode.value = true;
  newMultiSelectMode.value = mode;
  newSelectedTags.value = []; // 清空已选标签列表

  // 重置所有标签的选中状态
  newPendingTags.value.forEach(tag => {
    tag.selected = false;
  });
};
// 取消多选模式
const newCancelMultiSelect = () => {
  newIsMultiSelectMode.value = false;
  newMultiSelectMode.value = '';
  newSelectedTags.value = []; // 清空已选标签列表

  // 重置所有标签的选中状态
  newPendingTags.value.forEach(tag => {
    tag.selected = false;
  });
};
// 切换标签选中状态
const newToggleTagSelection = (tagId) => {
  // 查找对应的标签
  const tag = newPendingTags.value.find(t => t.id === tagId);
  if (tag) {
    // 设置新的状态 - 取反当前状态
    const newState = !tag.selected;
    tag.selected = newState;

    // 更新已选标签列表
    if (newState) {
      if (!newSelectedTags.value.includes(tagId)) {
        newSelectedTags.value.push(tagId);
      }
    } else {
      newSelectedTags.value = newSelectedTags.value.filter(id => id !== tagId);
    }
  }
};
// 检查标签是否被选中
const newIsTagSelected = (tagId) => {
  return newPendingTags.value.find(t => t.id === tagId)?.selected || false;
};
// 批量弃用标签
const newBatchAbandonTags = async () => {
  newDeletePendingTag(newSelectedTags.value);
};


// ===================== 4. 弃用/过滤词相关 =====================
// 筛选弃用标签匹配词的计算属性
const filteredFrequentTags = computed(() => {
  if (!searchFrequentTag.value.trim()) {
    return frequentTags.value;
  }

  const searchTerm = searchFrequentTag.value.trim().toLowerCase();
  return frequentTags.value.filter(tag =>
    tag.content.toLowerCase().includes(searchTerm)
  );
});

// 筛选弃用标签的计算属性
const filteredAbandonedTags = computed(() => {
  if (!searchAbandonedTag.value.trim()) {
    return abandonedTags.value;
  }

  const searchTerm = searchAbandonedTag.value.trim().toLowerCase();
  return abandonedTags.value.filter(tag =>
    tag.content.toLowerCase().includes(searchTerm)
  );
});
// 确认恢复弃用标签
const confirmRestoreFrequentTag = (tagId, tagContent) => {
  ElMessageBox.confirm(
    `确定要恢复标签「${tagContent}」吗？`,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(() => {
      restoreFrequentTag(tagId);
    })
    .catch(() => {
      // 用户取消删除操作
    });
};
// 确认删除过滤词标签
const confirmRemoveFilterTag = (tagId, tagContent) => {
  ElMessageBox.confirm(
    `确定要删除标签「${tagContent}」吗？`,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(() => {
      removeFilterTag(tagId);
    })
    .catch(() => {
      // 用户取消删除操作
    });
};
// 确认删除弃用标签
const confirmRemoveFrequentTag = (tagId, tagContent) => {
  ElMessageBox.confirm(
    `确定要删除标签「${tagContent}」吗？`,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(() => {
      removeFrequentTag(tagId);
    })
    .catch(() => {
      // 用户取消删除操作
    });
};
// 执行恢复弃用标签
const restoreFrequentTag = async (tagId) => {
  try {
    const result = await post(`${api_url}/tag/removeDiscardedTag`, JSON.stringify({
      ids: tagId
    }));

    if (result.error_code === 0) {
      ElMessage.success('弃用标签恢复');
      // 刷新所有相关数据
      fetchFrequentTags();
      fetchPendingTags();
      fetchAbandonedTags();
    } else {
      ElMessage.error(result.msg || '恢复标签失败');
    }
  } catch (error) {
    console.error('恢复弃用标签异常:', error);
    ElMessage.error('恢复标签失败');
  }
};
// 执行删除过滤词标签
const removeFilterTag = async (tagId) => {
  try {
    const result = await post(`${api_url}/tag/deleteMatchingWord`, JSON.stringify({
      id: tagId
    }));

    if (result.error_code === 0) {
      ElMessage.success('弃用标签已删除');
      // 刷新所有相关数据
      fetchFrequentTags();
      fetchPendingTags();
      fetchAbandonedTags();
    } else {
      ElMessage.error(result.msg || '删除标签失败');
    }
  } catch (error) {
    console.error('删除弃用标签异常:', error);
    ElMessage.error('删除标签失败');
  }
};
// 执行删除弃用标签
const removeFrequentTag = async (tagId) => {
  try {
    const result = await post(`${api_url}/tag/delDeprecated`, JSON.stringify({
      ids: tagId
    }));

    if (result.error_code === 0) {
      ElMessage.success('弃用标签已删除');
      // 刷新所有相关数据
      fetchFrequentTags();
      fetchPendingTags();
      fetchAbandonedTags();
    } else {
      ElMessage.error(result.msg || '删除标签失败');
    }
  } catch (error) {
    console.error('删除弃用标签异常:', error);
    ElMessage.error('删除标签失败');
  }
};

// 弃用标签匹配词查询
// 打开查询弹窗
const filterKeyVisible = ref(false); // 弃用标签匹配词查询弹窗显示状态
const filterKeyData = ref([]); // 弃用标签匹配词查询弹窗数据
const filterKeyPage = ref(1); // 弃用标签匹配词查询弹窗当前页
const filterKeyPageSize = ref(100); // 弃用标签匹配词查询弹窗当前页
const filterKeyTotal = ref(0); // 弃用标签匹配词查询弹窗数据总数
const filterKeyLoading = ref(false); // 弃用标签匹配词查询弹窗加载状态
const filterKeyName = ref(''); // 弃用标签匹配词查询弹窗查询关键词
const filterKeySearch = ref(''); // 弃用标签匹配词查询弹窗目前搜索
const handleFilterKeySizeChange = (size) => {
  filterKeyPageSize.value = size;
  filterKeyPage.value = 1; // 切换每页条数时重置为第一页
  getFilterKeyData();
};
const handleFilterKeyPageChange = (page) => {
  filterKeyPage.value = page;
  getFilterKeyData();
};
const getFilterKeyData = () => {
  filterKeyLoading.value = true;
  let url = `${api_url}/tag/getFilterKeywords`;
  post(url, JSON.stringify({
    name: filterKeyName.value,
    page: filterKeyPage.value,
    pageSize: filterKeyPageSize.value,
    keyword: filterKeySearch.value
  })).then((res) => {
    filterKeyData.value = res.data.list
    filterKeyTotal.value = res.data.total
    filterKeyLoading.value = false
  }).catch(err => {
    filterKeyLoading.value = false
  })
}
const openFilterKeys = (name) => {
  filterKeyName.value = name
  filterKeyVisible.value = true;
  filterKeySearch.value = ''
  getFilterKeyData()
}
const addTag = (name) => {
  let url = `${api_url}/tag/createMatchingWord`;
  post(url, JSON.stringify({
    name
  })).then((res) => {
    ElMessage.success('新增成功')
    fetchFrequentTags()
    getFilterKeyData()
  }).catch(err => {
    ElMessage.success('新增失败')
  })
}
const downLoadFilterKeyData = () => {
  filterKeyLoading.value = true
  // if (!filterKeySearch.value) {
  //   ElMessage.error('请输入关键词')
  //   return
  // }
  let url = `${api_url}/tag/getAllSereachKeywords`;
  post(url, JSON.stringify({
    name: filterKeyName.value,
    keyword: filterKeySearch.value
  })).then((res) => {
    createXLSX(res.data, '匹配词')
    ElMessage.success('下载成功')
    filterKeyLoading.value = false
  }).catch(err => {
    ElMessage.error('下载失败')
    filterKeyLoading.value = false

  })
}


// ===================== 5. 弹窗与UI状态相关 =====================
// 分类树相关状态
const classifyDialogVisible = ref(false); // 分类选择弹窗显示状态
const searchClassifyCategory = ref(''); // 搜索分类内容
const categoryTreeRef = ref(null); // 分类树引用
const currentClassifyTagId = ref(null); // 当前要分类的标签 ID
const selectedCategoryId = ref(null); // 当前选中的分类 ID
// 批量新增相关状态
const batchTagsInput = ref(''); // 批量新增标签的输入内容
// 删除记录相关状态
const deleteHistoryVisible = ref(false); // 删除记录弹窗显示状态
const deleteHistorySearch = ref(''); // 删除记录搜索关键词
const deleteHistory = ref([]); // 删除记录数据
// 过滤后的删除记录
const filteredDeleteHistory = computed(() => {
  const searchTerm = deleteHistorySearch.value.toLowerCase().trim();
  if (!searchTerm) {
    return deleteHistory.value;
  }

  return deleteHistory.value.filter(item =>
    item.name.toLowerCase().includes(searchTerm)
  );
});
// 显示删除记录弹窗
const showDeleteHistory = async () => {
  // 清空搜索关键词
  deleteHistorySearch.value = '';

  // 获取删除记录数据
  await fetchDeleteHistory();

  // 显示弹窗
  deleteHistoryVisible.value = true;
};
// 获取删除记录数据
const fetchDeleteHistory = async () => {
  try {
    const result = await post(`${api_url}/tag/getDeletedMatchingWords`, JSON.stringify({}));

    if (result.error_code === 0) {
      deleteHistory.value = result.data || [];
    } else {
      ElMessage.error(result.msg || '获取删除记录失败');
    }
  } catch (error) {
    console.error('获取删除记录异常:', error);
    ElMessage.error('获取删除记录失败');
  }
};

const addTagDialogVisible = ref(false);
const addTagDialogType = ref('filter'); // 'filter' 或 'deprecated'
const addTagInput = ref('');

// 打开弹窗
function openAddTagDialog(type) {
  addTagDialogType.value = type;
  addTagInput.value = '';
  addTagDialogVisible.value = true;
}

// 提交
async function submitAddTag() {
  if (!addTagInput.value.trim()) {
    ElMessage.warning('请输入标签内容');
    return;
  }
  try {
    let result;
    if (addTagDialogType.value === 'filter') {
      result = await post(`${api_url}/tag/createMatchingWord`, JSON.stringify({ name: addTagInput.value }));
    } else {
      result = await post(`${api_url}/tag/addDeprecated`, JSON.stringify({ name: addTagInput.value }));
    }
    if (result.error_code === 0) {
      ElMessage.success(addTagDialogType.value === 'filter' ? '过滤词标签添加成功' : '弃用标签添加成功');
      addTagDialogVisible.value = false;
      fetchFrequentTags();
      fetchPendingTags();
      fetchAbandonedTags();
    } else {
      ElMessage.error(result.msg || '添加失败');
    }
  } catch (error) {
    ElMessage.error('添加失败');
  }
}


// 当前分类弹窗的类型：'pending' 或 'newPending'
const classifyDialogType = ref('pending'); // 或 'newPending'

// 打开弹窗时指定类型
function openClassifyDialog(type) {
  classifyDialogType.value = type;
  classifyDialogVisible.value = true;
  searchClassifyCategory.value = '';
  if (categoryTreeRef.value) {
    categoryTreeRef.value.filter('');
  }
}

const classifyLoad = ref(false)
// 统一的确认方法
function handleClassifyConfirm() {
  console.log(classifyDialogType.value);
  classifyLoad.value = true
  
  if (classifyDialogType.value === 'pending') {
    confirmClassify();
  } else if (classifyDialogType.value === 'newPending') {
    newConfirmClassify();
    
  }
  // 关闭弹窗在各自方法内已处理
}


// ===================== 6. 工具/通用方法 =====================

// 创建表格下载
const createXLSX = (data, name) => {
  let xlsxData = []
  data.forEach((item) => {
    xlsxData.push({ '内容': item })
  })

  // 创建工作簿
  const wb = XLSX.utils.book_new();
  // 将数据转换为工作表
  const ws = XLSX.utils.json_to_sheet(xlsxData);
  // 将工作表添加到工作簿
  XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');
  // 导出 Excel 文件
  XLSX.writeFile(wb, name + '.xlsx');
}


// 状态管理
const searchFrequentTag = ref(''); // 搜索弃用标签匹配此
const searchAbandonedTag = ref(''); // 搜索弃用标签
const activeTagId = ref(null); // 当前激活的标签 ID，用于显示操作按钮

// 监听搜索内容变化，过滤树节点
watch(searchClassifyCategory, (val) => {
  categoryTreeRef.value?.filter(val);
});


// 使用监听器监听pendingTagSearch的变化，触发后端搜索
watch(pendingTagSearch, (newVal, oldVal) => {
  // 重置页码，确保从第一页开始搜索
  pendingTagsPage.value = 1;
  // 使用节流函数调用后端搜索，减少频繁请求
  const debouncedSearch = setTimeout(() => {
    fetchPendingTags();
  }, 1000);

  return () => clearTimeout(debouncedSearch);
});
// 使用监听器监听pendingTagSearch的变化，触发后端搜索
watch(newPendingTagSearch, (newVal, oldVal) => {
  // 重置页码，确保从第一页开始搜索
  // 使用节流函数调用后端搜索，减少频繁请求
  const debouncedSearch = setTimeout(() => {
    newFetchPendingTags();
  }, 1000);

  return () => clearTimeout(debouncedSearch);
});

// 使用onMounted生命周期钩子在组件挂载时加载数据
onMounted(() => {
  fetchCategoryTree();
  fetchPendingTags();
  newFetchPendingTags();
  fetchFrequentTags(); // 获取弃用标签数据
  fetchAbandonedTags(); // 获取弃用标签数据
  getAllPendingNewTags()
});


// ===================== 处理标签按钮相关功能 =====================

// 通过ai处理待处理标签
const allPendingTags = ref([]); // 所有待处理标签数据
const allPendingTagsLoading = ref(false); // 加载状态
const rate = ref('')



// 处理标签按钮点击时调用
const dealTagsRef = ref(null)
function dealPengdingTags() {
  if (!allPendingTags.value.length) {
    ElMessage.error('没有待处理标签')
    return
  }
  dealTagsRef.value.open()
}



const getAllPendingNewTags = async () => {

  allPendingTagsLoading.value = true;
  try {
    const result = await post(`${api_url}/tag/getAllPendingNewTags`, JSON.stringify({}));
    if (result.error_code === 0 && result.data) {
      allPendingTagsLoading.value = false;
      allPendingTags.value = result.data
    } else {
      ElMessage.error('获取所以待处理标签数据失败');
      allPendingTagsLoading.value = false;
    }
  } catch (error) {
    console.error('获取所以待处理标签异常:', error);
    ElMessage.error('获取所以待处理标签异常');
    allPendingTagsLoading.value = false;
  }
}

// 删除待处理标签（单个或批量）
const deletePendingTag = async (tagIds) => {
  // 判断是单个标签还是多个标签
  const isBatch = Array.isArray(tagIds);
  const ids = isBatch ? tagIds : [tagIds];

  // 如果是单个标签，找到标签信息
  let tagContent = '';
  if (!isBatch) {
    const tagToAbandon = pendingTags.value.find(tag => tag.id === tagIds);
    if (tagToAbandon) {
      tagContent = tagToAbandon.content;
    }
  }

  // 确认提示信息
  const confirmMessage = isBatch
    ? `确定要弃用选中的 ${ids.length} 个标签吗？`
    : `确定要弃用标签「${tagContent}」吗？`;

  ElMessageBox.confirm(
    confirmMessage,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(async () => {
      try {
        // 调用弃用标签接口
        const result = await post(`${api_url}/tag/delPendingNewTags`, JSON.stringify({
          ids: ids
        }));

        if (result.error_code === 0) {
          // 成功提示
          const successMessage = isBatch
            ? `已成功弃用 ${ids.length} 个标签`
            : '标签已成功弃用';
          ElMessage.success(successMessage);
          selectedTags.value = [];
          // 刷新待处理标签数据
          resetPendingTags();
          // 刷新弃用标签数据
          fetchAbandonedTags();

          // 如果是批量操作，退出多选模式
          if (isBatch) {
            cancelMultiSelect();
          } else {
            // 关闭操作按钮
            activeTagId.value = null;
          }
        } else {
          const errorMessage = isBatch ? '批量弃用标签失败' : '弃用标签失败';
          ElMessage.error(result.msg || errorMessage);

          // 如果是单个操作，关闭操作按钮
          if (!isBatch) {
            activeTagId.value = null;
          }
        }
      } catch (error) {
        console.error('弃用标签异常:', error);
        const errorMessage = isBatch ? '批量弃用标签异常' : '弃用标签异常';
        ElMessage.error(errorMessage);

        // 如果是单个操作，关闭操作按钮
        if (!isBatch) {
          activeTagId.value = null;
        }
      }
    })
    .catch(() => {
      // 用户取消操作
    });
};
// 删除人工处理标签（单个或批量）
const newDeletePendingTag = async (tagIds) => {
  // 判断是单个标签还是多个标签
  const isBatch = Array.isArray(tagIds);
  const ids = isBatch ? tagIds : [tagIds];

  // 如果是单个标签，找到标签信息
  let tagContent = '';
  if (!isBatch) {
    const tagToAbandon = newPendingTags.value.find(tag => tag.id === tagIds);
    if (tagToAbandon) {
      tagContent = tagToAbandon.content;
    }
  }

  // 确认提示信息
  const confirmMessage = isBatch
    ? `确定要弃用选中的 ${ids.length} 个标签吗？`
    : `确定要弃用标签「${tagContent}」吗？`;

  ElMessageBox.confirm(
    confirmMessage,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(async () => {
      try {
        // 调用弃用标签接口
        const result = await post(`${api_url}/tag/delPendingTempTags`, JSON.stringify({
          ids: ids
        }));

        if (result.error_code === 0) {
          // 成功提示
          const successMessage = isBatch
            ? `已成功弃用 ${ids.length} 个标签`
            : '标签已成功弃用';
          ElMessage.success(successMessage);
          newSelectedTags.value = [];
          // 刷新待处理标签数据
          newFetchPendingTags();
          // 刷新弃用标签数据
          fetchAbandonedTags();

          // 如果是批量操作，退出多选模式
          if (isBatch) {
            cancelMultiSelect();
          } else {
            // 关闭操作按钮
            activeTagId.value = null;
          }
        } else {
          const errorMessage = isBatch ? '批量弃用标签失败' : '弃用标签失败';
          ElMessage.error(result.msg || errorMessage);

          // 如果是单个操作，关闭操作按钮
          if (!isBatch) {
            activeTagId.value = null;
          }
        }
      } catch (error) {
        console.error('弃用标签异常:', error);
        const errorMessage = isBatch ? '批量弃用标签异常' : '弃用标签异常';
        ElMessage.error(errorMessage);

        // 如果是单个操作，关闭操作按钮
        if (!isBatch) {
          activeTagId.value = null;
        }
      }
    })
    .catch(() => {
      // 用户取消操作
    });
};



// 全局点击处理函数
let closeHandler = null;

// 显示标签操作按钮
const showTagActions = (event, tagId) => {

  // 防止冲突，阻止事件冒泡
  event.stopPropagation();

  // 如果点击的是当前已激活的标签，则关闭操作按钮
  if (activeTagId.value === tagId) {
    activeTagId.value = null;
    // 移除先前的监听器
    if (closeHandler) {
      document.removeEventListener('click', closeHandler);
      closeHandler = null;
    }
  } else {
    // 先关闭其它标签的操作按钮，再显示当前标签的操作按钮
    activeTagId.value = tagId;

    // 移除先前的监听器
    if (closeHandler) {
      document.removeEventListener('click', closeHandler);
    }

    // 添加新的点击其他区域关闭按钮的处理
    setTimeout(() => {
      closeHandler = () => {
        activeTagId.value = null;
        document.removeEventListener('click', closeHandler);
        closeHandler = null;
      };
      document.addEventListener('click', closeHandler);
    }, 10);
  }
};

// 确认分类
const confirmClassify = async () => {
  if (!selectedCategoryId.value) {
    ElMessage.warning('请选择一个分类');
    return;
  }

  // 判断处理类型：批量新增、批量分类或单个分类
  // 如果有批量新增的输入，则优先处理批量新增
  if (batchTagsInput.value.trim()) {
    try {
      // 直接发送标签字符串
      const result = await post(`${api_url}/tag/createTag`, JSON.stringify({
        name: batchTagsInput.value.trim(),
        category_id: selectedCategoryId.value,
        type: 1 // 默认为对比标签类型
      }));
      if (result.error_code === 0) {
        ElMessage.success('标签批量创建成功');
        selectedTags.value = [];
        // 重置批量标签输入
        batchTagsInput.value = '';

        // 刷新所有相关数据
        await fetchCategoryTree();
        await fetchPendingTags();
        await newFetchPendingTags();
        await fetchFrequentTags();
        await fetchAbandonedTags();
        classifyLoad.value = false

      } else {
        ElMessage.error(result.msg || '批量创建标签失败');
        batchTagsInput.value = '';
        classifyLoad.value = false
      }

      // 关闭分类对话框
      classifyDialogVisible.value = false;

      return; // 处理完批量新增后直接返回，不继续处理其他情况

    } catch (error) {
      classifyLoad.value = false
      console.error('批量新增标签异常:', error);
      ElMessage.error('批量新增标签异常');
      // 关闭分类对话框
      batchTagsInput.value = '';
      classifyDialogVisible.value = false;
      return;
    }
  }

  // 处理其他类型的分类操作
  let tagIds = [];

  // 如果是批量分类操作
  if (currentBatchClassifyTagIds.length > 0) {
    tagIds = currentBatchClassifyTagIds;
  }
  // 如果是单个标签分类操作
  else if (currentClassifyTagId.value) {
    tagIds = [currentClassifyTagId.value];
  } else {
    ElMessage.error('缺少标签信息');
    return;
  }

  try {
    // 调用分类标签接口
    const result = await post(`${api_url}/tag/updatePendingNewTag`, JSON.stringify({
      ids: tagIds,
      category_id: selectedCategoryId.value
    }));

    if (result.error_code === 0) {
      // 根据处理模式显示不同的成功消息
      if (currentBatchClassifyTagIds.length > 0) {
        ElMessage.success(`已成功分类 ${currentBatchClassifyTagIds.length} 个标签`);
        // 清空批量处理数组
        currentBatchClassifyTagIds = [];
        // 退出多选模式
        cancelMultiSelect();
      } else {
        ElMessage.success('标签分类成功');
      }

      // 关闭分类对话框
      classifyDialogVisible.value = false;
      classifyLoad.value = false
      // 刷新所有相关数据
      fetchPendingTags();
      fetchFrequentTags();
      fetchAbandonedTags();
      // 刷新分类树结构数据
      fetchCategoryTree();
    } else {
      ElMessage.error(result.msg || '标签分类失败');
      classifyLoad.value = false
    }
  } catch (error) {
    classifyLoad.value = false
    console.error('分类标签异常:', error);
    ElMessage.error('分类标签异常');
  }
};
// 人工处理确认分类
const newConfirmClassify = async () => {
  if (!selectedCategoryId.value) {
    ElMessage.warning('请选择一个分类');
    return;
  }

  // 判断处理类型：批量新增、批量分类或单个分类
  // 如果有批量新增的输入，则优先处理批量新增
  if (batchTagsInput.value.trim()) {
    try {
      // 直接发送标签字符串
      const result = await post(`${api_url}/tag/createTag`, JSON.stringify({
        name: batchTagsInput.value.trim(),
        category_id: selectedCategoryId.value,
        type: 1 // 默认为对比标签类型
      }));
      if (result.error_code === 0) {
        ElMessage.success('标签批量创建成功');
        newSelectedTags.value = [];
        // 重置批量标签输入
        batchTagsInput.value = '';

        // 刷新所有相关数据
        await fetchCategoryTree();
        await fetchPendingTags();
        await newFetchPendingTags();
        await fetchFrequentTags();
        await fetchAbandonedTags();
        classifyLoad.value = false
      } else {
        ElMessage.error(result.msg || '批量创建标签失败');
        batchTagsInput.value = '';
        classifyLoad.value = false
      }

      // 关闭分类对话框
      classifyDialogVisible.value = false;

      return; // 处理完批量新增后直接返回，不继续处理其他情况

    } catch (error) {
      console.error('批量新增标签异常:', error);
      ElMessage.error('批量新增标签异常');
      classifyLoad.value = false
      // 关闭分类对话框
      batchTagsInput.value = '';
      classifyDialogVisible.value = false;
      return;
    }
  }

  // 处理其他类型的分类操作
  let tagIds = [];

  // 如果是批量分类操作
  if (currentBatchClassifyTagIds.length > 0) {
    tagIds = currentBatchClassifyTagIds;
  }
  // 如果是单个标签分类操作
  else if (currentClassifyTagId.value) {
    tagIds = [currentClassifyTagId.value];
  } else {
    ElMessage.error('缺少标签信息');
    return;
  }

  try {
    // 调用分类标签接口
    const result = await post(`${api_url}/tag/updatePendingTempTag`, JSON.stringify({
      ids: tagIds,
      category_id: selectedCategoryId.value
    }));

    if (result.error_code === 0) {
      // 根据处理模式显示不同的成功消息
      if (currentBatchClassifyTagIds.length > 0) {
        ElMessage.success(`已成功分类 ${currentBatchClassifyTagIds.length} 个标签`);
        // 清空批量处理数组
        currentBatchClassifyTagIds = [];
        // 退出多选模式
        cancelMultiSelect();
        classifyLoad.value = false
      } else {
        ElMessage.success('标签分类成功');
      }

      // 关闭分类对话框
      classifyDialogVisible.value = false;
      // 刷新所有相关数据
      fetchPendingTags();
      newFetchPendingTags()
      fetchFrequentTags();
      fetchAbandonedTags();
      // 刷新分类树结构数据
      fetchCategoryTree();
    } else {
      ElMessage.error(result.msg || '标签分类失败');
      classifyLoad.value = false
    }
  } catch (error) {
    console.error('分类标签异常:', error);
    ElMessage.error('分类标签异常');
    classifyLoad.value = false
  }
};

// 新标签分类
const classifyTag = (tagId) => {
  console.log(111);
  
  currentClassifyTagId.value = tagId;
  openClassifyDialog('pending');
  activeTagId.value = null;
};
// 人工处理分类
const newClassifyTag = (tagId) => {
  console.log(222);
  currentClassifyTagId.value = tagId;
  openClassifyDialog('newPending');
  activeTagId.value = null;
};

// 新标签-弃用功能
const abandonTag = async (tagId) => {
  deletePendingTag(tagId);
};

const abandonedTagsVisible = ref(false); // 弃用标签弹窗显示状态
const abandonedTagsSearch = ref(''); // 弃用标签搜索关键词
const changeCheck = (type, index, data) => {
  data.forEach((item, i) => {
    if (type == 1 && index == i) {
      item.children.forEach(name => {
        name.selected = item.selected
      })
    }
    if (type === 2) {
      if (index === i) {
        let arr = item.children.filter(name => name.selected)
        if (arr.length > 0) {
          item.selected = true
        } else {
          item.selected = false
        }
      }
    }
  })
}

// 批量删除
const batchDeleteTag = (index) => {
  let nameArr = []
  filteredAbandonedTag.value.forEach((item, i) => {
    if (index === i) {
      item.children.forEach((name, i) => {
        if (name.selected) {
          nameArr.push(name.name)
        }
      })
    }
  })
  let nameString = nameArr.join(',')
  delectTag(nameString)
}
// 单个删除
const delectTag = (name) => {

  ElMessageBox.confirm(
    '确认删除',
    'Warning',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(() => {
      let url = `${api_url}/tag/deleteDeprecated`;
      post(url, JSON.stringify({
        names: name
      }))
        .then(res => {
          toggleFrequentTagsList()
          fetchAbandonedTags()
          ElMessage({
            type: 'success',
            message: '删除成功',
          })
        })
        .catch(() => {
          ElMessage({
            type: 'info',
            message: '删除失败',
          })
        })
    })


}
// 批量删除弃用匹配词
const batchDeleteMatchingTag = () => {
  let idArr = []
  filteredMatchingTag.value.forEach((item) => {
    if (item.selected) {
      idArr.push(item.id)
    }
  })
  let idString = idArr.join(',')
  delectMatchingTag(idString)
}
// 删除弃用匹配词
const delectMatchingTag = (id) => {
  ElMessageBox.confirm(
    '确认删除',
    'Warning',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(() => {
      let url = `${api_url}/tag/deleteMatchingWord1`;
      post(url, JSON.stringify({
        id
      }))
        .then(res => {
          toggleMatchingTagsList()
          fetchFrequentTags()
        })
        .catch(() => {
          ElMessage({
            type: 'info',
            message: '删除失败',
          })
        })
    })
}

const abandonTagData = ref([
  { name: '单标签', children: [], selected: false, indeterminate: false },
  { name: '多标签', children: [], selected: false, indeterminate: false },
])
// 筛选弃用标签的计算属性
const filteredAbandonedTag = computed(() => {
  if (!abandonedTagsSearch.value.trim()) {
    return abandonTagData.value;
  }
  const searchTerm = abandonedTagsSearch.value.trim().toLowerCase();
  return abandonTagData.value.map(group => ({
    ...group,
    children: group.children.filter(tag =>
      tag.toLowerCase().includes(searchTerm)
    )
  }));
});

const getAbandonedIndeterminate = (item) => {
  const total = item.children.length;
  const checked = item.children.filter(tag => tag.selected).length;
  return checked > 0 && checked < total;
};

// 弃用标签匹配词弹窗相关
const matchingTagData = ref([])
const changeMatchingCheckAll = () => {
  filteredMatchingTag.value.forEach(item => {
    item.selected = matchingDialogSeleced.value
  })
}
const changeMatchingCheck = () => {
  const allChecked = filteredMatchingTag.value.length > 0 &&
    filteredMatchingTag.value.every(item => item.selected);
  matchingDialogSeleced.value = allChecked;
}
const matchingDialogIndeterminate = computed(() => {
  const total = filteredMatchingTag.value.length;
  const checked = filteredMatchingTag.value.filter(item => item.selected).length;
  return checked > 0 && checked < total;
});

// 筛选弃用标签的计算属性
const filteredMatchingTag = computed(() => {
  if (!matchingTagsSearch.value.trim()) {
    return matchingTagData.value;
  }
  const searchTerm = matchingTagsSearch.value.trim().toLowerCase();
  return matchingTagData.value.filter(item => item.word.toLowerCase().includes(searchTerm));
});

const matchingTagsVisible = ref(false); // 弃用标签匹配词弹窗显示状态
const matchingTagsSearch = ref(''); // 弃用标签匹配词搜索关键词
const matchingDialogLoading = ref(false); // 弃用标签匹配词搜索关键词
const matchingDialogSeleced = ref(false); // 弃用标签匹配词搜索关键词


const toggleMatchingTagsList = () => {
  // 打开弃用标签弹窗
  matchingDialogSeleced.value = false
  matchingDialogLoading.value = true;
  matchingTagsVisible.value = true;
  let url = `${api_url}/tag/filterTags`
  post(url, JSON.stringify({})).then((res) => {
    console.log(res.data);
    res.data.forEach((d) => {
      d.selected = false
    })
    matchingTagData.value = res.data
    matchingDialogLoading.value = false
  }).catch(err => {
    matchingDialogLoading.value = false
  })
};

const downLoad = () => {
  let newData = []
  abandonedTags.value.map((item) => {
    newData.push(item.content)
  })
  createXLSX(newData, '弃用标签')
}
const toggleFrequentTagsList = () => {
  // 打开弃用标签弹窗
  abandonedTagsVisible.value = true;
  let url = `${api_url}/tag/haddleDeprecated`
  post(url, JSON.stringify({})).then((res) => {
    const { tag, tags } = res.data
    let newTag = [], newTags = []
    tag.forEach(item => {
      newTag.push({ name: item, selected: false })
    })
    tags.forEach(item => {
      newTags.push({ name: item, selected: false })
    })
    abandonTagData.value[0].children = newTag
    abandonTagData.value[1].children = newTags
    abandonTagData.value[0].selected = false
    abandonTagData.value[1].selected = false

  })
};


</script>

<style scoped>
/* 基础样式 */
body {
  margin: 0;
  padding: 0;
}



/* 新增分类表单样式 */
.category-form {
  padding: 10px;

}

.filterKey-form {
  padding: 10px;
  max-height: 400px;
  height: 400px;
  overflow: auto;
}

.form-item {
  margin-bottom: 20px;
}

.form-item label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
}

.required {
  color: #f56c6c;
  margin-right: 4px;
}

.tag-library-container {
  width: 100%;
  height: 100%;
  background-color: #ffffff;
  padding: 20px;
  box-sizing: border-box;

  .tag-tabs{
    height: 100%;
    .tag-tab{
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
    }
  }

  /* 右侧区域 */
  .right-section {
    width: 100%;

    /* 待处理标签 */
    .tags-section {
      display: flex;
      flex-direction: column;
      background-color: white;
      border-radius: 4px;
      padding: 15px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      height: 40%;
      margin-bottom: 20px;
    }

  }

  /* 删除记录弹窗样式 */
  .delete-history-dialog {
    .delete-history-content {
      max-height: 500px;
      overflow-y: auto;

      .history-item {
        margin-bottom: 20px;
        border-bottom: 1px solid #f0f0f0;
        padding-bottom: 15px;

        &:last-child {
          border-bottom: none;
        }

        .history-date-line {
          display: flex;
          align-items: center;
          margin-bottom: 10px;

          .history-date {
            font-weight: bold;
            margin-right: 10px;
          }

          .history-time {
            color: #909399;
            margin-right: 15px;
          }

          .history-status {
            color: #F56C6C;
          }
        }

        .history-content-row {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-left: 20px;

          .tag-block {
            background-color: #f0f0f0;
            border-radius: 4px;
            padding: 6px 12px;
            display: inline-block;
          }
        }
      }

      .no-history {
        text-align: center;
        padding: 30px 0;
        color: #909399;
      }
    }
  }

  /* 弃用标签弹窗 */
  .abandoned-tags-dialog {
    .search-bar {
      margin-bottom: 15px;
    }

    .abandoned-tags-content {
      max-height: 420px;
      overflow-y: auto;
    }

    .abandoned-tags-date {
      font-size: 14px;
      font-weight: bold;
      margin: 15px 0 10px 0;
      padding-bottom: 5px;
      border-bottom: 1px solid #eee;
      display: flex;
      justify-content: space-between;

      .select-all {
        margin-left: 20px;

      }
    }

    .abandoned-tags-grid {
      display: flex;
      flex-wrap: wrap;
      justify-content: flex-start;
      align-content: flex-start;
      gap: 10px;
      height: 150px;
      overflow: auto;

      .tag-item {
        background-color: #f5f7fa;
        border-radius: 4px;
        padding: 6px 12px;
        font-size: 12px;
        display: inline-flex;
        align-items: center;
        position: relative;
        max-height: 30px;

        .restore-icon {
          cursor: pointer;
          margin-left: 5px;
          color: #409eff;
          font-size: 14px;

          &:hover {
            color: #66b1ff;
          }
        }
      }
    }
  }

  /* 弃用标签匹配词弹窗 */
  .matching-tags-dialog {
    .search-bar {
      margin-bottom: 15px;
    }

    .matching-tags-content {
      max-height: 420px;
      height: 420px;
      overflow-y: auto;
    }

    .restore-icon {
      cursor: pointer;
      margin-left: 5px;
      color: #409eff;
      font-size: 16px;

      &:hover {
        color: #66b1ff;
      }
    }

    .matching-tags-date {
      cursor: pointer;
      font-size: 18px;
      font-weight: bold;
      margin: 15px 0 10px 10px;
      padding-bottom: 5px;
      color: #409eff;
      border-bottom: 1px solid #eee;
      display: flex;
      justify-content: space-between;

      .select-all {
        margin-left: 20px;

      }
    }

    .matching-tags-grid {
      display: flex;
      /* flex-wrap: wrap; */
      flex-direction: column;
      gap: 10px;
      overflow: auto;

      .matching-item {
        display: flex;
        gap: 10px;
        flex-direction: row;
        flex-wrap: wrap;
        align-content: flex-start;
      }

      .tag-item {
        background-color: #f5f7fa;
        border-radius: 4px;
        padding: 6px 12px;
        font-size: 16px;
        display: inline-flex;
        align-items: center;
        position: relative;
        max-height: 30px;

        .restore-icon {
          cursor: pointer;
          margin-left: 5px;
          color: #409eff;
          font-size: 14px;

          &:hover {
            color: #66b1ff;
          }
        }
      }
    }
  }
}

.classify-tree-container {
  overflow: auto;
  height: 600px;
}

.action-btn {
  border: none;
  padding: 6px 12px;
  cursor: pointer;
  font-size: 12px;
  text-align: center;
  white-space: nowrap;

  &.classify-btn {
    background-color: #f0f9eb;
    color: #67c23a;

    &:hover {
      background-color: #e1f3d8;
    }
  }

  &.abandon-btn {
    background-color: #fef0f0;
    color: #f56c6c;

    &:hover {
      background-color: #fde2e2;
    }
  }
}

.delete-icon {
  position: absolute;
  right: -6px;
  top: -6px;
  color: #ffffff;
  width: 12px;
  height: 12px;
  background-color: #ff0000;
  border-radius: 50%;
  font-size: 14px;
  cursor: pointer;
}
</style>