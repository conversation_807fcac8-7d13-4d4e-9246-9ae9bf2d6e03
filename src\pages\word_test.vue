<template>
  <div class="padding-20">
    <div style="margin-bottom: 20px;">
      <el-button type="primary" size="small" @click="addStep">建立流程</el-button>
      <el-button type="primary" size="small" @click="saveStep">保存流程</el-button>
    </div>

    <div class="content webkit-scrollbar">
      <el-timeline>
        <el-timeline-item
            :timestamp="'流程'+(index+1)"
            placement="top"
            v-for="(item,index) in case_arr"
            :key="index"
        >
          <el-card>
            <div class="m-b-15">
              <h3>提示词：</h3>
              <el-input v-model="item.word" style="height: 100px;" type="textarea"></el-input>
            </div>
            <!--添加key-->
            <div class="m-b-15">
              <el-button type="primary" size="small" @click="addKey(index)">添加Key</el-button>
            </div>
            <div class="m-b-15" v-for="(row,k_index) in item.keys">
              <h3>key{{k_index+1}}：</h3>
              <el-input style="height: 50px;" type="textarea" v-model="row.value"></el-input>
            </div>
            <!--添加value-->
            <div class="m-b-15">
              <el-button type="primary" size="small" @click="addValue(index)">添加Value</el-button>
            </div>
            <div class="m-b-15" v-for="(row,v_index) in item.values">
              <h3>Value_{{v_index+1}}：</h3>
              <el-input type="text" v-model="row.value"></el-input>
            </div>

            <h4>结果：</h4>
            <div class="border-1 padding-10 text">
              sadfasf
            </div>
          </el-card>
        </el-timeline-item>
      </el-timeline>
    </div>
  </div>
</template>

<script setup>
    import {ref} from 'vue';
    const case_arr = ref([{
      word:"",
      keys:[],
      values:[]
    }])
    const text = ref(`
      你是一位细致严谨的医疗信息整合助手。
      你的任务是整合并更新同一位病人不同日期的病症信息，并直接输出最终的、逻辑一致的最新病症总结。根据提供的按日期先后顺序排列的病人病症信息，进行以下处理并直接输出最终结果：
    `)
    const saveStep = ()=>{
      localStorage.setItem('list_log',JSON.stringify(case_arr.value))
    }
    const addStep = ()=>{
      case_arr.value.push({
        word:"",
        keys:[],
        values:[]
      })
    }

    const addValue = (index)=>{
      case_arr.value[index]['values'].push({
        value:""
      })
    }
    const addKey = (index)=>{
      case_arr.value[index]['keys'].push({
        value:""
      })
    }
</script>

<style scoped>
.content{
  height: calc(100vh - 40px - 50px);
  overflow-y: scroll;
}
.padding-10{
  padding: 10px;
}
  .padding-20{
    padding: 20px;
  }
  .padding-t-15{
    padding-top: 15rpx;
  }
  .m-b-15{
    margin-bottom: 15px;
  }
  .border-1{
    border: 1px solid antiquewhite;
  }
  .text{
    white-space: pre-line; /* 或者使用 pre-wrap */
  }
</style>