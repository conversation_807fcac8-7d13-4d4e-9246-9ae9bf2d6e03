<template>
  <div class="tableData">
      <h3>{{ route.meta.title }}</h3>
      <div class="topMenu">
            <div class="menuBox">
              <el-input style="display: none;"/>
              <el-input style="width: 200px;" v-model="searchValue" placeholder="输入患者名称"/>
                
              <el-date-picker
                  v-model="dataValue"
                  @change="getSysCaseList"
                  type="date"
                  placeholder="选择创建时间"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
              />
              <el-date-picker
                  v-model="updateTime"
                  type="date"
                  placeholder="选择上传时间"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
              />
              <el-button @click="hebingTask" type="primary">合并任务</el-button>
              <el-button @click="bulkOperation">批量操作</el-button>
              <el-button @click="piliangUpload">批量上传</el-button>
            </div>
      </div>
      <el-table class="table"  v-loading="load" :data="user_list" border style="width: 100%;height: 800px">
        <el-table-column type="index" label="编号" width="70"/>
        <el-table-column prop="pat_name" label="患者名称"/>
        <el-table-column prop="doctor_name" label="医生名称"
           :filters="[
              { text:'蔡本静', value:'蔡本静' },
              { text:'雷静', value:'雷静'},
              { text:'杨敏', value:'杨敏'},
              { text:'任凤', value:'任凤'},
              { text:'魏羽', value:'魏羽'},
              { text:'薛松', value:'薛松'},
              { text:'罗叶', value:'罗叶'},
              { text:'段敏', value:'段敏'},
              { text:'张艳', value:'张艳'},
              { text:'何静', value:'何静'},
           ]"
            :filter-method="filterExpidate"
            filter-placement="bottom-end"
        />
        <el-table-column prop="create_time" label="创建时间"/>
        <el-table-column prop="edit_time" label="上传时间"/>
        <el-table-column prop="his_last_time" label="his病例最后时间"/>
        <el-table-column prop="state" label="病例类型" />
        <el-table-column prop="content" label="上传原因" />
        <el-table-column prop="is_sys" label="创建方式" width="100">
          <template #default="scope">
            {{scope.row.is_sys===1?'自动':'手动'}}
          </template>
        </el-table-column>
        <el-table-column prop="document" label="AI病例内容" width="600">
          <template #default="scope">
            <div class="document"  v-if="scope.row.document" @click="seeContent(scope.row)">
              {{scope.row.document}}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="上传状态">
          <template #default="scope">
            <div v-if="scope.row.status==-1">
              <el-tag type="warning">已删除</el-tag>
            </div>
            <div v-else>
              <div v-if="scope.row.document">
                <div v-if="scope.row.status===0||scope.row.status===9">
                  <el-tag>待上传</el-tag>
                </div>
                <div v-else>
                  <el-tag v-if="scope.row.upload_status===1&&scope.row.status===0">上传中</el-tag>
                  <el-tag type="success"  v-if="scope.row.status===2">上传成功</el-tag>
                  <el-tag v-if="scope.row.status===-1">取消上传</el-tag>
                  <el-tag type="danger" v-if="scope.row.status===3||scope.row.status===4">上传失败</el-tag>
                </div>
              </div>
              <div v-else>
                <div v-if="scope.row.status===-1">
                  <el-tag>任务已删除</el-tag>
                </div>
                <div v-else>
                  <el-tag>待书写</el-tag>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
    
        <el-table-column prop="address" label="操作" fixed="right" width="250">
          <template #default="scope">
            <el-button type="primary" @click="generate(scope.row)" v-if="scope.row.status!==2" :loading="scope.row.isBtn">生成病例</el-button>
            <el-button type="warning" v-if="scope.row.status!==2" @click="generateUploads(scope.row)" :loading="scope.row.isUploadBtn">上传病例</el-button>
            <el-button type="success" v-if="scope.row.status===2">上传成功</el-button>
          </template>
        </el-table-column>
      </el-table>
  </div>

  <el-dialog
      v-model="dialogVisible"
      title="内容"
      width="50%"
  >
    <div class="document-content">
      <el-input type="textarea" style="height: 400px" v-model="content"></el-input>
      <div style="padding-top: 15px">
        <el-button type="primary" @click="save" :loading="in_load">保存</el-button>
      </div>
    </div>
  </el-dialog>

</template>

<script setup>
  import {computed, ref} from 'vue';
  import {message,confirm} from "@/utils/message";
  import c from "@/utils/config";
  import { post } from '@/utils/request.js'
  import { useRoute } from 'vue-router';

  const route = useRoute();
  const api_url = c.api_url;
  const searchValue = ref("")
  const load = ref(false)
  const dialogVisible = ref(false)
  const content =  ref("")
  const select_row = ref({})
  const in_load = ref(false)

  const currentDate = new Date();
  const year = currentDate.getFullYear();
  const month = currentDate.getMonth() + 1; // 月份从0开始，需要加1
  const day = currentDate.getDate();
  const dataValue = ref(year + "-" + month + "-" + day)
  const updateTime = ref(year + "-" + month + "-" + day)
  
  // 批量生成
  const bulkOperation = ()=>{
    confirm("确认批量生成吗？").then(()=>{
      user_list.value.forEach((item)=>{
        generate(item)
      })
    })
  }
  // 批量上传
  const piliangUpload = ()=>{
    confirm("确认批量上传吗？").then(()=>{
      user_list.value.forEach((item)=>{
        generateUploads(item) 
      })
    })
  }
  // 合并任务
  const hebingTask = ()=>{
    let task_ids = user_list.value.map((row)=>row.task_id)
    if(task_ids.length>0){
      task_ids = task_ids.join(",")
      post(`${api_url}/task/mergeTask`, JSON.stringify({task_ids})).then(res => {
        getSysCaseList()
        message("合并成功！")
      }).catch(error => {
        message("合并失败！")
      });
    }
  }

  const save = ()=>{
    let row = select_row.value;
    in_load.value = true
    post(`${api_url}/AiCase/updateCase`, JSON.stringify({task_id:row.task_id,document:content.value})).then(res => {
      message("保存成功！")
      user_list.value =user_list.value.map((item)=>{
        if(item.task_id===row.task_id){
          item.document = content.value
        }
        return item
      })
      in_load.value = false
    }).catch(error => {
      message("保存失败！")
      in_load.value = false
    });
  }



  const searchValueName = ()=>{
    post(`${api_url}/AiCase/exeTask`, JSON.stringify({doctor_id:row.doctor_id,task_id:row.task_id})).then(res => {
      message(res.data)
      // load.value = false
    }).catch(error => {
      load.value = false
    });
  }

  const seeContent = (row)=>{
    dialogVisible.value = true;
    select_row.value = row
    content.value = row.document
  }

  const filterExpidate = (value,row)=>{
    return row.doctor_name == value
  }

  const generateUploads = (row)=>{
    // load.value = true
    user_list.value =user_list.value.map((item)=>{
      if(item.task_id===row.task_id){
        item.isUploadBtn = true
      }
      return item
    })
    post(`${api_url}/AiCase/exeTask`, JSON.stringify({doctor_id:row.doctor_id,task_id:row.task_id})).then(res => {
      message(res.data)
      // load.value = false
    }).catch(error => {
      load.value = false
    });
  }

  // 生成病例
  const generate = (row)=>{
    // load.value = true

    user_list.value =user_list.value.map((item)=>{
      if(item.task_id===row.task_id){
        item.isBtn = true
      }
      return item
    })

    post(`${api_url}/AiCase/createDocument`, JSON.stringify({task_id:row.task_id})).then(res => {
      user_list.value =user_list.value.map((item)=>{
        if(item.task_id===row.task_id){
          item.isBtn = false
          item.document = res.data
        }
        return item
      })
      // load.value = false
      if(res.error_code&&res.error_code===500){
        message("生成病例出错！")
        return
      }
      // getSysCaseList()
    }).catch(error => {
      message("生成病例出错！")
      load.value = false
    });
  }

  // 数据
  const tableData = ref([])

  const user_list = computed(() =>
      tableData.value.filter((data_list) =>
          (!searchValue.value ||
              data_list.pat_name.toLowerCase().includes(searchValue.value.toLowerCase()))
      )
  )

  const formatDateForComparison = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return dateString;
    // 返回标准格式 YYYY-MM-DD
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
  }

  const getSysCaseList = ()=>{
    load.value = true
    post(`${api_url}/AiCase/getSysCaseList`,JSON.stringify({date:dataValue.value})).then(res => {
      let doctor_names = {}
      tableData.value = res.data.map((row)=>{
        if(row.status===2){
          if(doctor_names[row.doctor_name]){
            doctor_names[row.doctor_name]+=','+row.pat_name
          }else{
            doctor_names[row.doctor_name] = row.pat_name
          }
        }
        return row
      })
      load.value = false
    }).catch(error => {
      load.value = false
    });
  }
  getSysCaseList()
</script>

<style scoped>
  .tableData {
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      background-color: #ffffff;
      padding: 10px;
      box-sizing: border-box;
  }
  h3 {
      height: 40px;
  }
  .topMenu {
      display: flex;
      justify-content: flex-start;
      margin-bottom: 10px;
      flex-wrap: wrap;
      gap: 5px;
  }

  .menuBox {
      display: flex;
      justify-content: flex-start;
      margin-bottom: 10px;
      flex-wrap: wrap;
      gap: 5px;
  }

  .table {
      flex: 1;
      overflow-y: auto;
  }
  .tag_list {
      padding: 24px;
  }

  .tag_lists {
      height: 600px;
      overflow-y: scroll;
  }
  .search-doctor{
      width: 200px;
      margin-left: 10px;
  }
  .pass_select {
      display: flex;
      justify-content: center;
      align-items: center;
  }
    /* .document-content{
      max-height: 500px;
      overflow-y: auto;
    }
    .header-list{
      display: flex;
      margin-bottom: 12px;
    }
    .header-item{
      margin-right: 15px;
    }
    .document{
      height: 100px;
      overflow-y: scroll;
      cursor: pointer;
    } */
</style>