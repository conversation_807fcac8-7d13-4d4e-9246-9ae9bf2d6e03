<template>
  <div class="tag-library-container">
    <!-- 左侧：使用中的标签 -->
    <div class="used-tags-section">
      <TagSection title="使用中的标签" :showSearch="false" searchPlaceholder="搜索单待处理标签" :searchValue="pendingTagSearch"
        @update:searchValue="val => pendingTagSearch = val">
        <template #searchs>
          <el-input placeholder="搜索标签分类" v-model="searchTagCategory" clearable></el-input>
          <el-input placeholder="搜索标签内容" v-model="searchTagContent" clearable></el-input>
        </template>
        <template #actions>
          <template v-if="true">
            <el-button type="primary" @click="showBatchAddDialog">批量新增</el-button>
            <el-button type="primary" @click="showCategoryDialog">新增分类</el-button>
          </template>
        </template>
      </TagSection>

      <!-- 标签分类列表 -->
      <div class="category-list" v-loading="loadStatus.treeLoad">
        <div v-for="(category, index) in filteredCategories" :key="index">
          <!-- <div class="category-header">
           <div class="category-info" @click="toggleCategory(category.id)">
             <span class="expand-icon">{{ expandedCategories.includes(category.id) ? '▼' : '▶' }}</span>
             <span>{{ category.name }}</span>
             <span class="tag-count">{{ category.count }}</span>
             <span>{{ category.description ? `(${category.description})` : '' }}</span>
           </div>
           <div class="category-actions">
             <el-button size="small" type="primary" plain @click.stop="editCategory(category)">
               <el-icon><Edit /></el-icon>
             </el-button>
             <el-button size="small" type="danger" plain @click.stop="confirmDeleteCategory(category.id, category.name)">
               <el-icon><Delete /></el-icon>
             </el-button>
           </div>
         </div> -->
          <!-- 子分类 -->
          <div class="subcategory-list">
            <div v-for="(subCategory, subIndex) in getFilteredSubCategories(category)" :key="subIndex">
              <div class="subcategory-header">
                <div class="subcategory-info" @click="toggleSubCategory(subCategory.id)">
                  <span class="expand-icon">{{ expandedSubCategories.includes(subCategory.id) ? '▼' : '▶' }}</span>
                  <span>{{ subCategory.name }}</span>
                  <!-- <span style="padding-left:10px">{{ subCategory.description ? `(${subCategory.description})` : '' }}</span> -->
                </div>
                <div class="subcategory-actions">
                  <el-button size="small" type="primary" plain @click.stop="editCategory(subCategory)">
                    <el-icon>
                      <Edit />
                    </el-icon>
                  </el-button>
                  <el-button size="small" type="danger" plain
                    @click.stop="confirmDeleteCategory(subCategory.id, subCategory.name)">
                    <el-icon>
                      <Delete />
                    </el-icon>
                  </el-button>
                </div>
              </div>

              <!-- 标签列表 -->
              <div v-if="expandedSubCategories.includes(subCategory.id)" class="tag-list">
                <!-- 对比标签区域 -->
                <div class="tag-group tag-group-1">
                  <div class="tag-type-indicator">
                    <span>对比标签</span>
                  </div>

                  <!-- 批量操作按钮区域（独立一行） -->
                  <div class="batch-actions-row" v-if="getTagsByType(subCategory.tags, 1).length > 0">
                    <template v-if="!isTagBatchMode[`${subCategory.id}-1`]">
                      <el-button size="small" type="primary"
                        @click="startTagBatchMode(subCategory.id, 1)">批量删除</el-button>
                    </template>
                    <template v-else>
                      <el-checkbox v-model="tagBatchSelectAll[`${subCategory.id}-1`]"
                        @change="toggleSelectAllTags(subCategory.id, 1)">
                        全选
                      </el-checkbox>
                      <el-button size="small" type="danger" :disabled="!hasSelectedTags(subCategory.id, 1)"
                        @click="batchDeleteTags(subCategory.id, 1)">删除</el-button>
                      <el-button size="small" @click="cancelTagBatchMode(subCategory.id, 1)">取消</el-button>
                    </template>
                  </div>
                  <div class="tags-wrapper">
                    <TagsGrid
                      :tags="getFormattedTagsForGrid(getTagsByType(subCategory.tags, 1), subCategory.id, 1)"
                      :isMultiSelectMode="isTagBatchMode[`${subCategory.id}-1`]"
                      :activeTagId="activeTagId"
                      :loading="false"
                      :isTagSelected="(tagId) => isTagSelectedInBatch(subCategory.id, 1, tagId)"
                      :draggable="false"
                      @toggleTagSelection="(tagId) => toggleTagInBatch(subCategory.id, 1, tagId)"
                      @showTagActions="showTagActions"
                      @dragstart="onPendingTagDragStart"
                      @dragend="onPendingTagDragEnd"
                    >
                      <template #end="{ tag }">
                        <el-icon class="delete-icon" @click="removeTag(subCategory.id, tag.id)">
                          <Close />
                        </el-icon>
                      </template>
                    </TagsGrid>
                    
                    <!-- 添加对比标签按钮（非编辑状态时显示） -->
                    <div
                      v-if="(!isAddingTag || currentSubCategoryId !== `${subCategory.id}-1`) && !isTagBatchMode[`${subCategory.id}-1`]"
                      class="add-tag" @click="startAddingTag(`${subCategory.id}-1`, 1)">
                      <span>+ 添加标签</span>
                    </div>
                    <!-- 添加标签输入框（编辑状态时显示） -->
                    <div
                      v-else-if="isAddingTag && currentSubCategoryId === `${subCategory.id}-1` && !isTagBatchMode[`${subCategory.id}-1`]"
                      class="tag-input-wrapper">
                      <el-input v-model="newTagContent" placeholder="请输入标签内容" size="small"
                        @keyup.enter="handleAddTag"></el-input>
                      <div class="tag-input-buttons">
                        <el-button size="small" type="primary" @click="handleAddTag">确定</el-button>
                        <el-button size="small" @click="cancelAddingTag">取消</el-button>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 替换标签区域 -->
                <div class="tag-group tag-group-2">
                  <div class="tag-type-indicator">
                    <span>替换标签</span>
                  </div>

                  <!-- 批量操作按钮区域（独立一行） -->
                  <div class="batch-actions-row" v-if="getTagsByType(subCategory.tags, 2).length > 0">
                    <template v-if="!isTagBatchMode[`${subCategory.id}-2`]">
                      <el-button size="small" type="primary"
                        @click="startTagBatchMode(subCategory.id, 2)">批量删除</el-button>
                    </template>
                    <template v-else>
                      <el-checkbox v-model="tagBatchSelectAll[`${subCategory.id}-2`]"
                        @change="toggleSelectAllTags(subCategory.id, 2)">
                        全选
                      </el-checkbox>
                      <el-button size="small" type="danger" :disabled="!hasSelectedTags(subCategory.id, 2)"
                        @click="batchDeleteTags(subCategory.id, 2)">删除</el-button>
                      <el-button size="small" @click="cancelTagBatchMode(subCategory.id, 2)">取消</el-button>
                    </template>
                  </div>
                  <div class="tags-wrapper">
                    <TagsGrid
                      :tags="getFormattedTagsForGrid(getTagsByType(subCategory.tags, 2), subCategory.id, 2)"
                      :isMultiSelectMode="isTagBatchMode[`${subCategory.id}-2`]"
                      :activeTagId="activeTagId"
                      :loading="false"
                      :isTagSelected="(tagId) => isTagSelectedInBatch(subCategory.id, 2, tagId)"
                      :draggable="false"
                      @toggleTagSelection="(tagId) => toggleTagInBatch(subCategory.id, 2, tagId)"
                      @showTagActions="showTagActions"
                      @dragstart="onPendingTagDragStart"
                      @dragend="onPendingTagDragEnd"
                    >
                      <template #end="{ tag }">
                        <el-icon class="delete-icon" @click="removeTag(subCategory.id, tag.id)">
                          <Close />
                        </el-icon>
                      </template>
                    </TagsGrid>
                    
                    <!-- 添加替换标签按钮（非编辑状态时显示） -->
                    <div
                      v-if="(!isAddingTag || currentSubCategoryId !== `${subCategory.id}-2`) && !isTagBatchMode[`${subCategory.id}-2`]"
                      class="add-tag" @click="startAddingTag(`${subCategory.id}-2`, 2)">
                      <span>+ 添加标签</span>
                    </div>
                    <!-- 添加标签输入框（编辑状态时显示） -->
                    <div
                      v-else-if="isAddingTag && currentSubCategoryId === `${subCategory.id}-2` && !isTagBatchMode[`${subCategory.id}-2`]"
                      class="tag-input-wrapper">
                      <el-input v-model="newTagContent" placeholder="请输入标签内容" size="small"
                        @keyup.enter="handleAddTag"></el-input>
                      <div class="tag-input-buttons">
                        <el-button size="small" type="primary" @click="handleAddTag">确定</el-button>
                        <el-button size="small" @click="cancelAddingTag">取消</el-button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 右侧：待处理标签和弃用标签 -->
    <div class="right-section">
      <!-- 待处理标签 -->
      <div class="pending-tags-section">
        <TagSection title="多待处理标签" :showSearch="true" searchPlaceholder="搜索多待处理标签" :searchValue="pendingTagSearch"
          @update:searchValue="val => pendingTagSearch = val">
          <template #actions>
            <template v-if="!isMultiSelectMode">
              <el-button type="primary" :icon="Refresh" circle @click="refreshFetchPendingTags" />
              <el-button @click="enableMultiSelect('classify')">批量归类</el-button>
              <el-button type="primary" :disabled="allPendingTagsLoading"
                @click="dealPengdingTags">处理标签{{ rate }}</el-button>
            </template>
            <template v-else>
              <el-button @click="cancelMultiSelect">取消</el-button>
              <el-button type="primary" @click="confirmMultiSelect" v-if="selectedTags.length > 0">
                {{ multiSelectMode === 'classify' ? '选择分类' : '确认弃用' }}
                ({{ selectedTags.length }})
              </el-button>
            </template>
          </template>
        </TagSection>
        <TagsGrid :tags="pendingTags" :isMultiSelectMode="isMultiSelectMode" :activeTagId="activeTagId"
          :loading="pendingTagsLoading" :isTagSelected="isTagSelected" @toggleTagSelection="toggleTagSelection"
          @showTagActions="showTagActions">
          <template #actions="{ tag }">
            <button class="action-btn classify-btn" @click.stop="classifyTag(tag.id)">分类</button>
            <button class="action-btn abandon-btn" @click.stop="abandonTag(tag.id)">弃用</button>
            <button class="action-btn look-btn" @click.stop="lookTag(tag.description)">查看</button>
          </template>
        </TagsGrid>


        <!-- 分页组件 -->
        <Pagination :currentPage="pendingTagsPage" :pageSize="pendingTagsPageSize" :total="pendingTagsTotal"
          @current-change="handlePendingTagsPageChange" @size-change="handlePendingTagsSizeChange" />
      </div>

    </div>


    <!-- 批量新增对话框 -->
    <el-dialog v-model="batchAddDialogVisible" title="批量新增标签" width="40%"
      :before-close="() => batchAddDialogVisible = false">
      <div class="batch-add-input">
        <el-input type="textarea" v-model="batchTagsInput" :rows="5" placeholder="请输入标签内容，每行一个标签"></el-input>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="batchAddDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmBatchAdd">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 标签分类选择弹窗 -->
    <el-dialog v-model="classifyDialogVisible" title="选择分类" width="80%"
      :before-close="() => classifyDialogVisible = false">
      <div class="category-search">
        <el-input v-model="searchClassifyCategory" placeholder="搜索分类名称" prefix-icon="Search" clearable></el-input>
      </div>

      <el-scrollbar height="600px">
        <div class="classify-tree-container">
          <!-- 分类树形结构 -->
          <el-tree ref="categoryTreeRef" :data="categoryTreeData" :props="categoryTreeProps" node-key="id"
            :filter-node-method="filterCategoryNode" highlight-current @node-click="handleCategorySelect">
            <template #default="{ node, data }">
              <span class="custom-tree-node">
                <el-tooltip class="box-item" effect="light" :content="data.description" placement="top-start">
                  <span>{{ node.label }}</span>
                </el-tooltip>
                <!-- <span v-if="data.description" class="node-description"  >
                  ({{ data.description }})
                </span> -->
              </span>
            </template>
          </el-tree>
        </div>
      </el-scrollbar>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="classifyDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmClassify">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 新增分类弹窗 -->
    <el-dialog v-model="categoryDialogVisible" :title="isEditMode ? '修改分类' : '新增分类'" width="80%"
      :before-close="() => categoryDialogVisible = false">
      <div class="category-form">
        <div class="form-item">
          <label><span class="required">*</span> 上级分类</label>
          <el-select v-model="newCategory.pid" placeholder="请选择" style="width: 100%"
            :disabled="isEditMode && newCategory.pid === 0">
            <el-option v-if="newCategory.pid === 0" label="无" :value="0"></el-option>
            <el-option v-for="category in parentCategoryOptions" :key="category.pid" :label="category.name"
              :value="category.pid" :disabled="isEditMode && newCategory.id === category.pid"></el-option>
          </el-select>
        </div>

        <div class="form-item">
          <label><span class="required">*</span> 分类名称</label>
          <el-input v-model="newCategory.name" placeholder="请输入"></el-input>
        </div>

        <div class="form-item">
          <label>备注</label>
          <el-input v-model="newCategory.description" type="textarea" :rows="3" style="height: 150px;"
            placeholder="请输入"></el-input>
        </div>
        <div v-if="newCategory.info">
          <div v-for="(item, index) in newCategory.info">
            <div class="item-list">
              <div class="list-item-s"><strong>子标签{{ index + 1 }}:</strong> <el-tag>{{ item['子标签'] }}</el-tag></div>
              <div class="list-item-s"><strong>标签分类:</strong> <el-tag>{{ item['标签分类'] }}</el-tag> </div>
              <div class="list-item-s"><strong>Fund:</strong> <el-tag>{{ item['Fund'] }}</el-tag></div>
            </div>

          </div>
        </div>

      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="categoryDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleAddCategory">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 查看标签弹窗 -->
    <el-dialog v-model="tagDescriptionDialogVisible" title="查看标签" width="50%"
      :before-close="() => tagDescriptionDialogVisible = false">
      <p>{{ tagDescription }}</p>
    </el-dialog>
    <DealTags ref="dealTagsRef" :tags="allPendingTags" :apiUrl="api_url" />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch, nextTick } from 'vue'
import { ElMessage, ElMessageBox, ElCheckbox } from 'element-plus';
import { Refresh } from '@element-plus/icons-vue'
import { post } from '@/utils/request.js'
import { message } from '@/utils/message'
import c from "@/utils/config";
import TagsGrid from '@/components/TagsGrid.vue'
import Pagination from '@/components/Pagination.vue';
import DealTags from '@/components/DealTags.vue'
import { baseUrl } from '../utils/baseUrl';


// const api_url = c.api_url;
const api_url = baseUrl.url8;

// 状态管理
const searchTagCategory = ref('');
const searchTagContent = ref('');
const expandedCategories = ref([1]); // 默认展开第一个分类
const expandedSubCategories = ref([1, 2]); // 默认展开部分子分类
const tagModalVisible = ref(false);
const newTagContent = ref('');
const currentSubCategoryId = ref(null);
const currentTagType = ref(1); // 当前添加的标签类型，1=对比标签，2=替换标签
const isAddingTag = ref(false); // 是否正在添加标签
const activeTagId = ref(null); // 当前激活的标签 ID，用于显示操作按钮

// 标签批量操作相关状态
const isTagBatchMode = ref({}); // 各子分类的批量模式状态
const tagBatchSelectAll = ref({}); // 各子分类的全选状态
const selectedTagIds = ref({}); // 各子分类选中的标签ID
const loading = ref(false); // 加载状态

// 多选相关状态
const isMultiSelectMode = ref(false); // 是否处于多选模式
const multiSelectMode = ref(''); // 多选模式类型：classify或abandon
const selectedTags = ref([]); // 已选中的标签 ID 数组

// 分类树相关状态
const classifyDialogVisible = ref(false); // 分类选择弹窗显示状态
const searchClassifyCategory = ref(''); // 搜索分类内容
const categoryTreeRef = ref(null); // 分类树引用
const currentClassifyTagId = ref(null); // 当前要分类的标签 ID
const selectedCategoryId = ref(null); // 当前选中的分类 ID

// 通过ai处理待处理标签
const allPendingTags = ref([]); // 所有待处理标签数据
const allPendingTagsLoading = ref(false); // 加载状态
const rate = ref('')
const dealTagsRef = ref(null)

// 拖拽相关函数
const onPendingTagDragStart = (event, tag) => {
  // 这里可以添加拖拽开始时的逻辑
  console.log('拖拽开始:', tag);
};

const onPendingTagDragEnd = () => {
  // 这里可以添加拖拽结束时的逻辑
  console.log('拖拽结束');
};
function dealPengdingTags() {
  if (!allPendingTags.value.length) {
    ElMessage.error('没有待处理标签')
    return
  }
  dealTagsRef.value.open()
}

const getAllPendingNewTags = async () => {

  allPendingTagsLoading.value = true;
  try {
    const result = await post(`${api_url}/tag/getPendingTags`, JSON.stringify({
      page: pendingTagsPage.value,
      pageSize: 9999,
      searchText: '',
      type: "2",
      is_all: "1"
    }));
    if (result.error_code === 0 && result.data) {
      allPendingTagsLoading.value = false;
      allPendingTags.value = result.data.list
    } else {
      ElMessage.error('获取所以待处理标签数据失败');
      allPendingTagsLoading.value = false;
    }
  } catch (error) {
    console.error('获取所以待处理标签异常:', error);
    ElMessage.error('获取所以待处理标签异常');
    allPendingTagsLoading.value = false;
  }
}

// loading管理
const loadStatus = ref({
  treeLoad: true,
  pendingLoad: true,
});

// 分类树属性定义
const categoryTreeProps = {
  label: 'name',
  children: 'children'
};

// 分类树数据，基于 categories.value 转换而来
const categoryTreeData = computed(() => {
  // 深拷贝分类数据，不包含标签数组
  return categories.value.map(category => {
    const categoryData = {
      id: category.id,
      name: category.name,
      pid: category.pid || 0,
      description: category.description || ''
    };

    // 处理子分类，但不包含标签数组
    if (category.children && category.children.length > 0) {
      categoryData.children = category.children.map(child => ({
        id: child.id,
        name: child.name,
        pid: child.pid,
        description: child.description || '',
        isLeaf: true // 标记为叶子节点
      }));
    }

    return categoryData;
  });
});

// 监听搜索内容变化，过滤树节点
watch(searchClassifyCategory, (val) => {
  categoryTreeRef.value?.filter(val);
});

// 批量新增相关状态
const batchAddDialogVisible = ref(false); // 批量新增对话框显示状态
const batchTagsInput = ref(''); // 批量新增标签的输入内容

// 新增分类相关状态
const categoryDialogVisible = ref(false); // 分类弹窗显示状态
const isEditMode = ref(false); // 是否处于编辑模式
const newCategory = ref({
  id: null, // 编辑时使用的ID
  pid: 0, // 默认上级分类为无（pid=0）
  name: '',
  description: ''
});

// 父分类选项，从 getCategoryTree 接口获取的数据中提取
const parentCategoryOptions = computed(() => {
  // 筛选可用的父分类选项
  // 在编辑模式下，需要排除当前编辑的分类及其子分类
  if (isEditMode.value && newCategory.value.id) {
    // 排除自身及其子分类，避免循环引用
    return categories.value
      .filter(category => category.id !== newCategory.value.id)
      .map(category => ({
        name: category.name,  // key 使用 name
        pid: category.id      // value 使用 pid
      }));
  }

  // 新增模式下返回全部分类
  return categories.value.map(category => ({
    name: category.name,  // key 使用 name
    pid: category.id      // value 使用 pid
  }));
});

// 从接口获取标签分类数据
const categories = ref([]);

// 获取标签分类树结构
const fetchCategoryTree = async () => {
  try {
    let url = `${api_url}/tags/getCategoryTree`

    const result = await post(url, JSON.stringify({}));

    result.data = [
      {
        "id": 11308111,
        "name": "否认幻觉、幻听",
        "description": "",
        "pid": 0,
        "tags": [],
        "children": result.data
      }
    ]
    console.log(result)


    if (result.error_code === 0 && result.data) {
      // 处理返回的数据，添加必要的属性
      const processedCategories = result.data.map(category => {
        // 处理主分类
        const categoryChildren = [];
        // 如果原始数据中有子分类，处理这些子分类
        if (category.children && category.children.length > 0) {
          category.children.forEach(child => {
            // 处理子分类
            categoryChildren.push({
              id: child.id,
              name: child.name,
              pid: child.pid,
              type: child.type || 1, // 默认类型
              typeName: child.type === 2 ? '替换标签' : '对比标签', // 根据类型设置名称
              tags: child.tags || [], // 如果没有tags属性，使用空数组
              description: child.description || ''
            });
          });
        }

        return {
          id: category.id,
          name: category.name,
          pid: category.pid || 0,
          description: category.description,
          count: categoryChildren.length,
          children: categoryChildren
        };
      });

      // 注意：我们在前端展示对比标签和替换标签时，直接在子分类下都显示两种标签类型的区域
      // 标签类型通过标签本身的type属性来区分，而非创建不同类型的子分类
      categories.value = processedCategories;
      loadStatus.value.treeLoad = false;


    } else {
      ElMessage.error('获取标签分类数据失败');
      loadStatus.value.treeLoad = false;

    }
  } catch (error) {
    console.error('获取标签分类数据异常:', error);
    ElMessage.error('获取标签分类数据异常');
    loadStatus.value.treeLoad = false;

  }
};

// 待处理标签搜索
const pendingTagSearch = ref('');

// 使用监听器监听pendingTagSearch的变化，触发后端搜索
watch(pendingTagSearch, (newVal, oldVal) => {
  // 重置页码，确保从第一页开始搜索
  pendingTagsPage.value = 1;
  // 使用节流函数调用后端搜索，减少频繁请求
  const debouncedSearch = setTimeout(() => {
    fetchPendingTags();
  }, 1000);

  return () => clearTimeout(debouncedSearch);
});

// 使用onMounted生命周期钩子在组件挂载时加载数据
onMounted(() => {
  fetchCategoryTree();
  fetchPendingTags();
  getAllPendingNewTags()
});

// 待处理标签相关数据
const pendingTags = ref([]); // 待处理标签数据
const pendingTagsLoading = ref(false); // 加载状态
const pendingTagsPage = ref(1); // 当前页码
const pendingTagsPageSize = ref(10); // 每页记录数
const pendingTagsTotal = ref(0); // 总记录数

const refreshFetchPendingTags = () => {
  pendingTagsPage.value = 1;
  fetchPendingTags();
}
// 获取待处理标签
const fetchPendingTags = async () => {
  if (pendingTagsLoading.value) return;

  pendingTagsLoading.value = true;
  try {
    let url = `${api_url}/tag/getPendingTags`
    const result = await post(url, JSON.stringify({
      page: pendingTagsPage.value,
      pageSize: pendingTagsPageSize.value,
      searchText: pendingTagSearch.value,
      type: "2",
      is_all: "1"
    }));
    if (result.error_code === 0 && result.data) {
      // 处理返回的数据，添加selected属性以支持多选功能
      const newTags = result.data.list.map(tag => {
        // 检查当前标签是否在选中数组中
        const isSelected = isMultiSelectMode.value && selectedTags.value.includes(tag.id);

        return {
          id: tag.id,
          content: tag.name, // 将name存入content以兼容现有代码
          name: tag.name,    // 保留原始属性
          description: tag.description || '',
          create_time: tag.create_time,
          selected: isSelected  // 根据选中数组设置状态
        };
      });

      // 设置数据和总数
      pendingTags.value = newTags;
      pendingTagsTotal.value = result.data.total || 0;

    } else {
      ElMessage.error('获取待处理标签数据失败');
    }
  } catch (error) {
    console.error('获取待处理标签异常:', error);
    ElMessage.error('获取待处理标签异常');
  } finally {
    pendingTagsLoading.value = false;
  }
};

// 处理每页记录数变化
const handlePendingTagsSizeChange = (size) => {
  pendingTagsPageSize.value = size;
  pendingTagsPage.value = 1; // 重置为第一页
  fetchPendingTags();
};

// 处理页码变化
const handlePendingTagsPageChange = (page) => {
  pendingTagsPage.value = page;
  fetchPendingTags();
};

// 重置待处理标签列表
const resetPendingTags = () => {
  pendingTags.value = [];
  pendingTagsPage.value = 1;
  pendingTagsTotal.value = 0;
  pendingTagSearch.value = '';
  fetchPendingTags();
};

// 启用多选模式
const enableMultiSelect = (mode) => {

  isMultiSelectMode.value = true;
  multiSelectMode.value = mode;
  selectedTags.value = []; // 清空已选标签列表

  // 重置所有标签的选中状态
  pendingTags.value.forEach(tag => {
    tag.selected = false;
  });
};

// 取消多选模式
const cancelMultiSelect = () => {
  isMultiSelectMode.value = false;
  multiSelectMode.value = '';
  selectedTags.value = []; // 清空已选标签列表

  // 重置所有标签的选中状态
  pendingTags.value.forEach(tag => {
    tag.selected = false;
  });
};

// 切换标签选中状态
const toggleTagSelection = (tagId) => {
  console.log('toggleTagSelection', tagId);
  // 查找对应的标签
  const tag = pendingTags.value.find(t => t.id === tagId);
  if (tag) {
    // 设置新的状态 - 取反当前状态
    const newState = !tag.selected;
    tag.selected = newState;

    // 更新已选标签列表
    if (newState) {
      if (!selectedTags.value.includes(tagId)) {
        selectedTags.value.push(tagId);
      }
    } else {
      selectedTags.value = selectedTags.value.filter(id => id !== tagId);
    }
  }
};

// 检查标签是否被选中
const isTagSelected = (tagId) => {
  return pendingTags.value.find(t => t.id === tagId)?.selected || false;
};

// 确认多选操作
const confirmMultiSelect = () => {
  if (selectedTags.value.length === 0) {
    ElMessage.warning('请至少选择一个标签');
    return;
  }

  if (multiSelectMode.value === 'classify') {
    // 批量分类操作 - 打开分类选择对话框
    // 记录当前选中的标签ID数组
    currentBatchClassifyTagIds = [...selectedTags.value];
    selectedCategoryId.value = null; // 重置选中的分类

    // 重置搜索内容
    searchClassifyCategory.value = '';
    if (categoryTreeRef.value) {
      categoryTreeRef.value.filter('');
    }

    // 显示分类对话框
    classifyDialogVisible.value = true;
  } else if (multiSelectMode.value === 'abandon') {
    // 批量弃用操作
    batchAbandonTags();
  }
};


// 存储当前批量分类的标签ID数组
let currentBatchClassifyTagIds = [];

// 删除待处理标签（单个或批量）
const deletePendingTag = async (tagIds) => {
  // 判断是单个标签还是多个标签
  const isBatch = Array.isArray(tagIds);
  const ids = isBatch ? tagIds : [tagIds];

  // 如果是单个标签，找到标签信息
  let tagContent = '';
  if (!isBatch) {
    const tagToAbandon = pendingTags.value.find(tag => tag.id === tagIds);
    if (tagToAbandon) {
      tagContent = tagToAbandon.content;
    }
  }

  // 确认提示信息
  const confirmMessage = isBatch
    ? `确定要弃用选中的 ${ids.length} 个标签吗？`
    : `确定要弃用标签「${tagContent}」吗？`;

  ElMessageBox.confirm(
    confirmMessage,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(async () => {
      try {
        // 调用弃用标签接口
        const result = await post(`${api_url}/tags/deletePendingTag`, JSON.stringify({
          ids: ids
        }));

        if (result.error_code === 0) {
          // 成功提示
          const successMessage = isBatch
            ? `已成功弃用 ${ids.length} 个标签`
            : '标签已成功弃用';
          ElMessage.success(successMessage);

          // 刷新待处理标签数据
          resetPendingTags();

          // 如果是批量操作，退出多选模式
          if (isBatch) {
            cancelMultiSelect();
          } else {
            // 关闭操作按钮
            activeTagId.value = null;
          }
        } else {
          const errorMessage = isBatch ? '批量弃用标签失败' : '弃用标签失败';
          ElMessage.error(result.msg || errorMessage);

          // 如果是单个操作，关闭操作按钮
          if (!isBatch) {
            activeTagId.value = null;
          }
        }
      } catch (error) {
        console.error('弃用标签异常:', error);
        const errorMessage = isBatch ? '批量弃用标签异常' : '弃用标签异常';
        ElMessage.error(errorMessage);

        // 如果是单个操作，关闭操作按钮
        if (!isBatch) {
          activeTagId.value = null;
        }
      }
    })
    .catch(() => {
      // 用户取消操作
    });
};

// 批量弃用标签
const batchAbandonTags = async () => {
  deletePendingTag(selectedTags.value);
};





// 计算属性 - 综合考虑分类和标签内容的筛选
// 筛选二级分类的方法
const getFilteredSubCategories = (category) => {
  const categorySearchTerm = searchTagCategory.value.trim().toLowerCase();
  const tagSearchTerm = searchTagContent.value.trim().toLowerCase();

  // 如果没有搜索条件，返回所有二级分类
  if (!categorySearchTerm && !tagSearchTerm) {
    return category.children || [];
  }

  // 有搜索条件时，筛选匹配的二级分类
  return (category.children || []).filter(subCategory => {
    // 检查二级分类名称是否匹配
    const subCategoryMatches = categorySearchTerm ?
      subCategory.name.toLowerCase().includes(categorySearchTerm) : true;

    // 如果没有标签内容搜索，只考虑二级分类名称
    if (!tagSearchTerm) {
      return subCategoryMatches;
    }

    // 如果有标签内容搜索，检查是否有匹配的标签
    let hasMatchingTag = false;
    if (subCategory.tags && subCategory.tags.length > 0) {
      hasMatchingTag = subCategory.tags.some(tag =>
        tag.name && tag.name.toLowerCase().includes(tagSearchTerm)
      );
    }

    // 如果只搜索标签内容，则只返回包含匹配标签的分类
    if (tagSearchTerm && !categorySearchTerm) {
      return hasMatchingTag;
    }

    // 如果同时搜索分类名称和标签内容，则返回同时满足两个条件的结果
    if (categorySearchTerm && tagSearchTerm) {
      return subCategoryMatches && hasMatchingTag;
    }

    // 其他情况
    return subCategoryMatches;
  });
};

const filteredCategories = computed(() => {
  const categorySearchTerm = searchTagCategory.value.trim().toLowerCase();
  const tagSearchTerm = searchTagContent.value.trim().toLowerCase();

  // 如果没有输入任何搜索条件，返回所有分类
  if (!categorySearchTerm && !tagSearchTerm) {
    return categories.value;
  }

  return categories.value.filter(category => {
    // 分类名称筛选（一级分类）
    const categoryMatches = categorySearchTerm ?
      category.name.toLowerCase().includes(categorySearchTerm) : true;

    // 检查是否有匹配的二级分类
    const filteredSubCategories = getFilteredSubCategories(category);
    const hasMatchingSubCategories = filteredSubCategories.length > 0;

    // 如果只搜索标签内容，只返回有匹配标签的二级分类的一级分类
    if (tagSearchTerm && !categorySearchTerm) {
      return hasMatchingSubCategories;
    }

    // 如果只搜索分类名称，返回分类名称匹配的一级分类或者有匹配二级分类的一级分类
    if (categorySearchTerm && !tagSearchTerm) {
      return categoryMatches || hasMatchingSubCategories;
    }

    // 如果同时搜索分类名称和标签内容，返回分类名称匹配并且有匹配标签的二级分类的一级分类
    if (categorySearchTerm && tagSearchTerm) {
      // 如果一级分类名称匹配，则返回该分类
      if (categoryMatches) {
        return true;
      }
      // 否则检查是否有匹配的二级分类
      return hasMatchingSubCategories;
    }

    // 默认情况
    return false;
  });
});

// 根据类型获取标签
const getTagsByType = (tags, type) => {
  return tags.filter(tag => tag.type === type);
};

// 格式化标签数据以适配TagsGrid组件
const getFormattedTagsForGrid = (tags, subCategoryId, tagType) => {
  return tags.map(tag => ({
    id: tag.id,
    content: tag.name, // TagsGrid组件使用content属性显示标签内容
    name: tag.name,    // 保留原始name属性
    selected: tag.selected || false,
    subCategoryId,
    tagType
  }));
};

// 检查标签是否在批量选择中被选中
const isTagSelectedInBatch = (subCategoryId, tagType, tagId) => {
  const key = `${subCategoryId}-${tagType}`;
  return selectedTagIds.value[key]?.includes(tagId) || false;
};

// 获取筛选后的标签
const getFilteredTags = (tags) => {
  if (!searchTagContent.value.trim()) {
    return tags;
  }
  const tagSearchTerm = searchTagContent.value.trim().toLowerCase();
  return tags.filter(tag =>
    tag.name.toLowerCase().includes(tagSearchTerm)
  );
};

// 方法
const toggleCategory = (categoryId) => {
  if (expandedCategories.value.includes(categoryId)) {
    expandedCategories.value = expandedCategories.value.filter(id => id !== categoryId);
  } else {
    expandedCategories.value.push(categoryId);
  }
};

const toggleSubCategory = (subCategoryId) => {
  if (expandedSubCategories.value.includes(subCategoryId)) {
    expandedSubCategories.value = expandedSubCategories.value.filter(id => id !== subCategoryId);
  } else {
    expandedSubCategories.value.push(subCategoryId);
  }
};

const removeTag = async (subCategoryId, tagId) => {
  ElMessageBox.confirm(
    `确定要删除吗？删除后将无法恢复。`,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
  .then(async () => {
    try {
      // 调用接口删除标签
      const result = await post(`${api_url}/tags/deleteTag`, JSON.stringify({
        id: tagId
      }));
  
      if (result.error_code === 0) {
        ElMessage.success('标签已删除');
  
        // 重新获取分类数据，刷新标签列表
        await fetchCategoryTree();
      } else {
        ElMessage.error(result.msg || '标签删除失败');
      }
    } catch (error) {
      console.error('删除标签异常:', error);
      ElMessage.error('删除标签异常');
    }
  })
  .catch(() => {
    // 用户取消删除操作
  });
};

// 开始添加标签（点击添加标签按钮时调用）
const startAddingTag = (subCategoryId, tagType) => {
  currentSubCategoryId.value = subCategoryId;
  currentTagType.value = tagType || 1; // 默认为对比标签类型
  newTagContent.value = '';
  isAddingTag.value = true;
};

// 取消添加标签
const cancelAddingTag = () => {
  isAddingTag.value = false;
  newTagContent.value = '';
};


// 显示批量新增对话框
const showBatchAddDialog = () => {
  // 重置输入内容
  batchTagsInput.value = '';

  // 显示对话框
  batchAddDialogVisible.value = true;
};

// 显示新增分类弹窗
const showCategoryDialog = () => {
  // 重置表单
  newCategory.value = {
    pid: 0,
    name: '',
    description: ''
  };

  // 标记为新增模式
  isEditMode.value = false;

  // 显示弹窗
  categoryDialogVisible.value = true;
};

// 打开编辑分类弹窗
const editCategory = (category) => {
  // 设置为编辑模式并填充现有分类数据
  isEditMode.value = true;
  newCategory.value = {
    id: category.id,
    pid: category.pid || 0,
    name: category.name,
    description: category.description || '{\n"标签描述":"xxxx",\n"近义词":"xxxx"\n}',
    info: category.description ? JSON.parse(category.description) : []
  };


  // 如果是根节点，在控制台输出提示信息
  if (category.pid === 0) {
    console.log('正在编辑根节点，上级分类将被禁用');
  }

  // 显示弹窗
  categoryDialogVisible.value = true;
};

// 确认删除分类
const confirmDeleteCategory = (categoryId, categoryName) => {
  ElMessageBox.confirm(
    `确定要删除分类「${categoryName}」吗？删除后将无法恢复。`,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(() => {
      deleteCategory(categoryId);
    })
    .catch(() => {
      // 用户取消删除操作
    });
};

// 执行删除分类
const deleteCategory = async (categoryId) => {
  try {
    // 调用接口删除分类
    const result = await post(`${api_url}/tags/deleteCategory`, JSON.stringify({
      id: categoryId
    }));

    if (result.error_code === 0) {
      ElMessage.success('分类删除成功');
      // 重新获取分类数据
      fetchCategoryTree();
    } else {
      ElMessage.error(result.msg || '分类删除失败');
    }
  } catch (error) {
    console.error('删除分类异常:', error);
    ElMessage.error('删除分类异常');
  }
};

// 自定义节流函数
const throttle = (fn, delay) => {
  let lastCall = 0;
  return function (...args) {
    const now = new Date().getTime();
    if (now - lastCall < delay) {
      return;
    }
    lastCall = now;
    return fn.apply(this, args);
  };
};

// 处理添加或更新分类的原始函数
const _handleAddCategory = async () => {
  // 验证分类名称
  if (!newCategory.value.name.trim()) {
    ElMessage.error('请输入分类名称');
    return;
  }

  try {
    // 准备请求数据
    const requestData = {
      pid: newCategory.value.pid,
      name: newCategory.value.name.trim(),
      description: newCategory.value.description.trim()
    };

    // 如果是编辑模式，添加id参数并调用更新接口
    let apiUrl = `${api_url}/tags/createCategory`;
    let successMessage = '分类添加成功';

    if (isEditMode.value && newCategory.value.id) {
      apiUrl = `${api_url}/tags/updateCategory`;
      requestData.id = newCategory.value.id;
      successMessage = '分类更新成功';
    }

    // 发送请求
    const result = await post(apiUrl, JSON.stringify(requestData));

    if (result.error_code === 0) {
      ElMessage.success(successMessage);
      // 关闭弹窗
      categoryDialogVisible.value = false;
      // 重新加载分类数据
      fetchCategoryTree();
    } else {
      ElMessage.error(result.msg || (isEditMode.value ? '分类更新失败' : '分类添加失败'));
    }
  } catch (error) {
    console.error(isEditMode.value ? '更新分类异常:' : '添加分类异常:', error);
    ElMessage.error(isEditMode.value ? '更新分类异常' : '添加分类异常');
  }
};

// 使用节流处理函数，限制1秒内只能调用一次
const handleAddCategory = throttle(_handleAddCategory, 1000);

// 开始标签批量模式
function startTagBatchMode(subCategoryId, tagType) {
  // 初始化该分类和标签类型的批量选择状态
  isTagBatchMode.value[`${subCategoryId}-${tagType}`] = true;
  tagBatchSelectAll.value[`${subCategoryId}-${tagType}`] = false;
  selectedTagIds.value[`${subCategoryId}-${tagType}`] = [];

  // 初始化标签的选中状态
  const subCategory = getSubCategoryById(subCategoryId);
  if (subCategory && subCategory.tags) {
    const tags = getTagsByType(subCategory.tags, tagType);
    tags.forEach(tag => {
      tag.selected = false;
    });
  }
}

// 取消标签批量模式
function cancelTagBatchMode(subCategoryId, tagType) {
  isTagBatchMode.value[`${subCategoryId}-${tagType}`] = false;
  selectedTagIds.value[`${subCategoryId}-${tagType}`] = [];
}

// 切换标签在批量选择中的状态
function toggleTagInBatch(subCategoryId, tagType, tagId) {
  if (!selectedTagIds.value[`${subCategoryId}-${tagType}`]) {
    selectedTagIds.value[`${subCategoryId}-${tagType}`] = [];
  }

  const subCategory = getSubCategoryById(subCategoryId);
  if (subCategory && subCategory.tags) {
    const tags = getTagsByType(subCategory.tags, tagType);
    const tag = tags.find(t => t.id === tagId);

    if (tag && tag.selected) {
      // 如果标签被选中，添加到选中列表
      if (!selectedTagIds.value[`${subCategoryId}-${tagType}`].includes(tagId)) {
        selectedTagIds.value[`${subCategoryId}-${tagType}`].push(tagId);
      }
    } else {
      // 如果标签取消选中，从选中列表中移除
      selectedTagIds.value[`${subCategoryId}-${tagType}`] =
        selectedTagIds.value[`${subCategoryId}-${tagType}`].filter(id => id !== tagId);

      // 如果有标签取消选中，全选状态也要取消
      tagBatchSelectAll.value[`${subCategoryId}-${tagType}`] = false;
    }
  }
}

// 全选/取消全选标签
function toggleSelectAllTags(subCategoryId, tagType) {
  const isSelectAll = tagBatchSelectAll.value[`${subCategoryId}-${tagType}`];
  const subCategory = getSubCategoryById(subCategoryId);

  if (subCategory && subCategory.tags) {
    const tags = getTagsByType(subCategory.tags, tagType);

    // 更新所有标签的选中状态
    tags.forEach(tag => {
      tag.selected = isSelectAll;
    });

    // 更新选中的标签ID列表
    if (isSelectAll) {
      selectedTagIds.value[`${subCategoryId}-${tagType}`] = tags.map(tag => tag.id);
    } else {
      selectedTagIds.value[`${subCategoryId}-${tagType}`] = [];
    }
  }
}

// 检查是否有选中的标签
function hasSelectedTags(subCategoryId, tagType) {
  return selectedTagIds.value[`${subCategoryId}-${tagType}`]?.length > 0;
}

// 根据ID获取子分类
function getSubCategoryById(subCategoryId) {
  for (const category of categories.value) {
    if (category.children) {
      const subCategory = category.children.find(sub => sub.id === subCategoryId);
      if (subCategory) return subCategory;
    }
  }
  return null;
}

// 批量删除标签
async function batchDeleteTags(subCategoryId, tagType) {
  const selectedIds = selectedTagIds.value[`${subCategoryId}-${tagType}`];
  if (!selectedIds || selectedIds.length === 0) {
    ElMessage.warning('请至少选择一个标签');
    return;
  }

  try {
    await ElMessageBox.confirm(`确定要删除选中的 ${selectedIds.length} 个标签吗？`, '批量删除标签', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    });

    loading.value = true;
    const tagIdsString = selectedIds.join(',');

    try {
      const result = await post(`${api_url}/tags/deleteTag`, JSON.stringify({
        id: tagIdsString
      }));

      if (result.error_code === 0) {
        ElMessage.success('批量删除标签成功');
        // 重新加载分类树
        await fetchCategoryTree();
        // 退出批量模式
        cancelTagBatchMode(subCategoryId, tagType);
      } else {
        ElMessage.error(result.msg || '批量删除标签失败');
      }
    } catch (error) {
      console.error('批量删除标签出错:', error);
      ElMessage.error('操作失败，请稍后重试');
    } finally {
      loading.value = false;
    }
  } catch {
    // 用户取消删除操作
  }
}

const handleAddTag = async () => {
  if (!newTagContent.value.trim()) {
    ElMessage.error('请输入标签内容');
    return;
  }

  // 从当前子分类ID中提取真实ID和标签类型
  let realSubCategoryId = currentSubCategoryId.value;
  let tagType = currentTagType.value;

  // 如果当前ID包含类型信息，则提取
  if (typeof currentSubCategoryId.value === 'string' && currentSubCategoryId.value.includes('-')) {
    const parts = currentSubCategoryId.value.split('-');
    realSubCategoryId = parseInt(parts[0]);
    tagType = parseInt(parts[1]);
  }

  try {
    // 调用接口添加标签
    const result = await post(`${api_url}/tags/createTag`, JSON.stringify({
      name: newTagContent.value.trim(),
      category_id: realSubCategoryId,
      type: tagType // 1：对比标签，2：替换标签
    }));

    if (result.error_code === 0) {
      ElMessage.success('标签添加成功');

      // 重新获取所有相关数据
      await fetchCategoryTree();
      await fetchPendingTags();
    } else {
      ElMessage.error(result.msg || '标签添加失败');
    }
  } catch (error) {
    console.error('添加标签异常:', error);
    ElMessage.error('添加标签异常');
  }

  // 重置状态（既适用于弹窗也适用于直接编辑模式）
  tagModalVisible.value = false;
  isAddingTag.value = false;
  newTagContent.value = '';
};

// 全局点击处理函数
let closeHandler = null;

// 显示标签操作按钮
const showTagActions = (event, tagId) => {
  console.log('点击标签:', tagId);
  
  // 防止冲突，阻止事件冒泡
  event.stopPropagation();

  // 如果点击的是当前已激活的标签，则关闭操作按钮
  if (activeTagId.value === tagId) {
    activeTagId.value = null;
    // 移除先前的监听器
    if (closeHandler) {
      document.removeEventListener('click', closeHandler);
      closeHandler = null;
    }
  } else {
    // 先关闭其它标签的操作按钮，再显示当前标签的操作按钮
    activeTagId.value = tagId;

    // 移除先前的监听器
    if (closeHandler) {
      document.removeEventListener('click', closeHandler);
    }

    // 添加新的点击其他区域关闭按钮的处理
    setTimeout(() => {
      closeHandler = () => {
        activeTagId.value = null;
        document.removeEventListener('click', closeHandler);
        closeHandler = null;
      };
      document.addEventListener('click', closeHandler);
    }, 10);
  }
};

// 标签树节点过滤器
const filterCategoryNode = (value, data) => {
  if (!value) return true;
  return data.name.toLowerCase().includes(value.toLowerCase()) ||
    (data.description && data.description.toLowerCase().includes(value.toLowerCase()));
};

// 处理分类选择
const handleCategorySelect = (data) => {
  // 如果是可选分类，记录当前选中的分类 ID
  selectedCategoryId.value = data.id;
  console.log('选择分类:', data.name, 'ID:', data.id);
};

// 确认批量新增
const confirmBatchAdd = () => {
  // 验证输入内容
  if (!batchTagsInput.value.trim()) {
    ElMessage.error('请输入标签内容');
    return;
  }

  // 关闭批量新增对话框，打开分类选择对话框
  batchAddDialogVisible.value = false;

  // 重置搜索内容
  searchClassifyCategory.value = '';
  if (categoryTreeRef.value) {
    categoryTreeRef.value.filter('');
  }

  // 显示分类对话框
  classifyDialogVisible.value = true;
};

// 确认分类
const confirmClassify = async () => {
  if (!selectedCategoryId.value) {
    ElMessage.warning('请选择一个分类');
    return;
  }

  // 判断处理类型：批量新增、批量分类或单个分类
  // 如果有批量新增的输入，则优先处理批量新增
  if (batchTagsInput.value.trim()) {
    try {
      // 直接发送标签字符串
      const result = await post(`${api_url}/tags/createTag`, JSON.stringify({
        name: batchTagsInput.value.trim(),
        category_id: selectedCategoryId.value,
        type: 1 // 默认为对比标签类型
      }));
      if (result.error_code === 0) {
        ElMessage.success('标签批量创建成功');

        // 重置批量标签输入
        batchTagsInput.value = '';

        // 刷新所有相关数据
        await fetchCategoryTree();
        await fetchPendingTags();
      } else {
        ElMessage.error(result.msg || '批量创建标签失败');
        batchTagsInput.value = '';
      }

      // 关闭分类对话框
      classifyDialogVisible.value = false;

      return; // 处理完批量新增后直接返回，不继续处理其他情况

    } catch (error) {
      console.error('批量新增标签异常:', error);
      ElMessage.error('批量新增标签异常');
      // 关闭分类对话框
      batchTagsInput.value = '';
      classifyDialogVisible.value = false;
      return;
    }
  }

  // 处理其他类型的分类操作
  let tagIds = [];

  // 如果是批量分类操作
  if (currentBatchClassifyTagIds.length > 0) {
    tagIds = currentBatchClassifyTagIds;
  }
  // 如果是单个标签分类操作
  else if (currentClassifyTagId.value) {
    tagIds = [currentClassifyTagId.value];
  } else {
    ElMessage.error('缺少标签信息');
    return;
  }

  try {
    // 调用分类标签接口
    const result = await post(`${api_url}/tags/updatePendingTag`, JSON.stringify({
      ids: tagIds,
      category_id: selectedCategoryId.value
    }));

    if (result.error_code === 0) {
      // 根据处理模式显示不同的成功消息
      if (currentBatchClassifyTagIds.length > 0) {
        ElMessage.success(`已成功分类 ${currentBatchClassifyTagIds.length} 个标签`);
        // 清空批量处理数组
        currentBatchClassifyTagIds = [];
        // 退出多选模式
        cancelMultiSelect();
      } else {
        ElMessage.success('标签分类成功');
      }

      // 关闭分类对话框
      classifyDialogVisible.value = false;
      // 刷新所有相关数据
      fetchPendingTags();
      // 刷新分类树结构数据
      fetchCategoryTree();
    } else {
      ElMessage.error(result.msg || '标签分类失败');
    }
  } catch (error) {
    console.error('分类标签异常:', error);
    ElMessage.error('分类标签异常');
  }
};

// 打开分类标签对话框
const classifyTag = (tagId) => {
  // 记录当前标签 ID
  currentClassifyTagId.value = tagId;

  // 重置搜索内容
  searchClassifyCategory.value = '';
  if (categoryTreeRef.value) {
    categoryTreeRef.value.filter('');
  }

  // 显示分类对话框
  classifyDialogVisible.value = true;

  // 关闭操作按钮
  activeTagId.value = null;
};

// 弃用标签
const abandonTag = async (tagId) => {
  deletePendingTag(tagId);
};

const tagDescriptionDialogVisible = ref(false); // 标签描述对话框显示状态
const tagDescription = ref({}); // 标签描述内容
// 查看标签
const lookTag = async (description) => {
  // 如果描述是JSON格式，尝试解析
  let parsedDescription = {};
  try {
    parsedDescription = JSON.parse(description);
  } catch (e) {
    console.warn('标签描述不是有效的JSON格式:', description);
  }
  console.log('查看标签描述:', parsedDescription);

  // 显示标签描述对话框
  tagDescriptionDialogVisible.value = true;
  tagDescription.value = description;
};




</script>

<style scoped>
.item-list {
  display: flex;
}

.list-item-s {
  margin-left: 15px;
  margin-bottom: 15px;
}

/* 基础样式 */
body {
  margin: 0;
  padding: 0;
}

/* 新增分类表单样式 */
.category-form {
  padding: 10px;
}

.form-item {
  margin-bottom: 20px;
}

.form-item label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
}

.required {
  color: #f56c6c;
  margin-right: 4px;
}

.tag-library-container {
  display: flex;
  width: 100%;
  height: 100%;
  /* padding: 20px; */
  background-color: #f5f5f5;
  gap: 20px;

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;

    h3 {
      margin: 0;
      font-size: 16px;
    }

    .header-actions {
      display: flex;
      gap: 10px;
    }
  }

  /* 左侧区域 */
  .used-tags-section {
    flex: 1;
    background-color: white;
    border-radius: 4px;
    padding: 15px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

    .search-bar {
      display: flex;
      gap: 10px;
      margin-bottom: 15px;
    }

    .category-list {
      max-height: 90%;
      height: 90%;
      overflow-y: scroll;

      .category-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 10px;
        margin-bottom: 5px;
        border-radius: 4px;
        transition: background-color 0.2s ease;

        &:hover {
          background-color: #f9f9f9;
        }

        .category-info {
          display: flex;
          align-items: center;
          cursor: pointer;
          flex: 1;

          .expand-icon {
            margin-right: 10px;
            font-size: 12px;
          }
        }

        .category-actions {
          display: flex;
          gap: 5px;
          opacity: 0;
          transition: opacity 0.2s ease;
        }

        &:hover .category-actions {
          opacity: 1;
        }

        .tag-count {
          background-color: #ffcc00;
          color: white;
          border-radius: 50%;
          width: 20px;
          height: 20px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 12px;
          margin: 0 10px;
        }
      }

      .subcategory-list {
        padding-left: 20px;
        margin-bottom: 10px;

        .subcategory-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 8px;
          margin-bottom: 5px;
          border-radius: 4px;
          transition: background-color 0.2s ease;

          &:hover {
            background-color: #f9f9f9;
          }

          .subcategory-info {
            display: flex;
            align-items: center;
            cursor: pointer;
            flex: 1;

            .expand-icon {
              margin-right: 10px;
              font-size: 12px;
            }
          }

          .subcategory-actions {
            display: flex;
            gap: 5px;
            opacity: 0;
            transition: opacity 0.2s ease;
          }

          &:hover .subcategory-actions {
            opacity: 1;
          }
        }

        .tag-list {
          padding-left: 20px;
          margin-bottom: 10px;
          background-color: #f9f9f9;
          border-radius: 6px;
          padding: 15px 20px;

          .tag-group {
            margin-bottom: 20px;
            position: relative;
            border-radius: 6px;
            padding-left: 40px;

            /* 为垂直标签留出空间 */
            &.tag-group-1 {
              margin-bottom: 30px;

              .tag-type-indicator {
                background-color: #f5a623;
              }
            }

            &.tag-group-2 {
              .tag-type-indicator {
                background-color: #50e3c2;
              }
            }

            .tag-type-indicator {
              writing-mode: vertical-lr;
              display: flex;
              align-items: center;
              justify-content: center;
              padding: 8px 4px;
              color: white;
              border-radius: 4px;
              position: absolute;
              left: 0;
              top: 0;
              height: 100%;
              font-size: 14px;
            }

            .batch-actions-row {
              display: flex;
              align-items: center;
              gap: 8px;
              padding: 8px;
              background-color: #f5f5f5;
              border-top: 1px solid #eee;
              border-bottom: 1px solid #eee;
            }

            .tags-wrapper {
              /* display: flex; */
              flex-direction: column;
              margin-top: 0;
              padding: 15px;
              background-color: #f9f9f9;
              border-radius: 0 0 4px 4px;
              /* min-height: 300px;  */

              /* TagsGrid组件样式覆盖 - 移除自定义样式，完全跟随TagsGrid */
              :deep(.pending-tags-grid) {
                padding: 0;
                margin-bottom: 10px;
              }

              .add-tag {
                display: flex;
                align-items: center;
                justify-content: center;
                background-color: #e8f7ff;
                border-radius: 4px;
                padding: 8px 12px;
                height: 18px;
                margin-bottom: 8px;
                cursor: pointer;
                color: #1890ff;
                border: 1px dashed #1890ff;
              }

              .tag-input-wrapper {
                display: flex;
                height: 35px;

                .tag-input-buttons {
                  display: flex;
                  padding-left: 10px;
                  gap: 10px;

                  .el-button {
                    margin-left: 0;
                    height: 35px;
                    width: 70px;
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  /* 右侧区域 */
  .right-section {
    width: 40%;
    display: flex;
    flex-direction: column;
    gap: 20px;
    height: 100%;

    /* 待处理标签 */
    .pending-tags-section {
      background-color: white;
      border-radius: 4px;
      padding: 15px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      height: 100%;

      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

      .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;


        h3 {
          margin: 0;
          font-size: 16px;
          margin-right: 10px;

        }

        .header-actions {
          display: flex;
          gap: 10px;
        }
      }

      .pending-tags-grid {
        display: flex;
        flex-wrap: wrap;
        align-content: flex-start;
        overflow: auto;
        height: 500px;
        flex: 1 1 0;
        min-height: 0;


        .pending-tag-item {
          position: relative;
          cursor: pointer;
          display: inline-flex;
          align-items: center;
          border-radius: 4px;
          padding: 8px 12px;
          /* height: 18px; */
          margin-bottom: 8px;
          margin-right: 8px;
          white-space: nowrap;
          overflow: visible;
          /* 改为visible确保弹出内容可见 */
          text-overflow: ellipsis;
          background-color: rgb(242, 247, 251);
          border: 1px solid rgb(181, 181, 181);
          transition: all 0.2s ease;

          &.tag-selected {
            background-color: #ecf5ff;
            border-color: #409eff;
            color: #409eff;
          }

          .selection-indicator {
            margin-right: 5px;
          }

          .tag-actions {
            position: absolute;
            top: 100%;
            left: 0;
            background-color: #fff;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1);
            border-radius: 4px;
            z-index: 1000;
            /* 提高z-index确保比其他元素层级高 */
            display: flex;
            overflow: visible;
            /* 增加可见性 */
            margin-top: 5px;


          }
        }
      }
    }

  }

}

.action-btn {
  border: none;
  padding: 6px 12px;
  cursor: pointer;
  font-size: 12px;
  text-align: center;
  white-space: nowrap;

  &.classify-btn {
    background-color: #f0f9eb;
    color: #67c23a;

    &:hover {
      background-color: #e1f3d8;
    }
  }

  &.abandon-btn {
    background-color: #fef0f0;
    color: #f56c6c;

    &:hover {
      background-color: #fde2e2;
    }
  }

  &.look-btn {
    background-color: #ffffff;
    color: #09c6ff;

    &:hover {
      background-color: #ffffff;
    }
  }
}

.delete-icon {
  position: absolute;
  right: -6px;
  top: -6px;
  color: #ffffff;
  width: 12px;
  height: 12px;
  background-color: #ff0000;
  border-radius: 50%;
  font-size: 14px;
  cursor: pointer;
}
</style>