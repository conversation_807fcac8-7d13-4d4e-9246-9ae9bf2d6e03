<template>
    <el-container class="medical-record webkit-scrollbar">
      <!-- 左侧病人信息栏 -->
      <el-aside width="240px" class="patient-sidebar "  v-loading="load">
        <div class="sidebar-header">
          <el-select v-model="selectedDepartment" @change="getUser" placeholder="筛选病区" class="ward-select">
            <el-option :label="item.item_name" :value="item.item_no" v-for="item in select_list" :key="item.id" />
          </el-select>
          <div style="padding-top: 15px;">
            <el-select v-model="selectedDoctoId" @change="getUser" placeholder="医生筛选" class="ward-select">
              <el-option :label="item.username" :value="item.id" v-for="item in doctorList" :key="item.id">
                  <div class="select_item_num">
                    <span class="user_name">{{ item.username }}</span>
                    <el-tag size="small" type="danger">{{ item.task_total }}</el-tag>
                  </div>
              </el-option>
            </el-select>
          </div>
          <div style="padding-top: 15px;">
            <el-input placeholder="输入患者姓名" @input="searchUser" v-model="user_name" type="text"></el-input>
          </div>
        </div>

        <el-tabs v-model="activeName" class="demo-tabs">
          <el-tab-pane label="有任务的患者" name="first">
            <el-menu class="patient-list webkit-scrollbar">
              <el-menu-item
                  v-for="patient in user_list.filter((item)=>item.task_total>0)"
                  @click="selectUser(patient)"
                  :key="patient.pid"
                  :index="patient.pid"
                  v-show="patient.show"
              >
<!--                <span>{{ patient.pat_name }}（（主治医生：{{patient.doctor_name}}）</span>-->
                <span style="display: inline-block;width: 100px">{{ patient.pat_name }}</span>
                <el-tag size="small" type="danger">{{ patient.task_total }}</el-tag>
              </el-menu-item>
            </el-menu>
          </el-tab-pane>
          <el-tab-pane label="无任务的患者" name="second">
            <el-menu class="patient-list webkit-scrollbar">
              <el-menu-item
                  v-for="patient in user_list.filter((item)=>item.task_total<=0)"
                  @click="selectUser(patient)"
                  :key="patient.pid"
                  :index="patient.pid"
                  v-show="patient.show"
              >
                <span style="display: inline-block;width: 100px">{{ patient.pat_name }}</span>
                <el-tag size="small" type="danger">{{ patient.task_total }}</el-tag>
              </el-menu-item>
            </el-menu>
          </el-tab-pane>
        </el-tabs>


      </el-aside>
  
      <!-- 主要内容区 -->
      <el-main>
        <div class="content-header">
          <div style="display: flex;align-items: center;">
              <h3 style="margin-right: 15px;">选择入院次数</h3>
              <el-select v-model="selectedPvid" @change="getTask" placeholder="选择入院次数" class="ward-select">
                <el-option :value="row" :key="row" v-for="row in parseInt(select_row.pvid)">
                    {{'第'+row+'入院'}}
                </el-option>
              </el-select>
          </div>
        </div>
        <!-- 任务列表部分 -->
        <div class="task-section">
          <el-table v-if="taskList&&taskList.length>0" :data="taskList" border 
            :height="tableHeight"
            v-loading="load">
            <el-table-column type="index" label="编号" width="100"/>
            <el-table-column prop="state" label="病历类型"/>
            <el-table-column prop="create_time" label="任务创建时间"/>
            <el-table-column prop="edit_time" label="计划启动书写时间"/>
            <el-table-column label="创建类别" width="100">
              <template #default="scope">
                <el-tag v-if="scope.row.is_sys===0">手动</el-tag>
                <el-tag v-if="scope.row.is_sys===1">自动</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态"
              :filters="[
                  { text: '待上传', value: 9 },
                  { text: '已删除', value: -1 },
                  { text: '上传失败', value: 3 },
                  { text: '上传成功', value: 2 },
              ]"
              :filter-method="filterExpidate"
            >
              <template #default="scope">
                <div v-if="scope.row.status==-1">
                  <div v-if="scope.row.status==-1">
<!--                    <el-tag type="warning" v-if="is_shuxe.indexOf(scope.row.content)!==-1">已书写（任务删除）</el-tag>-->
                    <el-tag type="danger">任务已删除</el-tag>
                  </div>
                </div>
                <div v-else>
                    <div v-if="scope.row.document">
                          <div v-if="scope.row.status===0||scope.row.status==9">
                            <el-tag>待上传</el-tag>
                          </div>
                          <div v-else>
                            <el-tag v-if="scope.row.upload_status===1&&scope.row.status===0">上传中</el-tag>
                            <el-tag type="success" v-if="scope.row.status===2">上传成功</el-tag>
                            <el-tag type="info" v-if="scope.row.status===-1">取消上传</el-tag>
                            <el-tag  type="danger" v-if="scope.row.status===3||scope.row.status===4">上传失败</el-tag>
                          </div>
                    </div>
                    <div v-else>
                      <el-tag>待书写</el-tag>
                    </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="create_reason" label="任务创建来源"
              :filters="[
                  { text: '医嘱', value: '医嘱' },
                  { text: '检查', value: '检查' },
                  { text: '检验', value: '检验' },
              ]"
              :filter-method="filterYY"
            >
              <template #default="scope">
                <div style="height: 50px;">
                  <el-button type="warning" @click="see(scope.row)">查看详情</el-button>
                  <!-- {{scope.row.create_reason}} -->
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-main>
    </el-container>

    <el-dialog
        v-model="diseaseShow"
        title="详情"
        width="80%"
        draggable
    >
      <div style="max-height: 600px;overflow-y: auto;">
         <div>
          <div class="see_item">
            <h4>医嘱内容：</h4>
            <div>{{ see_row.yz_content?see_row.yz_content:"暂无医嘱" }}</div>
          </div>
          <div class="see_item">
            <h4>检查内容：</h4>
            <div>{{ see_row.jc_content?see_row.jc_content:"暂无检查内容" }}</div>
          </div>
          <div class="see_item">
            <h4>检验内容：</h4>
            <div>{{ see_row.jy_content?see_row.jy_content:"暂无检验内容" }}</div>
          </div>
          <div class="see_item">
            <h4>分析结果：</h4>
            <div>{{ see_row.create_reason }}</div>
          </div>
           <div class="see_item" v-if="see_row.content&&see_row.status===-1">
             <h4>删除原因：</h4>
             <div>{{ see_row.content }}</div>
           </div>
         </div>
      </div>
    </el-dialog>

  </template>
  
  <script setup>
  import { ref,onMounted,onUnmounted } from 'vue'
  import { post } from '@/utils/request.js'
  import c from "@/utils/config";
  const api_url = c.api_url;
  const selectedDepartment = ref('')
  const load = ref(false)
  const select_list = ref([])
  const user_list = ref([])
  const select_row = ref({})
  const page = ref(1)
  const his_list = ref({})
  const user_name = ref("")
  const adviceList = ref([]) //  医嘱列表
  const advicePage = ref(1)
  const taskList = ref([]) // 任务列表
  const selectedPvid = ref("")
  const select_name = ref("")
  const doctorList = ref([])
  const selectedDoctoId = ref()
  const diseaseShow = ref(false)
  const see_row = ref({})
  const tableHeight = ref(0)
  const activeName = ref("first")
  const is_shuxe = ref([
      '该类型记录已写入病程，不可重复写入。',
      '该日期记录已写入病程记录，不可重复写入。',
      '该医嘱已编写，不可重新编写。',
      '该检验报告已编写，不可重新编写。',
      '该日期记录已写入日常，不可重复写入',
      '该日期记录已写入病例，不可重复写入',
      '正常日常病程存在其他任务，则删除',
  ])

  // 添加计算表格高度的方法
  const calculateTableHeight = () => {
    // 计算表格可用高度
    // 视窗高度 - 头部高度(约140px) - 边距(40px)
    tableHeight.value = window.innerHeight - 100
  }

  // 监听窗口大小变化
  onMounted(() => {
    calculateTableHeight()
    window.addEventListener('resize', calculateTableHeight)
  })

  onUnmounted(() => {
    window.removeEventListener('resize', calculateTableHeight)
  })


  const see = (row)=>{
    diseaseShow.value = true
    see_row.value = row
  }

  const filterYY = (value,row)=>{
    let str = ""
    if(row.yz_content) str="医嘱"
    if(row.jy_content) str="检验"
    if(row.jc_content) str="检查"
    return str == value
  }

  // 筛选状态
  const filterExpidate = (value,row)=>{
    return row.status == value
  }


  const getDoctorByDeptId = (item_no)=>{
    let url = `${api_url}/AiCase/getDoctorByDeptId`
    post(url, JSON.stringify({item_no})).then(res => {
      doctorList.value = res.data
    }).catch(error => {
      console.error('发生错误:', error);
    });
  } 
  
  //搜索患者
  const searchUser = (value)=>{
    user_list.value = user_list.value.map((row) =>{
      if(!user_name.value || row.pat_name.toLowerCase().includes(user_name.value.toLowerCase())){
        row.show = true;
      }else{
        row.show = false;
      }
      return row;
    })
  
     console.log(value)
  }

  // 选择患者
  const selectUser = (row)=>{
    select_row.value = row
    selectedPvid.value = row.pvid;
    page.value = parseInt(row.pvid)
    advicePage.value = parseInt(row.pvid)
    getTask(row.pid,row.pvid)
  }
  // 获取任务列表
  const getTask = (pid,pvid)=>{
    load.value = true
    let url = `${api_url}/Patient/getCaseList`
    post(url, JSON.stringify({pid:select_row.value.pid,pvid:selectedPvid.value})).then(res => {
      let arr = []
      res.data.ai_list.forEach((row)=>{
        if(row.status!==-1){
          arr.push(row)
        }else{
          if(row.content) arr.push(row)
        }
      })
      taskList.value = arr
      if(res.data.his_list.length>0){
        his_list.value = res.data.his_list
      }else{
        his_list.value = {
          WBMC:"",
          document_time:""
        }
      }
      load.value = false
    }).catch(error => {
      load.value = false
      console.error('发生错误:', error);
    });
  }
  
  // 获取患者
  const getUser = ()=>{
    let s_index = select_list.value.findIndex((row)=>row.item_no==selectedDepartment.value);
    select_name.value = select_list.value[s_index]['item_name']
    load.value = true
    let url = `${api_url}/AiCase/getPatientListByDeptId`
    getDoctorByDeptId(selectedDepartment.value)
    post(url, JSON.stringify({
      item_no:selectedDepartment.value,
      doctor_id:selectedDoctoId.value 
    })).then(res => {
      user_list.value = res.data.map((row)=>{
        row.show = true
        return row
      })
      load.value = false
    }).catch(error => {
      load.value = false
      console.error('发生错误:', error);
    });
  }
  
  // 获取病区
  const getWardList = ()=>{
    let url = `${api_url}/AiCase/getWardList`
    load.value = true
    post(url, JSON.stringify({})).then(res => {
      console.log(res)
      select_list.value = res.data
      load.value = false
    }).catch(error => {
      load.value = false
      console.error('发生错误:', error);
    });
  }
  getWardList()
  
  
  // 模拟病人列表数据
  const patients = ref([
    { id: '1', name: '患者姓名' },
    { id: '2', name: '患者姓名' },
    { id: '3', name: '患者姓名' },
    { id: '4', name: '患者姓名' },
  ])
  
  // 最新病历数据
  // const latestRecord = ref([{
  //   type: '三级查房-主治医师',
  //   time: '2024-12-12 09:01',
  //   admissionCount: 10,
  //   admissionTime: '2024-12-10 09:30'
  // }])

  // 删除
  const deleteTask = (row)=>{
    load.value = true
    let url = `${api_url}/task/deleteTask`
    post(url, JSON.stringify({task_id:row.task_id})).then(res => {
      taskList.value = taskList.value.filter((item)=>item.task_id!==row.task_id)
      load.value = false
    }).catch(error => {
      load.value = false
      console.error('发生错误:', error);
    });
  }

  // 查看病历
  const seePatientCase = (row)=>{
    let url = `${api_url}/patient/getPatientCase`
    post(url, JSON.stringify({task_id:row.task_id})).then(res => {
      see_row.value = res.data;
      diseaseShow.value = true
    }).catch(error => {
      console.error('发生错误:', error);
    });
  }

  // 获取任务列表
  const getTaskList = ()=>{
    load.value = true;
    let url = `${api_url}/task/getTaskList`
    post(url, JSON.stringify({date:dataValue.value})).then(res => {
      taskList.value = res.data
      load.value = false
    }).catch(error => {
      load.value = false
      console.error('发生错误:', error);
    });
  }
  </script>
  
  <style scoped>

  .medical-record {
    height:100vh;
  }
  
  .patient-sidebar {
    border-right: 1px solid #e6e6e6;
    background-color: #f5f7fa;
  }
  
  .sidebar-header {
    padding: 20px;
    border-bottom: 1px solid #e6e6e6;
  }
  
  .ward-select {
    width: 100%;
    width: 200px;
  }
  
  .patient-list {
    border-right: none;
    height: calc(100vh - 225px);
    overflow-y: scroll;
  }
  
  .content-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }
  
  .task-section {
    margin-top: 20px;
  }
  
  .task-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
  }
  
  .el-main {
    padding: 20px;
    background-color: #fff;
  }
  .see_item{
    margin-bottom: 15px;
  }
  .see_item h4{
    margin-bottom: 10px;
  }
  .see_item div{
    padding: 15px;
    background: #f5f7fa;
  }
  .select_item_num{
    display: flex;
    align-items: center;
  }
  .user_name{
    width: 60px;
  }

  </style>