<template>
  <div class="tableData">
    <h3>{{ route.meta.title }}</h3>
    <div class="topMenu">
      <div class="menuBox">
        <el-button type="primary" @click="showAddDiseaseList">添加</el-button>

      </div>
    </div>
    <el-table class="table" :data="tableData" style="width: 100%;height: 800px;">
      <el-table-column type="index" label="序号" width="150" />
      <el-table-column prop="name" label="病种名称" />
      <el-table-column label="关联模版">
        <template #default="scope">
          <div>
            {{ scope.row.rel_disease }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="状态">
        <template #default="scope">
          <div>
            <el-switch v-model="value" />
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200">
        <template #default="scope">
          <div>
            <el-button link type="primary" @click="saveEditTag(scope.row, 'edit')">编辑</el-button>
            <el-button link type="danger" @click="saveEditTag(scope.row, 'del')">删除</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
  </div>

  <el-dialog v-model="show" title="病种" width="500" draggable>
    <div>
      <div style="margin-bottom: 15px;">
        <el-input placeholder="病种名称" type="text" v-model="name"></el-input>
      </div>
      <div style="margin-bottom: 15px;">
        <el-select v-model="selectedDepartment" placeholder="选择关联模版病种" class="ward-select">
          <el-option :label="item" :value="item" v-for="item in temp_tag" :key="item" />
        </el-select>
      </div>
      <el-button type="primary" @click="add">保存</el-button>
    </div>
  </el-dialog>


</template>
<script setup>
import { ref } from 'vue';
import { post } from '@/utils/request.js';
import { message } from '@/utils/message.js';
import { useRoute } from 'vue-router';

const route = useRoute();
const load = ref(false)
const show = ref(false)

import c from "@/utils/config";
const api_url = c.api_url;
const tableData = ref([])
const tmep_list = ref([])
const temp_show = ref(false)
const temp_tag = ref([])
const selectedDepartment = ref("")
const name = ref("")
const id = ref()
const value = ref(false)

const getBaseDiseaseList = (id) => {
  load.value = true
  let url = `${api_url}/patient/getBaseDiseaseList`
  post(url, JSON.stringify({ id })).then(res => {
    temp_tag.value = res.data;
    load.value = false
  }).catch(error => {
    load.value = false
    console.error('发生错误:', error);
  });
}
getBaseDiseaseList()
// 添加病种
const showAddDiseaseList = () => {
  id.value = ""
  selectedDepartment.value = ""
  name.value = ""
  show.value = true;
}
// 编辑
const saveEditTag = (row, type) => {
  id.value = ""
  selectedDepartment.value = ""
  name.value = ""
  if (type === 'del') {
    del(row.id)
  } else if (type === 'edit_temp') {
    temp_show.value = true
  } else if (type === 'edit') {
    id.value = row.id
    selectedDepartment.value = row.rel_disease
    name.value = row.name
    show.value = true;
    //temp_show.value = true
  }
}
const edit = () => {
  load.value = true
  let url = `${api_url}/patient/editDisease`
  post(url, JSON.stringify({
    id: id.value,
    name: name.value,
    base_disease: selectedDepartment.value
  })).then(res => {
    message('编辑成功')
    id.value = ""
    selectedDepartment.value = ""
    name.value = ""
    show.value = false;
    getDiseaseList()
    load.value = false
  }).catch(error => {
    load.value = false
    console.error('发生错误:', error);
  });
}
const del = (id) => {
  load.value = true
  let url = `${api_url}/patient/delDisease`
  post(url, JSON.stringify({ id })).then(res => {
    message('删除成功')
    getDiseaseList()
    load.value = false
  }).catch(error => {
    load.value = false
    console.error('发生错误:', error);
  });
}

const add = () => {
  if (id.value) {
    edit()
    return;
  }
  let list = tableData.value.filter((row) => row.name === name.value)
  if (list.length > 0) {
    message('已经有病种')
    return;
  }

  let postdata = { name: name.value, base_disease: selectedDepartment.value }
  load.value = true
  let url = `${api_url}/patient/addDisease`
  post(url, JSON.stringify(postdata)).then(res => {
    id.value = ""
    selectedDepartment.value = ""
    name.value = ""
    getDiseaseList()
    show.value = false;
    load.value = false
  }).catch(error => {
    load.value = false
    console.error('发生错误:', error);
  });
}
// 获取病种列表
const getDiseaseList = () => {
  load.value = true
  let url = `${api_url}/patient/getDiseaseList`
  post(url, JSON.stringify({})).then(res => {
    tableData.value = res.data
    console.log(tmep_list.value)
    load.value = false
  }).catch(error => {
    load.value = false
    console.error('发生错误:', error);
  });
}
// 获取模版列表
const getTempList = () => {
  load.value = true
  let url = `${api_url}/patient/getTemplateList`
  post(url, JSON.stringify({})).then(res => {
    tmep_list.value = res.data
    load.value = false
  }).catch(error => {
    load.value = false
    console.error('发生错误:', error);
  });
}

getDiseaseList()
getTempList()
</script>
<style scoped>
.tableData {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  background-color: #ffffff;
  padding: 10px;
  box-sizing: border-box;
}

h3 {
  height: 40px;
}

.topMenu {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 10px;
  flex-wrap: wrap;
  gap: 5px;
}

.menuBox {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 10px;
  flex-wrap: wrap;
  gap: 5px;
}

.table {
  flex: 1;
  overflow-y: auto;
}
</style>