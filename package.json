{"name": "vue3_cli_default", "version": "0.0.0", "scripts": {"dev": "vite --mode development", "test": "vite --mode test", "prod": "vite --mode production", "release": "vite --mode release", "build:test": "vite build --mode test", "build:dev": "vite build --mode development", "build:prod": "vite build --mode production", "serve": "vite preview", "build:release": "vite build --mode release"}, "dependencies": {"crypto-js": "^4.2.0", "d3": "^7.9.0", "dayjs": "^1.11.13", "diff": "^7.0.0", "echarts": "^5.6.0", "element-plus": "^2.9.0", "js-md5": "^0.8.3", "openai": "^4.87.3", "vue": "^3.2.8", "vue-router": "^4.5.0", "xlsx": "^0.18.5"}, "devDependencies": {"@vitejs/plugin-vue": "^1.6.0", "@vue/compiler-sfc": "^3.2.6", "unplugin-auto-import": "^0.18.6", "unplugin-vue-components": "^0.27.5", "vite": "^2.5.2"}}