<template>
  <div class="ai-butler-container">
    <div class="chat-window" ref="chatWindowRef">
      <div v-for="(msg, idx) in messages" :key="msg.role + idx" :class="['chat-msg', msg.role]">
        <span>{{ msg.content }}</span>
      </div>
    </div>
    <div class="chat-input">
      <el-input
        v-model="input"
        placeholder="请输入内容"
        @keyup.enter="sendMsg"
        clearable
      />
      <el-button type="primary" @click="sendMsg" :disabled="loading || !input">发送</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch, nextTick } from 'vue'

const wsUrl = 'ws://**************:19530/ws/chat';
const ws = ref(null)
const input = ref('')
const messages = ref([
  { role: 'ai', content: '您好，我是AI管家，有什么可以帮您？' }
])
const loading = ref(false)
const chatWindowRef = ref(null)
let reconnectTimer = null
let reconnectCount = 0
const MAX_RECONNECT = 5

function connectWS() {
  if (ws.value) ws.value.close()
  ws.value = new WebSocket(wsUrl)
  ws.value.onopen = () => {
    reconnectCount = 0
    console.log('WebSocket连接已建立')
  }
  ws.value.onmessage = (event) => {
  loading.value = false
  let data = JSON.parse(event.data)
  
  // 流式拼接
  if (
    data.role === 'assistant' &&
    messages.value.length > 0 &&
    messages.value[messages.value.length - 1].role === 'ai'
  ) {
    // 如果当前AI消息内容为“思考中...”，先替换为本次内容，否则拼接
    if (messages.value[messages.value.length - 1].content === '思考中...') {
      messages.value[messages.value.length - 1].content = data.content
    } else {
      messages.value[messages.value.length - 1].content += data.content
    }
  }
}
  ws.value.onerror = (err) => {
    loading.value = false
    messages.value.push({ role: 'ai', content: 'AI服务连接异常，请稍后重试。' })
    tryReconnect()
  }
  ws.value.onclose = () => {
    loading.value = false
    console.log('WebSocket已关闭')
    tryReconnect()
  }
}

// 重连机制
function tryReconnect() {
  if (reconnectCount < MAX_RECONNECT) {
    reconnectCount++
    if (reconnectTimer) clearTimeout(reconnectTimer)
    reconnectTimer = setTimeout(() => {
      connectWS()
    }, 1500)
  }
}

function sendMsg() {
  if (!input.value.trim()) return
  messages.value.push({ role: 'user', content: input.value })
  loading.value = true
  // 先插入“思考中...”的AI消息
  messages.value.push({ role: 'ai', content: '思考中...' })
  ws.value && ws.value.send(input.value)
  input.value = ''
}

// 自动滚动到底部
watch(messages, async () => {
  await nextTick()
  const el = chatWindowRef.value
  if (el) {
    el.scrollTop = el.scrollHeight
  }
}, { deep: true })

onMounted(() => {
  connectWS()
})
onBeforeUnmount(() => {
  if (ws.value) ws.value.close()
  if (reconnectTimer) clearTimeout(reconnectTimer)
})
</script>

<style scoped>
.ai-butler-container {
  width: 100%;
  min-width: 400px;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  height: 100%;
}
.chat-window {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  background: #f7f8fa;
  display: flex;
  flex-direction: column;
}
.chat-msg {
  margin-bottom: 12px;
  padding: 8px 12px;
  border-radius: 6px;
  max-width: 80%;
  word-break: break-all;
}
.chat-msg.user {
  background: #e6f7ff;
  align-self: flex-end;
  text-align: right;
}
.chat-msg.ai {
  background: #f0f0f0;
  align-self: flex-start;
  text-align: left;
}
.chat-input {
  display: flex;
  gap: 10px;
  padding: 16px;
  border-top: 1px solid #eee;
  background: #fff;
}
</style>