<template>
  <div class="tag-library-container" v-loading="isLoading">
    <!-- 左侧：使用中的标签 -->
    <div class="used-tags-section">

      <TagSection
        :title="`使用中的标签(对比：${tagsNumber.type1}， 替换：${tagsNumber.type2}， 总计${tagsNumber.type1 + tagsNumber.type2})`"
        :showSearch="false"
        searchPlaceholder="搜索标签"
        :searchValue="pendingTagSearch"
        @update:searchValue="val => pendingTagSearch = val"
      >
        <template #searchs>
          <el-input style="min-width: 100px;" placeholder="搜索标签分类" v-model="searchTagCategory" clearable></el-input>
          <el-input style="min-width: 100px;" placeholder="搜索标签内容" v-model="searchTagContent" clearable></el-input>
        </template>
        <template #actions>
          <template v-if="true">
            <el-button type="success" @click="RAGDownload">RAG下载JSON</el-button>
            <el-button type="success" @click="tagDownload">标签下载</el-button>
            <el-button type="primary" @click="showBatchAddDialog">批量新增</el-button>
            <el-button type="primary" @click="showCategoryDialog">新增分类</el-button>
            <el-button type="primary" @click="moverCategory">移动分类</el-button>
          </template>
        </template>
      </TagSection>

      <!-- 标签分类列表 -->
      <el-scrollbar class="category-list" style="height: 100%;" v-loading="loadStatus.treeLoad">
        <div v-for="(category, index) in filteredCategories" :key="index">
          <div class="category-header">
            <div class="category-info" @click="toggleCategory(category.id)">
              <span class="expand-icon">{{ expandedCategories.includes(category.id) ? '▼' : '▶' }}</span>
              <span>{{ category.name }}</span>
              <span class="tag-count">{{ category.count }}</span>
              <span>{{ category.description ? `(${category.description})` : '' }}</span>
            </div>
            <div class="category-actions">
              <el-button size="small" type="primary" plain @click.stop="addCategoryTag(category)">
                <el-icon>
                  <Plus />
                </el-icon>
              </el-button>
              <el-button size="small" type="primary" plain @click.stop="editCategory(category)">
                <el-icon>
                  <Edit />
                </el-icon>
              </el-button>
              <el-button size="small" type="danger" plain
                @click.stop="confirmDeleteCategory(category.id, category.name, category)">
                <el-icon>
                  <Delete />
                </el-icon>
              </el-button>
            </div>
          </div>
  
          <!-- 子分类 -->
          <div v-if="expandedCategories.includes(category.id)" class="subcategory-list">
            <div v-for="(subCategory, subIndex) in getFilteredSubCategories(category)" :key="subIndex">
              <div class="subcategory-header">
                <div class="subcategory-info" @click="toggleSubCategory(subCategory.id)">
                  <div>
                    <el-checkbox v-model="subCategory.isSelect" @click.stop />
                  </div>
                  <span class="expand-icon">{{ expandedSubCategories.includes(subCategory.id) ? '▼' : '▶' }}</span>
                  <span>{{ subCategory.name }}==={{subCategory.tags.filter((itemss) => itemss.type == 1).length}}</span>
                  <!-- <span style="padding-left:10px">{{ subCategory.description ? `(${subCategory.description})` : '' }}</span> -->
                </div>
                <div class="subcategory-actions">
                  <el-button size="small" type="primary" plain @click.stop="aiCategory(subCategory)">AI提取多标签</el-button>
                  <el-button size="small" type="primary" plain @click.stop="addSubCategoryTag(subCategory)">
                    <el-icon>
                      <Plus />
                    </el-icon>
                  </el-button>
                  <el-button size="small" type="primary" plain @click.stop="editCategory(subCategory)">
                    <el-icon>
                      <Edit />
                    </el-icon>
                  </el-button>
                  <el-button size="small" type="danger" plain
                    @click.stop="confirmDeleteCategory(subCategory.id, subCategory.name, subCategory)">
                    <el-icon>
                      <Delete />
                    </el-icon>
                  </el-button>
                </div>
              </div>
  
              <!-- 标签列表 -->
              <div v-if="expandedSubCategories.includes(subCategory.id)" class="tag-list">
                                <!-- 对比标签区域 -->
                <div class="tag-group tag-group-1">
                  <div class="tag-type-indicator">
                    <span>对比标签</span>
                  </div>

                  <!-- 批量操作按钮区域（独立一行） -->
                  <div class="batch-actions-row" v-if="getTagsByType(subCategory.tags, 1).length > 0">
                    <template v-if="!isTagBatchMode[`${subCategory.id}-1`]">
                      <el-button size="small" type="primary"
                        @click="startTagBatchMode(subCategory.id, 1)">批量删除</el-button>
                    </template>
                    <template v-else>
                      <el-checkbox v-model="tagBatchSelectAll[`${subCategory.id}-1`]"
                        @change="toggleSelectAllTags(subCategory.id, 1)">
                        全选
                      </el-checkbox>
                      <el-button size="small" type="danger" :disabled="!hasSelectedTags(subCategory.id, 1)"
                        @click="batchDeleteTags(subCategory.id, 1)">删除</el-button>
                      <el-button size="small" @click="cancelTagBatchMode(subCategory.id, 1)">取消</el-button>
                    </template>
                  </div>
                  
                  <!-- 使用TagsGrid组件显示对比标签 -->
                  <div class="tags-wrapper" @dragover.prevent="onTagsWrapperDragOver" @drop="onTagsWrapperDrop($event, subCategory.id, 1)">
                    <TagsGrid
                      :tags="getFormattedTagsForGrid(getTagsByType(subCategory.tags, 1), subCategory.id, 1)"
                      :isMultiSelectMode="isTagBatchMode[`${subCategory.id}-1`]"
                      :activeTagId="activeTagId"
                      :loading="false"
                      :isTagSelected="(tagId) => isTagSelectedInBatch(subCategory.id, 1, tagId)"
                      :draggable="false"
                      @toggleTagSelection="(tagId) => toggleTagInBatch(subCategory.id, 1, tagId)"
                      @showTagActions="showTagActions"
                      @dragstart="onPendingTagDragStart"
                      @dragend="onPendingTagDragEnd"
                    >
                      <template #end="{ tag }">
                        <el-icon class="delete-icon" @click="removeTag(subCategory.id, tag.id)">
                          <Close />
                        </el-icon>
                      </template>
                    </TagsGrid>
                    
                    <!-- 添加对比标签按钮（非编辑状态时显示） -->
                    <div
                      v-if="(!isAddingTag || currentSubCategoryId !== `${subCategory.id}-1`) && !isTagBatchMode[`${subCategory.id}-1`]"
                      class="add-tag" @click="startAddingTag(`${subCategory.id}-1`, 1)">
                      <span>+ 添加标签</span>
                    </div>
                    <!-- 添加标签输入框（编辑状态时显示） -->
                    <div
                      v-else-if="isAddingTag && currentSubCategoryId === `${subCategory.id}-1` && !isTagBatchMode[`${subCategory.id}-1`]"
                      class="tag-input-wrapper">
                      <el-input v-model="newTagContent" placeholder="请输入标签内容" size="small"
                        @keyup.enter="handleAddTag"></el-input>
                      <div class="tag-input-buttons">
                        <el-button size="small" type="primary" @click="handleAddTag">确定</el-button>
                        <el-button size="small" @click="cancelAddingTag">取消</el-button>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 替换标签区域 -->
                <div class="tag-group tag-group-2">
                  <div class="tag-type-indicator">
                    <span>替换标签</span>
                  </div>

                  <!-- 批量操作按钮区域（独立一行） -->
                  <div class="batch-actions-row" v-if="getTagsByType(subCategory.tags, 2).length > 0">
                    <template v-if="!isTagBatchMode[`${subCategory.id}-2`]">
                      <el-button size="small" type="primary"
                        @click="startTagBatchMode(subCategory.id, 2)">批量删除</el-button>
                    </template>
                    <template v-else>
                      <el-checkbox v-model="tagBatchSelectAll[`${subCategory.id}-2`]"
                        @change="toggleSelectAllTags(subCategory.id, 2)">
                        全选
                      </el-checkbox>
                      <el-button size="small" type="danger" :disabled="!hasSelectedTags(subCategory.id, 2)"
                        @click="batchDeleteTags(subCategory.id, 2)">删除</el-button>
                      <el-button size="small" @click="cancelTagBatchMode(subCategory.id, 2)">取消</el-button>
                    </template>
                  </div>
                  
                  <!-- 使用TagsGrid组件显示替换标签 -->
                  <div class="tags-wrapper" @dragover.prevent="onTagsWrapperDragOver" @drop="onTagsWrapperDrop($event, subCategory.id, 2)">
                    <TagsGrid
                      :tags="getFormattedTagsForGrid(getTagsByType(subCategory.tags, 2), subCategory.id, 2)"
                      :isMultiSelectMode="isTagBatchMode[`${subCategory.id}-2`]"
                      :activeTagId="activeTagId"
                      :loading="false"
                      :isTagSelected="(tagId) => isTagSelectedInBatch(subCategory.id, 2, tagId)"
                      :draggable="false"
                      @toggleTagSelection="(tagId) => toggleTagInBatch(subCategory.id, 2, tagId)"
                      @showTagActions="showTagActions"
                      @dragstart="onPendingTagDragStart"
                      @dragend="onPendingTagDragEnd"
                    >
                      <template #end="{ tag }">
                        <el-icon class="delete-icon" @click="removeTag(subCategory.id, tag.id)">
                          <Close />
                        </el-icon>
                      </template>
                    </TagsGrid>
                    
                    <!-- 添加替换标签按钮（非编辑状态时显示） -->
                    <div
                      v-if="(!isAddingTag || currentSubCategoryId !== `${subCategory.id}-2`) && !isTagBatchMode[`${subCategory.id}-2`]"
                      class="add-tag" @click="startAddingTag(`${subCategory.id}-2`, 2)">
                      <span>+ 添加标签</span>
                    </div>
                    <!-- 添加标签输入框（编辑状态时显示） -->
                    <div
                      v-else-if="isAddingTag && currentSubCategoryId === `${subCategory.id}-2` && !isTagBatchMode[`${subCategory.id}-2`]"
                      class="tag-input-wrapper">
                      <el-input v-model="newTagContent" placeholder="请输入标签内容" size="small"
                        @keyup.enter="handleAddTag"></el-input>
                      <div class="tag-input-buttons">
                        <el-button size="small" type="primary" @click="handleAddTag">确定</el-button>
                        <el-button size="small" @click="cancelAddingTag">取消</el-button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-scrollbar>
    </div>

    <!-- 右侧：待处理标签和弃用标签 -->
    <div class="right-section">
      <!-- 待处理标签 -->
      
      <div class="pending-tags-section" style="height: calc(100%);">
        <TagSection
          title="单待处理标签"
          :showSearch="true"
          searchPlaceholder="搜索单待处理标签"
          :searchValue="pendingTagSearch"
          @update:searchValue="val => pendingTagSearch = val"
        >
          <template #actions>
            <template v-if="!isMultiSelectMode">
              <el-button type="primary" @click="enableMultiSelect('classify')">批量归类</el-button>
              <el-button type="danger" @click="enableMultiSelect('abandon')">批量弃用</el-button>
            </template>
            <template v-else>
              <el-button @click="cancelMultiSelect">取消</el-button>
              <el-button type="primary" @click="confirmMultiSelect(selectedTags)" v-if="selectedTags.length > 0">
                {{ multiSelectMode === 'classify' ? '选择分类' : '确认弃用' }}({{ selectedTags.length }})
              </el-button>
            </template>
          </template>
        </TagSection>

        <TagsGrid
          :tags="pendingTags"
          :isMultiSelectMode="isMultiSelectMode"
          :activeTagId="activeTagId"
          :loading="pendingTagsLoading"
          :isTagSelected="isTagSelected"
          :draggable="true"
          @toggleTagSelection="toggleTagSelection"
          @showTagActions="showTagActions"
          @classifyTag="classifyTag"
          @abandonTag="abandonTag"
          @dragstart="onPendingTagDragStart"
          @dragend="onPendingTagDragEnd"
        >
          <!-- 可选：自定义操作按钮插槽 -->
          <template #actions="{ tag }">
            <button class="action-btn classify-btn" @click.stop="classifyTag(tag.id)">分类</button>
            <button class="action-btn abandon-btn" @click.stop="abandonTag(tag.id)">弃用</button>
          </template>
        </TagsGrid>

        <!-- 分页组件 -->

        <Pagination 
        :currentPage="pendingTagsPage" 
        :pageSize="pendingTagsPageSize" 
        :total="pendingTagsTotal"
        @current-change="handlePendingTagsPageChange"
        @size-change="handlePendingTagsSizeChange"
        />
      </div>

    </div>

    <!-- 添加标签弹窗 -->
    <el-dialog :visible.sync="tagModalVisible" title="添加标签" width="30%" :before-close="() => tagModalVisible = false">
      <el-input v-model="newTagContent" placeholder="请输入标签内容"></el-input>
      <span slot="footer" class="dialog-footer">
        <el-button @click="tagModalVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleAddTag">确 定</el-button>
      </span>
    </el-dialog>



    <!-- 批量新增对话框 -->
    <el-dialog v-model="batchAddDialogVisible" title="批量新增标签" width="40%"
      :before-close="() => batchAddDialogVisible = false">
      <div class="batch-add-input">
        <el-input type="textarea" v-model="batchTagsInput" :rows="5" placeholder="请输入标签内容，每行一个标签"></el-input>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="batchAddDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="confirmBatchAdd">确 定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 二级分类添加标签对话框 -->
    <el-dialog v-model="subCategoryAddTagVisible" title="添加标签" width="40%"
      :before-close="() => subCategoryAddTagVisible = false">
      <el-input v-model="subCategoryTagInput" placeholder="请输入标签内容"></el-input>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="subCategoryAddTagVisible = false">取 消</el-button>
          <el-button type="primary" @click="confirmAddSubCategoryTag">确 定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 一级分类添加标签对话框 -->
    <el-dialog v-model="categoryAddTagVisible" title="添加标签" width="40%"
      :before-close="() => categoryAddTagVisible = false">
      <div class="category-select-container">
        <div style="margin-bottom: 15px">
          <span>请选择二级分类：</span>
          <el-select v-model="selectedSubCategoryForTag" placeholder="请选择二级分类">
            <el-option v-for="item in subCategoriesForSelect" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </div>
        <el-input v-model="categoryTagInput" placeholder="请输入标签内容"></el-input>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="categoryAddTagVisible = false">取 消</el-button>
          <el-button type="primary" @click="confirmAddCategoryTag" :disabled="!selectedSubCategoryForTag">确
            定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 标签分类选择弹窗 -->
    <el-dialog v-model="classifyDialogVisible" title="选择分类" width="80%"
      :before-close="() => classifyDialogVisible = false">
      <div class="category-search">
        <el-input v-model="searchClassifyCategory" placeholder="搜索分类名称" prefix-icon="Search" clearable></el-input>
      </div>

      <el-scrollbar height="500px">
        <div class="classify-tree-container">
          <!-- 分类树形结构 -->
          <el-tree ref="categoryTreeRef" :data="categoryTreeData" :props="categoryTreeProps" node-key="id"
            :filter-node-method="filterCategoryNode" highlight-current @node-click="handleCategorySelect">
            <template #default="{ node, data }">
              <span class="custom-tree-node">
                <span>{{ node.label }}</span>
                <span v-if="data.description" class="node-description">
                  ({{ data.description }})
                </span>
              </span>
            </template>
          </el-tree>
        </div>
      </el-scrollbar>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="classifyDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmClassify">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 新增分类弹窗 -->
    <el-dialog v-model="categoryDialogVisible" :title="isEditMode ? '修改分类' : '新增分类'" width="30%" height="100%" 
      :before-close="() => categoryDialogVisible = false">
      <div class="category-form" >
        <div class="form-item">
          <label><span class="required">*</span> 上级分类</label>
          <el-select v-model="newCategory.pid" placeholder="请选择" style="width: 100%"
            :disabled="isEditMode && newCategory.pid === 0">
            <el-option v-if="newCategory.pid === 0" label="无" :value="0"></el-option>
            <el-option v-for="category in parentCategoryOptions" :key="category.pid" :label="category.name"
              :value="category.pid" :disabled="isEditMode && newCategory.id === category.pid"></el-option>
          </el-select>
        </div>

        <div class="form-item">
          <label><span class="required">*</span> 分类名称</label>
          <el-input v-model="newCategory.name" placeholder="请输入"></el-input>
        </div>

        <div class="form-item">
          <label>备注</label>
          <el-input v-model="newCategory.description" type="textarea" :rows="3" style="height: 150px;"
            placeholder="请输入"></el-input>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="categoryDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleAddCategory" :disabled="showCategoryDialogLoading">确定</el-button>
        </span>
      </template>
    </el-dialog>


    <el-dialog v-model="ai_extraction" title="提取多标签" width="80%">

      <div style="height: 500px;overflow-y: scroll;">
        <el-timeline>
          <el-timeline-item timestamp="提示词" placement="top">
            <el-card>
              <el-input v-model="ai_extraction_word.word_text" type="textarea" :rows="3" style="height: 250px;"
                placeholder="请输入"></el-input>
            </el-card>
          </el-timeline-item>
          <el-timeline-item timestamp="入参数" placement="top">
            <el-card>
              {{ ai_extraction_word.word_rc }}
            </el-card>
          </el-timeline-item>
          <el-timeline-item timestamp="提取多标签结果" placement="top">
            <el-card>
              <el-input v-model="ai_extraction_word.word_fh_tags" type="textarea" :rows="1" style="height: 50px;"
                placeholder="请输入"></el-input>
            </el-card>
          </el-timeline-item>
          <el-timeline-item timestamp="分词提示词" placement="top">
            <el-card>
              <el-input v-model="ai_extraction_word.word_jg_text" type="textarea" :rows="3" style="height: 250px;"
                placeholder="请输入"></el-input>
            </el-card>
          </el-timeline-item>
          <el-timeline-item timestamp="分词标签结果" placement="top">
            <el-card>
              <div v-for="(item, index) in ai_extraction_word.word_jg">
                <div style="margin-bottom: 15px;">
                  tag{{ index + 1 }}:{{ item.compound_label }}
                </div>
                <div style="margin-bottom: 15px;">
                  <el-button :disabled="tag_load" @click="setTag(tag, item.compound_label)"
                    v-for="(tag, i) in item.sub_label">{{ tag }}</el-button>
                </div>
              </div>
            </el-card>
          </el-timeline-item>
        </el-timeline>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="ai_extraction = false">取消</el-button>
          <el-button type="primary" @click="extractionTag" :loading="load">提取</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 移动分类对话框 -->
    <el-dialog v-model="moveCategoryDialogVisible" title="移动分类" width="40%"
      :before-close="() => moveCategoryDialogVisible = false">
      <!-- 已选中分类的标签显示区域 -->
      <div class="selected-categories-tags" v-if="selectedSubCategories.length > 0">
        <span class="tag-label">已选中：</span>
        <div class="tags-container">
          <el-tag v-for="subCategory in selectedSubCategories" :key="subCategory.id" closable
            @close="removeSelectedCategory(subCategory.id)" class="category-tag" type="info">
            {{ subCategory.name }}
          </el-tag>
        </div>
      </div>

      <div class="category-search">
        <el-input v-model="searchMoveCategory" placeholder="搜索父分类名称" prefix-icon="Search" clearable></el-input>
      </div>
      <el-scrollbar height="500px">
        <div class="move-category-container">
          <!-- 仅显示父分类的列表 -->
          <el-radio-group v-model="selectedMoveCategory">
            <div class="parent-category-list">
              <div v-for="(category, index) in filteredParentCategories" :key="index" class="parent-category-item">
                <el-radio :label="category.id">
                  <span>{{ category.name }}</span>
                  <span v-if="category.description" class="category-description">（{{ category.description }}）</span>
                </el-radio>
              </div>
            </div>
          </el-radio-group>
        </div>
      </el-scrollbar>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="moveCategoryDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmMoveCategory">确定</el-button>
        </span>
      </template>
    </el-dialog>


  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch, onBeforeUnmount } from 'vue'
import { post, get } from '@/utils/request.js'
import c from "@/utils/config";
import { message, confirm } from '@/utils/message'
import { ElMessage, ElMessageBox, ElCheckbox } from 'element-plus';
import { extractionWord, extractionItemWord } from "@/utils/word_text";
import * as XLSX from 'xlsx';
import TagSection from '@/components/TagSection.vue';
import Pagination from '@/components/Pagination.vue';
import TagsGrid from '@/components/TagsGrid.vue'
import { baseUrl } from '../utils/baseUrl';


// const api_url = c.api_url;
const api_url = baseUrl.url8;

const draggingTagIds = ref([]);
const dragPreviewPos = ref({ x: 0, y: 0 });
let dragPreviewListener = null;
let rafId = null; // 新增
const isLoading = ref(false); // 用于控制加载状态

// 拖拽开始
function onPendingTagDragStart(event, tag) {
    if (isMultiSelectMode.value && isTagSelected(tag.id)) {
    draggingTagIds.value = [...selectedTags.value];
    event.dataTransfer.effectAllowed = 'move';
    event.dataTransfer.setData('text/plain', JSON.stringify(draggingTagIds.value));
    // 优化：使用 requestAnimationFrame 节流
    dragPreviewPos.value = { x: event.clientX + 10, y: event.clientY + 10 };
    dragPreviewListener = (e) => {
      if (rafId) return;
      rafId = requestAnimationFrame(() => {
        dragPreviewPos.value = { x: e.clientX + 10, y: e.clientY + 10 };
        rafId = null;
      });
    };
    document.addEventListener('dragover', dragPreviewListener);
    if (event.dataTransfer.setDragImage) {
      const img = document.createElement('div');
      img.style.display = 'none';
      document.body.appendChild(img);
      event.dataTransfer.setDragImage(img, 0, 0);
      setTimeout(() => document.body.removeChild(img), 0);
    }
  } else {
    draggingTagIds.value = [tag.id];
    event.dataTransfer.effectAllowed = 'move';
    event.dataTransfer.setData('text/plain', JSON.stringify([tag.id]));
  }
}

// 拖拽结束
function onPendingTagDragEnd() {
  draggingTagIds.value = [];
  dragPreviewPos.value = { x: 0, y: 0 };
  if (dragPreviewListener) {
    document.removeEventListener('dragover', dragPreviewListener);
    dragPreviewListener = null;
  }
  if (rafId) {
    cancelAnimationFrame(rafId);
    rafId = null;
  }
}

// 保证组件卸载时移除监听
onBeforeUnmount(() => {
  if (dragPreviewListener) {
    document.removeEventListener('dragover', dragPreviewListener);
    dragPreviewListener = null;
  }
  if (rafId) {
    cancelAnimationFrame(rafId);
    rafId = null;
  }
});

// 拖拽到tags-wrapper上
function onTagsWrapperDragOver(event) {
  event.preventDefault();
}

// 拖拽释放到tags-wrapper
async function onTagsWrapperDrop(event, subCategoryId, tagType) {
  isLoading.value = true; // 开始加载状态
  event.preventDefault();
  let tagIds;
  try {
    tagIds = JSON.parse(event.dataTransfer.getData('text/plain'));
  } catch {
    tagIds = draggingTagIds.value;
  }
  if (!tagIds || tagIds.length === 0) return;

  // 调用接口批量分类
  try {
    const result = await post(`${api_url}/tag/updatePendingTag`, JSON.stringify({
      ids: tagIds,
      category_id: subCategoryId,
      type: tagType
    }));
    if (result.error_code === 0) {
      ElMessage.success(`已成功分类 ${tagIds.length} 个标签`);
      // 刷新数据
      fetchPendingTags();
      fetchCategoryTree();
      cancelMultiSelect();
      isLoading.value = false; // 结束加载状态
    } else {
      ElMessage.error(result.msg || '标签分类失败');
      isLoading.value = false; // 结束加载状态
    }
  } catch (error) {
    ElMessage.error('标签分类异常');
    isLoading.value = false; // 结束加载状态
  }
}

// 状态管理
const searchTagCategory = ref('');
const searchTagContent = ref('');
const searchMoveCategory = ref(''); // 搜索移动分类
const moveCategoryDialogVisible = ref(false); // 移动分类对话框显示状态
const selectedMoveCategory = ref(null); // 选择的目标父分类ID
const expandedCategories = ref([1]); // 默认展开第一个分类
const expandedSubCategories = ref([1, 2]); // 默认展开部分子分类
const tagModalVisible = ref(false);
const newTagContent = ref('');
const currentSubCategoryId = ref(null);
const currentTagType = ref(1); // 当前添加的标签类型，1=对比标签，2=替换标签
const isAddingTag = ref(false); // 是否正在添加标签
const activeTagId = ref(null); // 当前激活的标签 ID，用于显示操作按钮

// 标签批量操作相关状态
const isTagBatchMode = ref({}); // 各子分类的批量模式状态
const tagBatchSelectAll = ref({}); // 各子分类的全选状态
const selectedTagIds = ref({}); // 各子分类选中的标签ID
const loading = ref(false); // 加载状态

// 多选相关状态
const isMultiSelectMode = ref(false); // 是否处于多选模式
const multiSelectMode = ref(''); // 多选模式类型：classify或abandon
const selectedTags = ref([]); // 已选中的标签 ID 数组

// 分类树相关状态
const classifyDialogVisible = ref(false); // 分类选择弹窗显示状态
const searchClassifyCategory = ref(''); // 搜索分类内容
const categoryTreeRef = ref(null); // 分类树引用
const currentClassifyTagId = ref(null); // 当前要分类的标签 ID
const selectedCategoryId = ref(null); // 当前选中的分类 ID

// loading管理
const loadStatus = ref({
  treeLoad: true,
  pendingLoad: true,
});

// 分词加载
const load = ref(false)
const tag_load = ref(false)
// ai分词弹窗
const ai_extraction = ref(false)
// 分词流程结果展示
const ai_extraction_word = ref({
  word_text: "",
  word_rc: "",
  word_jg: "",
  word_jg_text: "",
  word_fh_tags: []
})

let select_arr = ref([])


// 存储选中的子分类信息（包含id和name）
const selectedSubCategories = ref([]);

// 移除已选中的分类
const removeSelectedCategory = (categoryId) => {
  // 从显示列表中移除
  selectedSubCategories.value = selectedSubCategories.value.filter(item => item.id !== categoryId);
  // 从select_arr中移除
  select_arr.value = select_arr.value.filter(id => id !== categoryId);

  // 同步更新原始分类的选中状态（取消选中）
  categories.value.forEach(category => {
    if (category.children && category.children.length > 0) {
      category.children.forEach(subCategory => {
        if (subCategory.id === categoryId) {
          subCategory.isSelect = false;
        }
      });
    }
  });

  // 如果所有分类都被移除，关闭对话框
  if (selectedSubCategories.value.length === 0) {
    moveCategoryDialogVisible.value = false;
    ElMessage.info('已取消移动操作');
  }
};

const moverCategory = () => {
  // 收集所有已选中的subCategory的id
  const selectedIds = [];
  const selectedCats = [];

  // 遍历所有分类
  categories.value.forEach(category => {
    // 遍历每个分类下的子分类
    if (category.children && category.children.length > 0) {
      category.children.forEach(subCategory => {
        // 如果子分类被选中，添加到selectedIds数组
        if (subCategory.isSelect) {
          selectedIds.push(subCategory.id);
          // 同时保存名称，用于显示标签
          selectedCats.push({
            id: subCategory.id,
            name: subCategory.name
          });
        }
      });
    }
  });

  // 如果没有选中任何子分类，提示用户
  if (selectedIds.length === 0) {
    ElMessage.warning('请先选择要移动的分类');
    return;
  }

  // 保存选中的分类ID到状态变量
  select_arr.value = selectedIds;
  // 保存选中的分类信息（包含名称）
  selectedSubCategories.value = selectedCats;

  // 打开移动分类对话框
  moveCategoryDialogVisible.value = true;
}

// 保存标签
const setTag = async (tag, compound_label) => {

  tag_load.value = true;
  let header = {
    'Content-Type': 'application/json',
    'Cache-Control': 'no-cache',
    'Authorization': 'Bearer Lkk3yeIiKbn9pFEhUYD0Lv6X'
  }
  let data = {
    "query": tag,
    "mode": "hybrid",
    "top_k": 300,
    'X-Tenant-ID': 12345
  }

  const res = await post(`${baseUrl.url2}/hospital/search_match_label`, JSON.stringify(data), header)

  if (res.data.result[0]) {
    let info = {
      name: compound_label,
      sub_list: [{
        name: tag,
        sub_list: res.data.result[0] ? res.data.result[0]['sub_tags'] : []
      }]
    }

    const res_post = await post(`${api_url}/tags/saveTag `, JSON.stringify(info), header)
    if (res_post.error_code === 0) {
      ai_extraction_word.value.word_jg = ai_extraction_word.value.word_jg.map((row) => {
        row.sub_label = row.sub_label.filter((item) => item !== tag)
        return row
      })
    }
    ElMessage.success('标签分配成功');
  } else {
    ElMessage.info('标签没找到');
  }
  tag_load.value = false;
}


// ai分词操作
const aiCategory = (row) => {

  ai_extraction_word.value = {
    word_text: "",
    word_rc: "",
    word_jg: "",
    word_jg_text: "",
    word_fh_tags: []
  }

  let tags = (row.tags.map((tag) => tag.name))
  let tag_str = {}
  tags.forEach((itemsd, j) => {
    tag_str['tag' + (j + 1)] = itemsd
  })
  let value = JSON.stringify(tag_str)
  ai_extraction_word.value.word_rc = value
  ai_extraction_word.value.word_text = extractionWord(value)
  ai_extraction_word.value.word_jg_text = extractionItemWord('')

  ai_extraction.value = true;
}

function extractContent(str) {
  const regex = /\[(.*?)\]/g;
  let match;
  const results = [];

  while ((match = regex.exec(str)) !== null) {
    results.push(match[1]);
  }

  return results;
}

const getAi = async (word) => {
  let data = {
    messages: [{ role: "user", content: word }],
    temperature: 0.0,
    max_tokens: 10000,
    model: "qwen3-32b",
    // model:"qwen3-32b"
    enable_thinking: false
  }
  let ai_url = baseUrl.ai_url
  const res = await post(ai_url, JSON.stringify(data), {
    'Content-Type': 'application/json',
    'Cache-Control': 'no-cache',
    // 'Authorization': 'Bearer Lkk3yeIiKbn9pFEhUYD0Lv6X'
    'Authorization': 'Bearer sk-a11f833afe094bb9971932a5ea001834'
  })
  let str = res.choices[0]['message']['content']
  return str;
}

// 确认提取标签
const extractionTag = async () => {
  load.value = true;
  let str = await getAi(ai_extraction_word.value.word_text)
  let tags = extractContent(str)
  if (tags[0]) {
    let word_fh_tags = tags[0].replace(/"/g, '').replace(/\s+/g, '').replace(/\\n/g, '').split(',')
    ai_extraction_word.value.word_fh_tags = word_fh_tags;
    let word = word_fh_tags.join(",")
    ai_extraction_word.value.word_jg_text = ai_extraction_word.value.word_jg_text.replace(/{tags}/, word);
    let res = await getAi(ai_extraction_word.value.word_jg_text)
    ai_extraction_word.value.word_jg = JSON.parse(res)
    load.value = false;
  } else {
    load.value = false;
    ai_extraction_word.value.word_fh_tags = "没有提取到标签"
  }
}


// 分类树属性定义
const categoryTreeProps = {
  label: 'name',
  children: 'children'
};

// 分类树数据，基于 categories.value 转换而来
const categoryTreeData = computed(() => {
  // 深拷贝分类数据，不包含标签数组
  return categories.value.map(category => {
    const categoryData = {
      id: category.id,
      name: category.name,
      pid: category.pid || 0,
      description: category.description || ''
    };

    // 处理子分类，但不包含标签数组
    if (category.children && category.children.length > 0) {
      categoryData.children = category.children.map(child => ({
        id: child.id,
        name: child.name,
        pid: child.pid,
        description: child.description || '',
        isLeaf: true // 标记为叶子节点
      }));
    }

    return categoryData;
  });
});

// 监听搜索内容变化，过滤树节点
watch(searchClassifyCategory, (val) => {
  categoryTreeRef.value?.filter(val);
});

// 批量新增相关状态
// 批量新增标签的输入内容
const batchTagsInput = ref('');

// 批量新增对话框可见性
const batchAddDialogVisible = ref(false);

// 二级分类添加标签输入内容
const subCategoryTagInput = ref('');

// 二级分类添加标签对话框可见性
const subCategoryAddTagVisible = ref(false);

// 当前选中的二级分类
const currentSubCategory = ref(null);

// 一级分类添加标签输入内容
const categoryTagInput = ref('');

// 一级分类添加标签对话框可见性
const categoryAddTagVisible = ref(false);

// 当前选中的一级分类
const currentCategory = ref(null);

// 用于选择的二级分类列表
const subCategoriesForSelect = ref([]);

// 选中的二级分类用于添加标签
const selectedSubCategoryForTag = ref('');

// 新增分类相关状态
const categoryDialogVisible = ref(false); // 分类弹窗显示状态
const isEditMode = ref(false); // 是否处于编辑模式
const newCategory = ref({
  id: null, // 编辑时使用的ID
  pid: 0, // 默认上级分类为无（pid=0）
  name: '',
  description: ''
});

// 父分类选项，从 getCategoryTree 接口获取的数据中提取
const parentCategoryOptions = computed(() => {
  // 筛选可用的父分类选项
  // 在编辑模式下，需要排除当前编辑的分类及其子分类
  if (isEditMode.value && newCategory.value.id) {
    // 排除自身及其子分类，避免循环引用
    return categories.value
      .filter(category => category.id !== newCategory.value.id)
      .map(category => ({
        name: category.name,  // key 使用 name
        pid: category.id      // value 使用 pid
      }));
  }

  // 新增模式下返回全部分类
  return categories.value.map(category => ({
    name: category.name,  // key 使用 name
    pid: category.id      // value 使用 pid
  }));
});

// 从接口获取标签分类数据
const categories = ref([]);

// 获取标签分类树结构
const fetchCategoryTree = async () => {
  tagsNumber.value.type1 = 0
  tagsNumber.value.type2 = 0
  try {
    let url = `${api_url}/tag/getCategoryTree`
    const result = await post(url, JSON.stringify({}));

    if (result.error_code === 0 && result.data) {
      // 处理返回的数据，添加必要的属性
      const processedCategories = result.data.map(category => {
        // 处理主分类
        const categoryChildren = [];

        // 如果原始数据中有子分类，处理这些子分类
        if (category.children && category.children.length > 0) {
          category.children.forEach(child => {
            // 处理子分类
            categoryChildren.push({
              id: child.id,
              name: child.name,
              pid: child.pid,
              type: child.type || 1, // 默认类型
              typeName: child.type === 2 ? '替换标签' : '对比标签', // 根据类型设置名称
              tags: child.tags || [], // 如果没有tags属性，使用空数组
              description: child.description || ''
            });
            
            tagsNumber.value.type1 += child.tags.filter(tag => tag.type === 1).length
            tagsNumber.value.type2 += child.tags.filter(tag => tag.type === 2).length
          });
        }

        return {
          id: category.id,
          name: category.name,
          pid: category.pid || 0,
          description: category.description,
          count: categoryChildren.length,
          children: categoryChildren
        };
      });

      // 注意：我们在前端展示对比标签和替换标签时，直接在子分类下都显示两种标签类型的区域
      // 标签类型通过标签本身的type属性来区分，而非创建不同类型的子分类

      categories.value = processedCategories;
      loadStatus.value.treeLoad = false

    } else {
      ElMessage.error('获取标签分类数据失败');
      loadStatus.value.treeLoad = false

    }
  } catch (error) {
    loadStatus.value.treeLoad = false

    console.error('获取标签分类数据异常:', error);
    ElMessage.error('获取标签分类数据异常');
  }
};

// 待处理标签搜索
const pendingTagSearch = ref('');

// 使用监听器监听pendingTagSearch的变化，触发后端搜索
watch(pendingTagSearch, (newVal, oldVal) => {
  // 重置页码，确保从第一页开始搜索
  pendingTagsPage.value = 1;
  // 使用节流函数调用后端搜索，减少频繁请求
  const debouncedSearch = setTimeout(() => {
    fetchPendingTags();
  }, 1000);

  return () => clearTimeout(debouncedSearch);
});

// 使用onMounted生命周期钩子在组件挂载时加载数据
const tagsNumber = ref({
  type1: 0, // 对比标签总数
  type2: 0, // 替换标签总数
}); // 标签总数
onMounted(() => {
  fetchCategoryTree();
  fetchPendingTags();
});

// 待处理标签相关数据
const pendingTags = ref([]); // 待处理标签数据
const pendingTagsLoading = ref(false); // 加载状态
const pendingTagsPage = ref(1); // 当前页码
const pendingTagsPageSize = ref(10); // 每页记录数
const pendingTagsTotal = ref(0); // 总记录数

// 获取待处理标签
const fetchPendingTags = async () => {
  if (pendingTagsLoading.value) return;

  pendingTagsLoading.value = true;
  try {
    let url = `${api_url}/tag/getPendingTags`
    const result = await post(url, JSON.stringify({
      page: pendingTagsPage.value,
      pageSize: pendingTagsPageSize.value,
      searchText: pendingTagSearch.value,
      type: "1",
      is_all: "1"
    }));
    if (result.error_code === 0 && result.data) {
      // 处理返回的数据，添加selected属性以支持多选功能
      const newTags = result.data.list.map(tag => {
        // 检查当前标签是否在选中数组中
        const isSelected = isMultiSelectMode.value && selectedTags.value.includes(tag.id);

        return {
          id: tag.id,
          content: tag.name, // 将name存入content以兼容现有代码
          name: tag.name,    // 保留原始属性
          description: tag.description || '',
          create_time: tag.create_time,
          selected: isSelected  // 根据选中数组设置状态
        };
      });

      // 设置数据和总数
      pendingTags.value = newTags;
      pendingTagsTotal.value = result.data.total || 0;

    } else {
      ElMessage.error('获取待处理标签数据失败');
    }
  } catch (error) {
    console.error('获取待处理标签异常:', error);
    ElMessage.error('获取待处理标签异常');
  } finally {
    pendingTagsLoading.value = false;
  }
};

// 处理每页记录数变化
const handlePendingTagsSizeChange = (size) => {
  pendingTagsPageSize.value = size;
  pendingTagsPage.value = 1; // 重置为第一页
  fetchPendingTags();
};

// 处理页码变化
const handlePendingTagsPageChange = (page) => {
  pendingTagsPage.value = page;
  fetchPendingTags();
};

// 重置待处理标签列表
const resetPendingTags = () => {
  pendingTags.value = [];
  pendingTagsPage.value = 1;
  pendingTagsTotal.value = 0;
  pendingTagSearch.value = '';
  fetchPendingTags();
};

// 启用多选模式
const enableMultiSelect = (mode) => {

  isMultiSelectMode.value = true;
  multiSelectMode.value = mode;
  selectedTags.value = []; // 清空已选标签列表

  // 重置所有标签的选中状态
  pendingTags.value.forEach(tag => {
    tag.selected = false;
  });
};

// 取消多选模式
const cancelMultiSelect = () => {
  isMultiSelectMode.value = false;
  multiSelectMode.value = '';
  selectedTags.value = []; // 清空已选标签列表

  // 重置所有标签的选中状态
  pendingTags.value.forEach(tag => {
    tag.selected = false;
  });
};

// 切换标签选中状态
const toggleTagSelection = (tagId) => {
  console.log('toggleTagSelection', tagId);

  // 查找对应的标签
  const tag = pendingTags.value.find(t => t.id === tagId);
  if (tag) {
    // 设置新的状态 - 取反当前状态
    const newState = !tag.selected;
    tag.selected = newState;

    // 更新已选标签列表
    if (newState) {
      if (!selectedTags.value.includes(tagId)) {
        selectedTags.value.push(tagId);
      }
    } else {
      selectedTags.value = selectedTags.value.filter(id => id !== tagId);
    }
  }
};

// 检查标签是否被选中
const isTagSelected = (tagId) => {
  return pendingTags.value.find(t => t.id === tagId)?.selected || false;
};

// 确认多选操作
const confirmMultiSelect = () => {
  if (selectedTags.value.length === 0) {
    ElMessage.warning('请至少选择一个标签');
    return;
  }
  if (multiSelectMode.value === 'classify') {
    // 批量分类操作 - 打开分类选择对话框
    // 记录当前选中的标签ID数组
    currentBatchClassifyTagIds = [...selectedTags.value];
    selectedCategoryId.value = null; // 重置选中的分类

    // 重置搜索内容
    searchClassifyCategory.value = '';
    if (categoryTreeRef.value) {
      categoryTreeRef.value.filter('');
    }

    // 显示分类对话框
    classifyDialogVisible.value = true;
  } else if (multiSelectMode.value === 'abandon') {
    // 批量弃用操作
    batchAbandonTags();
  }
};


// 存储当前批量分类的标签ID数组
let currentBatchClassifyTagIds = [];

// 删除待处理标签（单个或批量）
const deletePendingTag = async (tagIds) => {
  // 判断是单个标签还是多个标签
  const isBatch = Array.isArray(tagIds);
  const ids = isBatch ? tagIds : [tagIds];

  // 如果是单个标签，找到标签信息
  let tagContent = '';
  if (!isBatch) {
    const tagToAbandon = pendingTags.value.find(tag => tag.id === tagIds);
    if (tagToAbandon) {
      tagContent = tagToAbandon.content;
    }
  }

  // 确认提示信息
  const confirmMessage = isBatch
    ? `确定要弃用选中的 ${ids.length} 个标签吗？`
    : `确定要弃用标签「${tagContent}」吗？`;

  ElMessageBox.confirm(
    confirmMessage,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(async () => {
      try {
        // 调用弃用标签接口
        const result = await post(`${api_url}/tag/deletePendingTag`, JSON.stringify({
          ids: ids
        }));

        if (result.error_code === 0) {
          // 成功提示
          const successMessage = isBatch
            ? `已成功弃用 ${ids.length} 个标签`
            : '标签已成功弃用';
          ElMessage.success(successMessage);

          // 刷新待处理标签数据
          resetPendingTags();

          // 如果是批量操作，退出多选模式
          if (isBatch) {
            cancelMultiSelect();
          } else {
            // 关闭操作按钮
            activeTagId.value = null;
          }
        } else {
          const errorMessage = isBatch ? '批量弃用标签失败' : '弃用标签失败';
          ElMessage.error(result.msg || errorMessage);

          // 如果是单个操作，关闭操作按钮
          if (!isBatch) {
            activeTagId.value = null;
          }
        }
      } catch (error) {
        console.error('弃用标签异常:', error);
        const errorMessage = isBatch ? '批量弃用标签异常' : '弃用标签异常';
        ElMessage.error(errorMessage);

        // 如果是单个操作，关闭操作按钮
        if (!isBatch) {
          activeTagId.value = null;
        }
      }
    })
    .catch(() => {
      // 用户取消操作
    });
};

// 批量弃用标签
const batchAbandonTags = async () => {
  deletePendingTag(selectedTags.value);
};





// 计算属性 - 综合考虑分类和标签内容的筛选
// 筛选二级分类的方法
const getFilteredSubCategories = (category) => {
  const categorySearchTerm = searchTagCategory.value.trim().toLowerCase();
  const tagSearchTerm = searchTagContent.value.trim().toLowerCase();
  

  // 如果没有搜索条件，返回所有二级分类
  if (!categorySearchTerm && !tagSearchTerm) {
    return category.children || [];
  }

  // 有搜索条件时，筛选匹配的二级分类
  return (category.children || []).filter(subCategory => {
    // 检查二级分类名称是否匹配
    const subCategoryMatches = categorySearchTerm ?
      subCategory.name.toLowerCase().includes(categorySearchTerm) : true;

    // 如果没有标签内容搜索，只考虑二级分类名称
    if (!tagSearchTerm) {
      return subCategoryMatches;
    }

    // 如果有标签内容搜索，检查是否有匹配的标签
    let hasMatchingTag = false;
    if (subCategory.tags && subCategory.tags.length > 0) {
      hasMatchingTag = subCategory.tags.some(tag =>
        tag.name && tag.name.toLowerCase().includes(tagSearchTerm)
      );
    }

    // 如果只搜索标签内容，则只返回包含匹配标签的分类
    if (tagSearchTerm && !categorySearchTerm) {
      return hasMatchingTag;
    }

    // 如果同时搜索分类名称和标签内容，则返回同时满足两个条件的结果
    if (categorySearchTerm && tagSearchTerm) {
      // 如果一级分类名称匹配，则返回该分类
      if (categoryMatches) {
        return true;
      }
      // 否则检查是否有匹配的二级分类
      return hasMatchingTag;
    }

    // 其他情况
    return subCategoryMatches;
  });
};

// 过滤父分类列表的计算属性，用于移动分类对话框
const filteredParentCategories = computed(() => {
  // 获取所有顶级分类
  const parentCats = categories.value.filter(category => {
    // 如果有搜索关键词，按名称或描述搜索
    if (searchMoveCategory.value.trim() !== '') {
      const searchTerm = searchMoveCategory.value.trim().toLowerCase();
      const nameMatch = category.name.toLowerCase().includes(searchTerm);
      const descMatch = category.description && category.description.toLowerCase().includes(searchTerm);
      return nameMatch || descMatch;
    }
    return true; // 没有搜索关键词时返回所有父分类
  });

  return parentCats;
});

// 确认移动分类函数
const confirmMoveCategory = async () => {
  if (!selectedMoveCategory.value) {
    ElMessage.warning('请选择目标分类');
    return;
  }
  try {
    const params = {
      ids: select_arr.value,  // 选中的子分类IDs
      category_id: selectedMoveCategory.value // 目标父分类ID
    };
    // 发起请求
    let url = `${api_url}/tag/moveCategories`;
    const result = await post(url, JSON.stringify(params));
    if (result.error_code === 0) {
      ElMessage.success('移动分类成功');
      moveCategoryDialogVisible.value = false;
      // 重新加载分类树
      fetchCategoryTree();
      // 重置选择状态
      selectedMoveCategory.value = null;
    } else {
      ElMessage.error(result.message || '移动分类失败');
    }
  } catch (error) {
    console.error('移动分类异常:', error);
    ElMessage.error('移动分类异常');
  }
};

const filteredCategories = computed(() => {
  const categorySearchTerm = searchTagCategory.value.trim().toLowerCase();
  const tagSearchTerm = searchTagContent.value.trim().toLowerCase();

  // 如果没有输入任何搜索条件，返回所有分类
  if (!categorySearchTerm && !tagSearchTerm) {
    return categories.value;
  }

  return categories.value.filter(category => {
    // 分类名称筛选（一级分类）
    const categoryMatches = categorySearchTerm ?
      category.name.toLowerCase().includes(categorySearchTerm) : true;

    // 检查是否有匹配的二级分类
    const filteredSubCategories = getFilteredSubCategories(category);
    const hasMatchingSubCategories = filteredSubCategories.length > 0;

    // 如果只搜索标签内容，只返回有匹配标签的二级分类的一级分类
    if (tagSearchTerm && !categorySearchTerm) {
      return hasMatchingSubCategories;
    }

    // 如果只搜索分类名称，返回分类名称匹配的一级分类或者有匹配二级分类的一级分类
    if (categorySearchTerm && !tagSearchTerm) {
      return categoryMatches || hasMatchingSubCategories;
    }

    // 如果同时搜索分类名称和标签内容，返回分类名称匹配并且有匹配标签的二级分类的一级分类
    if (categorySearchTerm && tagSearchTerm) {
      // 如果一级分类名称匹配，则返回该分类
      if (categoryMatches) {
        return true;
      }
      // 否则检查是否有匹配的二级分类
      return hasMatchingSubCategories;
    }

    // 默认情况
    return false;
  });
});

// 根据类型获取标签
const getTagsByType = (tags, type) => {
  return tags.filter(tag => tag.type === type);
};

// 格式化标签数据以适配TagsGrid组件
const getFormattedTagsForGrid = (tags, subCategoryId, tagType) => {
  return tags.map(tag => ({
    id: tag.id,
    content: tag.name, // TagsGrid组件使用content属性显示标签内容
    name: tag.name,    // 保留原始name属性
    selected: tag.selected || false,
    subCategoryId,
    tagType
  }));
};

// 检查标签是否在批量选择中被选中
const isTagSelectedInBatch = (subCategoryId, tagType, tagId) => {
  const key = `${subCategoryId}-${tagType}`;
  return selectedTagIds.value[key]?.includes(tagId) || false;
};


// 方法
const toggleCategory = (categoryId) => {
  if (expandedCategories.value.includes(categoryId)) {
    expandedCategories.value = expandedCategories.value.filter(id => id !== categoryId);
  } else {
    expandedCategories.value.push(categoryId);
  }
};

const toggleSubCategory = (subCategoryId) => {
  if (expandedSubCategories.value.includes(subCategoryId)) {
    expandedSubCategories.value = expandedSubCategories.value.filter(id => id !== subCategoryId);
  } else {
    expandedSubCategories.value.push(subCategoryId);
  }
};

const removeTag = async (subCategoryId, tagId) => {
  ElMessageBox.confirm(
    `确定要删除吗？删除后将无法恢复。`,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
  .then(async () => {
    try {
      // 调用接口删除标签
      const result = await post(`${api_url}/tag/deleteTag`, JSON.stringify({
        id: tagId
      }));
  
      if (result.error_code === 0) {
        ElMessage.success('标签已删除');
        deleteOther(result.data)
        // 重新获取分类数据，刷新标签列表
        await fetchCategoryTree();
      } else {
        ElMessage.error(result.msg || '标签删除失败');
      }
    } catch (error) {
      console.error('删除标签异常:', error);
      ElMessage.error('删除标签异常');
    }
  })
  .catch(() => {
    // 用户取消删除操作
  });
};

// 开始添加标签（点击添加标签按钮时调用）
const startAddingTag = (subCategoryId, tagType) => {
  currentSubCategoryId.value = subCategoryId;
  currentTagType.value = tagType || 1; // 默认为对比标签类型
  newTagContent.value = '';
  isAddingTag.value = true;
};

// 取消添加标签
const cancelAddingTag = () => {
  isAddingTag.value = false;
  newTagContent.value = '';
};


// RGA下载
const RAGDownload = () => {
  let datas = {
    entities: [],
    relationships:[]
  }
  let existingNames = new Set();
  categories.value.forEach((category) => {
    category.children.forEach((subCategory) => {
       subCategory.tags.forEach((tag) => {
        // 检查当前标签的名称是否已经存在
        if (!existingNames.has(tag.name)) {
          // 如果不存在，则添加到entities数组和existingNames集合中
          datas.entities.push({
            entity_name: tag.name,
            description: "",
            entity_type: "病症标签",
            source_id: tag.id
          });
          existingNames.add(tag.name);
        }
      });
    });
  });
  // 将datas对象转换为JSON字符串
  let jsonData = JSON.stringify(datas, null, 2); // null和2参数用于格式化JSON字符串，使其更易读

  // 创建Blob对象
  let blob = new Blob([jsonData], { type: 'application/json' });

  // 创建链接元素
  let a = document.createElement('a');
  a.href = URL.createObjectURL(blob);
  a.download = 'datas.json'; // 设置下载文件的文件名

  // 触发下载
  a.click();

  // 释放URL对象
  URL.revokeObjectURL(a.href);
}
// 标签下载
const tagDownload = () => {
  let xlsxData = dealXlsxData(filteredCategories.value)
  
  // 创建工作簿
  const wb = XLSX.utils.book_new();
  // 将数据转换为工作表
  const ws = XLSX.utils.json_to_sheet(xlsxData);
  // 将工作表添加到工作簿
  XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');
  // 导出 Excel 文件
  XLSX.writeFile(wb, '标签' + '.xlsx');
}
const dealXlsxData = (data) => {
  let newData = []
  
  data.forEach((item) => {
    item.children.forEach((subItem) => {
      let d = {
        "一级标签": item.description,
        "二级标签名称": subItem.name,
        "对比标签": subItem.tags.filter((tag) => tag.type=='1').map((tag) => tag.name).join(','),
        "替换标签": subItem.tags.filter((tag) => tag.type=='2').map((tag) => tag.name).join(','),
      }
      
      try {
        d["二级标签描述"] = JSON.parse(subItem.description)['标签描述']
      }catch (e) {
        d["二级标签描述"] = ''
      }
      newData.push(d)
    })

  })
  return newData
}

// 显示批量新增对话框
const showBatchAddDialog = () => {
  batchTagsInput.value = '';  // 清空输入框
  batchAddDialogVisible.value = true;
};

// 显示二级分类添加标签对话框
const addSubCategoryTag = (subCategory) => {
  subCategoryTagInput.value = '';  // 清空输入框
  currentSubCategory.value = subCategory;  // 保存当前二级分类
  subCategoryAddTagVisible.value = true;
};

// 显示一级分类添加标签对话框
const addCategoryTag = (category) => {
  categoryTagInput.value = '';  // 清空输入框
  currentCategory.value = category;  // 保存当前一级分类
  selectedSubCategoryForTag.value = '';  // 清空选中的二级分类

  // 获取该一级分类下的所有二级分类
  subCategoriesForSelect.value = category.children || [];

  categoryAddTagVisible.value = true;
};

const showCategoryDialogLoading = ref(false); // 弹窗加载状态
// 显示新增分类弹窗
const showCategoryDialog = () => {
  // 重置表单
  newCategory.value = {
    pid: 0,
    name: '',
    description: ''
  };

  // 标记为新增模式
  isEditMode.value = false;

  // 显示弹窗
  categoryDialogVisible.value = true;
};

// 打开编辑分类弹窗
const editCategory = (category) => {
  // 设置为编辑模式并填充现有分类数据
  isEditMode.value = true;
  newCategory.value = {
    id: category.id,
    pid: category.pid || 0,
    name: category.name,
    description: category.description || '{\n"标签描述":"xxxx",\n"近义词":"xxxx"\n}'
  };

  // 如果是根节点，在控制台输出提示信息
  if (category.pid === 0) {
    console.log('正在编辑根节点，上级分类将被禁用');
  }

  // 显示弹窗
  categoryDialogVisible.value = true;
};

// 确认删除分类
const confirmDeleteCategory = (categoryId, categoryName, category) => {
  ElMessageBox.confirm(
    `确定要删除分类「${categoryName}」吗？删除后将无法恢复。`,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(() => {
      deleteOther(category)
      deleteCategory(categoryId);
    })
    .catch(() => {
      // 用户取消删除操作
    });
};

// 执行删除分类
const deleteCategory = async (categoryId) => {
  try {
    // 调用接口删除分类
    const result = await post(`${api_url}/tag/deleteCategory`, JSON.stringify({
      id: categoryId
    }));

    if (result.error_code === 0) {
      ElMessage.success('分类删除成功');
      // 重新获取分类数据
      fetchCategoryTree();
    } else {
      ElMessage.error(result.msg || '分类删除失败');
    }
  } catch (error) {
    console.error('删除分类异常:', error);
    ElMessage.error('删除分类异常');
  }
};

// 自定义节流函数
const throttle = (fn, delay) => {
  let lastCall = 0;
  return function (...args) {
    const now = new Date().getTime();
    if (now - lastCall < delay) {
      return;
    }
    lastCall = now;
    return fn.apply(this, args);
  };
};

// 处理添加或更新分类的原始函数
const _handleAddCategory = async () => {
  // 验证分类名称
  if (!newCategory.value.name.trim()) {
    ElMessage.error('请输入分类名称');
    return;
  }

  try {
    // 准备请求数据
    const requestData = {
      pid: newCategory.value.pid,
      name: newCategory.value.name.trim(),
      description: newCategory.value.description.trim()
    };

    // 如果是编辑模式，添加id参数并调用更新接口
    let apiUrl = `${api_url}/tag/createCategory`;
    let successMessage = '分类添加成功';

    if (isEditMode.value && newCategory.value.id) {
      apiUrl = `${api_url}/tag/updateCategory`;
      requestData.id = newCategory.value.id;
      successMessage = '分类更新成功';
    }
    showCategoryDialogLoading.value = true; // 弹窗加载状态

    // 发送请求
    const result = await post(apiUrl, JSON.stringify(requestData));

    if (result.error_code === 0) {
      ElMessage.success(successMessage);
      // 关闭弹窗
      categoryDialogVisible.value = false;
      // 重新加载分类数据
      fetchCategoryTree();
      showCategoryDialogLoading.value = false; // 弹窗加载状态

    } else {
      ElMessage.error(result.msg || (isEditMode.value ? '分类更新失败' : '分类添加失败'));
      showCategoryDialogLoading.value = false; // 弹窗加载状态

    }
  } catch (error) {
    console.error(isEditMode.value ? '更新分类异常:' : '添加分类异常:', error);
    ElMessage.error(isEditMode.value ? '更新分类异常' : '添加分类异常');
    showCategoryDialogLoading.value = false; // 弹窗加载状态

  }
};

// 使用节流处理函数，限制1秒内只能调用一次
const handleAddCategory = throttle(_handleAddCategory, 1000);
// 开始标签批量模式
function startTagBatchMode(subCategoryId, tagType) {
  // 初始化该分类和标签类型的批量选择状态
  isTagBatchMode.value[`${subCategoryId}-${tagType}`] = true;
  tagBatchSelectAll.value[`${subCategoryId}-${tagType}`] = false;
  selectedTagIds.value[`${subCategoryId}-${tagType}`] = [];

  // 初始化标签的选中状态
  const subCategory = getSubCategoryById(subCategoryId);
  if (subCategory && subCategory.tags) {
    const tags = getTagsByType(subCategory.tags, tagType);
    tags.forEach(tag => {
      tag.selected = false;
    });
  }
}

// 取消标签批量模式
function cancelTagBatchMode(subCategoryId, tagType) {
  isTagBatchMode.value[`${subCategoryId}-${tagType}`] = false;
  selectedTagIds.value[`${subCategoryId}-${tagType}`] = [];
}

// 切换标签在批量选择中的状态
function toggleTagInBatch(subCategoryId, tagType, tagId) {
  if (!selectedTagIds.value[`${subCategoryId}-${tagType}`]) {
    selectedTagIds.value[`${subCategoryId}-${tagType}`] = [];
  }

  const subCategory = getSubCategoryById(subCategoryId);
  if (subCategory && subCategory.tags) {
    const tags = getTagsByType(subCategory.tags, tagType);
    const tag = tags.find(t => t.id === tagId);

    if (tag) {
      // 切换标签的选中状态
      tag.selected = !tag.selected;
      
      if (tag.selected) {
        // 如果标签被选中，添加到选中列表
        if (!selectedTagIds.value[`${subCategoryId}-${tagType}`].includes(tagId)) {
          selectedTagIds.value[`${subCategoryId}-${tagType}`].push(tagId);
        }
      } else {
        // 如果标签取消选中，从选中列表中移除
        selectedTagIds.value[`${subCategoryId}-${tagType}`] =
          selectedTagIds.value[`${subCategoryId}-${tagType}`].filter(id => id !== tagId);

        // 如果有标签取消选中，全选状态也要取消
        tagBatchSelectAll.value[`${subCategoryId}-${tagType}`] = false;
      }
    }
  }
}

// 全选/取消全选标签
function toggleSelectAllTags(subCategoryId, tagType) {
  const isSelectAll = tagBatchSelectAll.value[`${subCategoryId}-${tagType}`];
  const subCategory = getSubCategoryById(subCategoryId);

  if (subCategory && subCategory.tags) {
    const tags = getTagsByType(subCategory.tags, tagType);

    // 更新所有标签的选中状态
    tags.forEach(tag => {
      tag.selected = isSelectAll;
    });

    // 更新选中的标签ID列表
    if (isSelectAll) {
      selectedTagIds.value[`${subCategoryId}-${tagType}`] = tags.map(tag => tag.id);
    } else {
      selectedTagIds.value[`${subCategoryId}-${tagType}`] = [];
    }
  }
}

// 检查是否有选中的标签
function hasSelectedTags(subCategoryId, tagType) {
  return selectedTagIds.value[`${subCategoryId}-${tagType}`]?.length > 0;
}

// 根据ID获取子分类
function getSubCategoryById(subCategoryId) {
  for (const category of categories.value) {
    if (category.children) {
      const subCategory = category.children.find(sub => sub.id === subCategoryId);
      if (subCategory) return subCategory;
    }
  }
  return null;
}

// 批量删除标签
async function batchDeleteTags(subCategoryId, tagType) {
  const selectedIds = selectedTagIds.value[`${subCategoryId}-${tagType}`];
  if (!selectedIds || selectedIds.length === 0) {
    ElMessage.warning('请至少选择一个标签');
    return;
  }

  try {
    await ElMessageBox.confirm(`确定要删除选中的 ${selectedIds.length} 个标签吗？`, '批量删除标签', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    });

    loading.value = true;
    const tagIdsString = selectedIds.join(',');

    try {
      const result = await post(`${api_url}/tag/deleteTag`, JSON.stringify({
        id: tagIdsString
      }));

      if (result.error_code === 0) {
        ElMessage.success('批量删除标签成功');
        deleteOther(result.data);
        // 重新加载分类树
        await fetchCategoryTree();
        // 退出批量模式
        cancelTagBatchMode(subCategoryId, tagType);
      } else {
        ElMessage.error(result.msg || '批量删除标签失败');
      }
    } catch (error) {
      console.error('批量删除标签出错:', error);
      ElMessage.error('操作失败，请稍后重试');
    } finally {
      loading.value = false;
    }
  } catch {
    // 用户取消删除操作
  }
}

// 新增标签额外调用接口
const addOther = async (datas) => {
  let url = baseUrl.url2
  let param = {
    operation: 'add',
    data: {
      entities: datas.map(item => {
        return {
          entity_name: item.name,
          entity_type: '病症标签',
          description: '',
          source_id: item.id
        }
      })
    }
  }
  const result = await fetch(`${url}/update_custom_kg`,{
    method: "POST",
    headers: {
      'Content-Type': 'application/json',
      'X-Tenant-ID': '12345'
    },
    body:  JSON.stringify(param),
  })
}
// 删除标签额外调用接口
const deleteOther = async (datas) => {
  let url = baseUrl.url2

  let param = {
    operation: 'delete',
    del_entity_name: [],
    data: null
  }
  if (datas.length) {
    param.del_entity_name = datas
  }
  else if (datas?.children?.length) {
    datas.children.forEach(item => {
      item.tags.forEach(tag => {
        param.del_entity_name.push(tag.name)
      })
    })
  }
  else if (datas?.tags?.length) {
    datas.tags.forEach(tag => {
      param.del_entity_name.push(tag.name)
    })
  }
  const result = await fetch(`${url}/update_custom_kg`,{
    method: "POST",
    headers: {
      'Content-Type': 'application/json',
      'X-Tenant-ID': '12345'
    },
    body:  JSON.stringify(param),
  })
}
const handleAddTag = async () => {
  if (!newTagContent.value.trim()) {
    ElMessage.error('请输入标签内容');
    return;
  }

  // 从当前子分类ID中提取真实ID和标签类型
  let realSubCategoryId = currentSubCategoryId.value;
  let tagType = currentTagType.value;

  // 如果当前ID包含类型信息，则提取
  if (typeof currentSubCategoryId.value === 'string' && currentSubCategoryId.value.includes('-')) {
    const parts = currentSubCategoryId.value.split('-');
    realSubCategoryId = parseInt(parts[0]);
    tagType = parseInt(parts[1]);
  }

  

  try {
    // 调用接口添加标签
    const result = await post(`${api_url}/tag/createTag`, JSON.stringify({
      name: newTagContent.value.trim(),
      category_id: realSubCategoryId,
      type: tagType // 1：对比标签，2：替换标签
    }));

    if (result.error_code === 0) {
      ElMessage.success('标签添加成功');
      addOther(result.data);
      // 重新获取所有相关数据
      await fetchCategoryTree();
      await fetchPendingTags();
    } else {
      ElMessage.error(result.msg || '标签添加失败');
    }
  } catch (error) {
    console.error('添加标签异常:', error);
    ElMessage.error('添加标签异常');
  }

  // 重置状态（既适用于弹窗也适用于直接编辑模式）
  tagModalVisible.value = false;
  isAddingTag.value = false;
  newTagContent.value = '';
};

// 全局点击处理函数
let closeHandler = null;

// 显示标签操作按钮
const showTagActions = (event, tagId) => {

  // 防止冲突，阻止事件冒泡
  event.stopPropagation();

  // 如果点击的是当前已激活的标签，则关闭操作按钮
  if (activeTagId.value === tagId) {
    activeTagId.value = null;
    // 移除先前的监听器
    if (closeHandler) {
      document.removeEventListener('click', closeHandler);
      closeHandler = null;
    }
  } else {
    // 先关闭其它标签的操作按钮，再显示当前标签的操作按钮
    activeTagId.value = tagId;

    // 移除先前的监听器
    if (closeHandler) {
      document.removeEventListener('click', closeHandler);
    }

    // 添加新的点击其他区域关闭按钮的处理
    setTimeout(() => {
      closeHandler = () => {
        activeTagId.value = null;
        document.removeEventListener('click', closeHandler);
        closeHandler = null;
      };
      document.addEventListener('click', closeHandler);
    }, 10);
  }
};

// 标签树节点过滤器
const filterCategoryNode = (value, data) => {
  if (!value) return true;
  return data.name.toLowerCase().includes(value.toLowerCase()) ||
    (data.description && data.description.toLowerCase().includes(value.toLowerCase()));
};

// 处理分类选择
const handleCategorySelect = (data) => {
  // 如果是可选分类，记录当前选中的分类 ID
  selectedCategoryId.value = data.id;
  console.log('选择分类:', data.name, 'ID:', data.id);
};

// 确认批量新增
const confirmBatchAdd = () => {
  // 验证输入内容
  if (!batchTagsInput.value.trim()) {
    ElMessage.error('请输入标签内容');
    return;
  }

  // 关闭批量新增对话框，打开分类选择对话框
  batchAddDialogVisible.value = false;

  // 重置搜索内容
  searchClassifyCategory.value = '';
  if (categoryTreeRef.value) {
    categoryTreeRef.value.filter('');
  }

  // 显示分类对话框
  classifyDialogVisible.value = true;
};

// 确认添加二级分类标签
const confirmAddSubCategoryTag = async () => {
  // 验证输入内容
  if (!subCategoryTagInput.value.trim()) {
    ElMessage.error('请输入标签内容');
    return;
  }

  if (!currentSubCategory.value || !currentSubCategory.value.id) {
    ElMessage.error('分类信息无效');
    return;
  }

  try {
    const result = await post(`${api_url}/tag/createTag`, JSON.stringify({
      name: subCategoryTagInput.value.trim(),
      category_id: currentSubCategory.value.id,
      type: 1 // 默认为对比标签类型
    }));
    if (result.error_code === 0) {
      ElMessage.success('标签创建成功');
      subCategoryAddTagVisible.value = false;
      // 刷新分类树以显示新添加的标签
      fetchCategoryTree();
    } else {
      ElMessage.error(result.message || '标签创建失败');
    }
  } catch (error) {
    console.error('标签创建错误:', error);
    ElMessage.error('网络错误，请稍后重试');
  }
};

// 确认在一级分类下添加标签
const confirmAddCategoryTag = async () => {
  // 验证输入内容
  if (!categoryTagInput.value.trim()) {
    ElMessage.error('请输入标签内容');
    return;
  }

  if (!selectedSubCategoryForTag.value) {
    ElMessage.error('请选择二级分类');
    return;
  }

  try {
    const result = await post(`${api_url}/tag/createTag`, JSON.stringify({
      name: categoryTagInput.value.trim(),
      category_id: selectedSubCategoryForTag.value,
      type: 1 // 默认为对比标签类型
    }));
    if (result.error_code === 0) {
      ElMessage.success('标签创建成功');
      categoryAddTagVisible.value = false;
      // 刷新分类树以显示新添加的标签
      fetchCategoryTree();
    } else {
      ElMessage.error(result.message || '标签创建失败');
    }
  } catch (error) {
    console.error('标签创建错误:', error);
    ElMessage.error('网络错误，请稍后重试');
  }
};

// 确认分类
const confirmClassify = async () => {
  if (!selectedCategoryId.value) {
    ElMessage.warning('请选择一个分类');
    return;
  }

  // 判断处理类型：批量新增、批量分类或单个分类
  // 如果有批量新增的输入，则优先处理批量新增
  if (batchTagsInput.value.trim()) {
    try {
      // 直接发送标签字符串
      const result = await post(`${api_url}/tag/createTag`, JSON.stringify({
        name: batchTagsInput.value.trim(),
        category_id: selectedCategoryId.value,
        type: 1 // 默认为对比标签类型
      }));
      if (result.error_code === 0) {
        ElMessage.success('标签批量创建成功');
        addOther(result.data);
        // 重置批量标签输入
        batchTagsInput.value = '';

        // 刷新所有相关数据
        await fetchCategoryTree();
        await fetchPendingTags();
      } else {
        ElMessage.error(result.msg || '批量创建标签失败');
        batchTagsInput.value = '';
      }

      // 关闭分类对话框
      classifyDialogVisible.value = false;

      return; // 处理完批量新增后直接返回，不继续处理其他情况

    } catch (error) {
      console.error('批量新增标签异常:', error);
      ElMessage.error('批量新增标签异常');
      // 关闭分类对话框
      batchTagsInput.value = '';
      classifyDialogVisible.value = false;
      return;
    }
  }

  // 处理其他类型的分类操作
  let tagIds = [];

  // 如果是批量分类操作
  if (currentBatchClassifyTagIds.length > 0) {
    tagIds = currentBatchClassifyTagIds;
  }
  // 如果是单个标签分类操作
  else if (currentClassifyTagId.value) {
    tagIds = [currentClassifyTagId.value];
  } else {
    ElMessage.error('缺少标签信息');
    return;
  }

  try {
    // 调用分类标签接口
    const result = await post(`${api_url}/tag/updatePendingTag`, JSON.stringify({
      ids: tagIds,
      category_id: selectedCategoryId.value
    }));

    if (result.error_code === 0) {
      // 根据处理模式显示不同的成功消息
      if (currentBatchClassifyTagIds.length > 0) {
        ElMessage.success(`已成功分类 ${currentBatchClassifyTagIds.length} 个标签`);
        // 清空批量处理数组
        currentBatchClassifyTagIds = [];
        // 退出多选模式
        cancelMultiSelect();
      } else {
        ElMessage.success('标签分类成功');
      }

      // 关闭分类对话框
      classifyDialogVisible.value = false;
      // 刷新所有相关数据
      fetchPendingTags();
      // 刷新分类树结构数据
      fetchCategoryTree();
    } else {
      ElMessage.error(result.msg || '标签分类失败');
    }
  } catch (error) {
    console.error('分类标签异常:', error);
    ElMessage.error('分类标签异常');
  }
};

// 打开分类标签对话框
const classifyTag = (tagId) => {
  // 记录当前标签 ID
  currentClassifyTagId.value = tagId;

  // 重置搜索内容
  searchClassifyCategory.value = '';
  if (categoryTreeRef.value) {
    categoryTreeRef.value.filter('');
  }

  // 显示分类对话框
  classifyDialogVisible.value = true;

  // 关闭操作按钮
  activeTagId.value = null;
};

// 弃用标签
const abandonTag = async (tagId) => {
  deletePendingTag(tagId);
};

</script>

<style scoped>
/* 已选中分类的标签样式 */
.selected-categories-tags {
  margin-bottom: 16px;
  padding: 10px;
  border-radius: 4px;
  background-color: #f9f9f9;
  display: flex;
  align-items: flex-start;
}

.tag-label {
  font-size: 14px;
  margin-right: 8px;
  color: #606266;
  margin-top: 5px;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  flex: 1;
}

.category-tag {
  margin-right: 8px;
  margin-bottom: 8px;
}

/* 基础样式 */
body {
  margin: 0;
  padding: 0;
}

/* 新增分类表单样式 */
.category-form {
  padding: 10px;
}

.form-item {
  margin-bottom: 20px;
}

.form-item label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
}

.required {
  color: #f56c6c;
  margin-right: 4px;
}

.tag-library-container {
  display: flex;
  width: 100%;
  height: 100%;
  /* padding: 20px; */
  background-color: #f5f5f5;
  gap: 20px;

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;

    h3 {
      margin: 0;
      font-size: 16px;
    }

    .header-actions {
      display: flex;
      gap: 10px;
    }
  }

  /* 左侧区域 */
  .used-tags-section {
    flex: 1;
    background-color: white;
    border-radius: 4px;
    padding: 15px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;

    .search-bar {
      display: flex;
      gap: 10px;
      margin-bottom: 15px;
    }

    .category-list {

      .category-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 10px;
        margin-bottom: 5px;
        border-radius: 4px;
        transition: background-color 0.2s ease;

        &:hover {
          background-color: #f9f9f9;
        }

        .category-info {
          display: flex;
          align-items: center;
          cursor: pointer;
          flex: 1;

          .expand-icon {
            margin-right: 10px;
            font-size: 12px;
          }
        }

        .category-actions {
          display: flex;
          gap: 5px;
          opacity: 0;
          transition: opacity 0.2s ease;
        }

        &:hover .category-actions {
          opacity: 1;
        }

        .tag-count {
          background-color: #ffcc00;
          color: white;
          border-radius: 50%;
          width: 20px;
          height: 20px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 12px;
          margin: 0 10px;
        }
      }

      .subcategory-list {
        padding-left: 20px;
        margin-bottom: 10px;

        .subcategory-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 8px;
          margin-bottom: 5px;
          border-radius: 4px;
          transition: background-color 0.2s ease;

          &:hover {
            background-color: #f9f9f9;
          }

          .subcategory-info {
            display: flex;
            align-items: center;
            cursor: pointer;
            flex: 1;

            .expand-icon {
              margin-right: 10px;
              font-size: 12px;
            }
          }

          .subcategory-actions {
            display: flex;
            gap: 5px;
            opacity: 0;
            transition: opacity 0.2s ease;
          }

          &:hover .subcategory-actions {
            opacity: 1;
          }
        }

        .tag-list {
          padding-left: 20px;
          margin-bottom: 10px;
          background-color: #f9f9f9;
          border-radius: 6px;
          padding: 15px 20px;

          .tag-group {
            margin-bottom: 20px;
            position: relative;
            border-radius: 6px;
            padding-left: 40px;

            /* 为垂直标签留出空间 */
            &.tag-group-1 {
              margin-bottom: 30px;

              .tag-type-indicator {
                background-color: #f5a623;
              }
            }

            &.tag-group-2 {
              .tag-type-indicator {
                background-color: #50e3c2;
              }
            }

            .tag-type-indicator {
              writing-mode: vertical-lr;
              display: flex;
              align-items: center;
              justify-content: center;
              padding: 8px 4px;
              color: white;
              border-radius: 4px;
              position: absolute;
              left: 0;
              top: 0;
              height: 100%;
              font-size: 14px;
            }

            .batch-actions-row {
              display: flex;
              align-items: center;
              gap: 8px;
              padding: 8px;
              background-color: #f5f5f5;
              border-top: 1px solid #eee;
              border-bottom: 1px solid #eee;
            }

            .tags-wrapper {
              /* display: flex; */
              flex-direction: column;
              margin-top: 0;
              padding: 15px;
              background-color: #f9f9f9;
              border-radius: 0 0 4px 4px;
              /* overflow-y: auto; */
              /* height: 300px; */


              /* TagsGrid组件样式覆盖 - 移除自定义样式，完全跟随TagsGrid */
              :deep(.pending-tags-grid) {
                padding: 0;
                margin-bottom: 10px;
              }
            }

            .add-tag {
              display: flex;
              align-items: center;
              justify-content: center;
              background-color: #e8f7ff;
              border-radius: 4px;
              padding: 8px 12px;
              height: 18px;
              margin-bottom: 8px;
              cursor: pointer;
              color: #1890ff;
              border: 1px dashed #1890ff;
            }

            .tag-input-wrapper {
              display: flex;
              height: 35px;

              .tag-input-buttons {
                display: flex;
                padding-left: 10px;
                gap: 10px;

                .el-button {
                  margin-left: 0;
                  height: 35px;
                  width: 70px;
                }
              }
            }
          }
        }
      }
    }
  }

  /* 右侧区域 */
  .right-section {
    width: 40%;
    display: flex;
    flex-direction: column;
    gap: 20px;

    /* 待处理标签 */
    .pending-tags-section {
      background-color: white;
      border-radius: 4px;
      padding: 15px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      display: flex;
      flex-direction: column;

      .pending-tags-grid {
        display: flex;
        flex-wrap: wrap;
        align-content: start;
        flex: 1 1 0;
        overflow: auto;

        .pending-tag-item {
          position: relative;
          cursor: pointer;
          display: inline-flex;
          align-items: center;
          border-radius: 4px;
          padding: 8px 12px;
          height: 18px;
          margin-bottom: 8px;
          margin-right: 8px;
          white-space: nowrap;
          overflow: visible;
          /* 改为visible确保弹出内容可见 */
          text-overflow: ellipsis;
          background-color: rgb(242, 247, 251);
          border: 1px solid rgb(181, 181, 181);
          transition: all 0.2s ease;

          &.tag-selected {
            background-color: #ecf5ff;
            border-color: #409eff;
            color: #409eff;
          }

          .selection-indicator {
            margin-right: 5px;
          }

          .tag-actions {
            position: absolute;
            top: 100%;
            left: 0;
            background-color: #fff;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1);
            border-radius: 4px;
            z-index: 1000;
            /* 提高z-index确保比其他元素层级高 */
            display: flex;
            overflow: visible;
            /* 增加可见性 */
            margin-top: 5px;
          }
        }
      }
    }

  }

}
.drag-preview {
  background: rgba(64,158,255,0.9);
  color: #fff;
  border-radius: 4px;
  padding: 6px 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
  min-width: 80px;
  pointer-events: none;
  font-size: 13px;
}
.drag-preview-item {
  padding: 2px 0;
}
.action-btn {
  border: none;
  padding: 6px 12px;
  cursor: pointer;
  font-size: 12px;
  text-align: center;
  white-space: nowrap;

  &.classify-btn {
    background-color: #f0f9eb;
    color: #67c23a;

    &:hover {
      background-color: #e1f3d8;
    }
  }

  &.abandon-btn {
    background-color: #fef0f0;
    color: #f56c6c;

    &:hover {
      background-color: #fde2e2;
    }
  }

  &.delete-btn {
    background-color: #fef0f0;
    color: #f56c6c;

    &:hover {
      background-color: #fde2e2;
    }
  }
}
.delete-icon {
  position: absolute;
  right: -6px;
  top: -6px;
  color: #ffffff;
  width: 12px;
  height: 12px;
  background-color: #ff0000;
  border-radius: 50%;
  font-size: 14px;
  cursor: pointer;
}
</style>