<template>
  <div class="btn-list">
    <div>
      <h3>综合病历页面</h3>
      <div class="item-btn"><el-button type="primary" link @click="linke('CaseList')">患者病历查询</el-button></div>
      <div class="item-btn"><el-button type="primary" link @click="linke('CueWord')">提示词调试</el-button></div>
      <div class="item-btn"><el-button type="primary" link @click="linke('CaseWriteCount')">书写病历统计</el-button></div>
      <div class="item-btn"><el-button type="primary" link @click="linke('CaseAbnormalUser')">异常患者病例</el-button></div>
      <div class="item-btn"><el-button type="primary" link @click="linke('CaseTest')">模版病例测试</el-button></div>
      <div class="item-btn"><el-button type="primary" link @click="linke('WordList')">提示词列表</el-button></div>
      <div class="item-btn"><el-button type="primary" link @click="linke('WordManage')">提示词管理</el-button></div>
      <div class="item-btn"><el-button type="primary" link @click="linke('TagList')">标签管理</el-button></div>
      <div class="item-btn"><el-button type="primary" link @click="linke('TaskList')">病例任务</el-button></div>
      <div class="item-btn"><el-button type="primary" link @click="linke('GenerateCases')">批量书写</el-button></div>
      <div class="item-btn"><el-button type="primary" link @click="linke('TaskUpload')">任务列表</el-button></div>
      <div class="item-btn"><el-button type="primary" link @click="linke('DiseaseList')">病种模版配置</el-button></div>
      <div class="item-btn"><el-button type="primary" link @click="linke('CaseTemplate')">创建模版</el-button></div>
      <div class="item-btn"><el-button type="primary" link @click="linke('HandCaseUpload')">手动上传病例</el-button></div>
      <div class="item-btn"><el-button type="primary" link @click="linke('TestHis')">测试his病例系统</el-button></div>
      <div class="item-btn"><el-button type="primary" link @click="linke('Statistics')">统计</el-button></div>
      <div class="item-btn"><el-button type="primary" link @click="linke('DoctorList')">医生列表</el-button></div>
      <div class="item-btn"><el-button type="primary" link @click="linke('TagLibrary')">标签库</el-button></div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import router from '@/router'
import TagList from "@/pages/tag_list.vue";
const outerVisible = ref(false)
const linke = (path)=>{
  router.push({name:path,params: {}})
}
</script>


<style scoped>
h3{
  margin-bottom: 15px;
}
.btn-list{
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.item-btn{
  margin-bottom: 15px;
}
</style>