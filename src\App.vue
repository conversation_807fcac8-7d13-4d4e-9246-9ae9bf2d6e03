<template>
  <el-container class="layout-container">
    <el-aside :width="isCollapse ? '70px' : '200px'" class="aside">
      <div class="logo">
        <h2 v-if="!isCollapse">病例管理</h2>
        <div class="toggle-icon">
          <el-icon v-if="!isCollapse" @click="toggleSidebar">
            <Fold />
          </el-icon>
          <el-icon v-if="isCollapse" @click="toggleSidebar">
            <Expand />
          </el-icon>
        </div>
      </div>
      <el-scrollbar height="calc(100vh - 60px)" ref="menuScrollbar">
        <el-menu :default-active="activeMenu" class="menu" background-color="#304156" text-color="#fff"
          active-text-color="#409EFF" router ref="menuRef" :collapse="isCollapse" @select="handleMenuSelect">
          <template v-for="(item, index) in menu">
            <!-- 标签管理1只在is_dev_str !== 'dist_zs'时显示 -->
            <template v-if="item.isShow">
              <el-sub-menu v-if="item.children" :index="`/${item.path}`" :key="item.path">
                <template #title>
                  <el-icon>
                    <component :is="item.icon || 'Menu'" />
                  </el-icon>
                  <span>{{ item.name }}</span>
                </template>
                <el-menu-item v-for="(sub, index1) in item.children" :index="`/${sub.path}`" :key="index1">
                  <el-icon>
                    <component :is="sub.icon || 'Menu'" />
                  </el-icon>
                  <span>{{ sub.name }}</span>
                </el-menu-item>
              </el-sub-menu>
              <el-menu-item v-else :index="`/${item.path}`" :key="index">
                <el-icon>
                  <component :is="item.icon || 'Menu'" />
                </el-icon>
                <span>{{ item.name }}</span>
              </el-menu-item>
            </template>
          </template>
        </el-menu>
      </el-scrollbar>
    </el-aside>
    <el-container>
      <el-main class="main">
        <transition name="fade" mode="out-in">
          <router-view></router-view>
        </transition>
      </el-main>
    </el-container>
  </el-container>
<!--  <router-view v-else></router-view>-->
</template>

<script setup>
import { ref, computed, onMounted, watch, nextTick } from 'vue'
import { useRoute } from 'vue-router'
const env = import.meta.env.VITE_APP_OUT_DIR
const route = useRoute()
const isCollapse = ref(false)
const menuScrollbar = ref(null)
const menuRef = ref(null)

const is_dev_str = ref(env)
const isTestEnv = computed(() => {
  return env === 'dist_test' || env === 'dist_dev'
})
const menu = [
  // { name: "提示词调试", path: "CueWord", isShow: true, icon: 'EditPen' },
  // { name: "模版病例测试", path: "CaseTest", isShow: true, icon: 'Document' },
  { name: "病种模版配置", path: "DiseaseList", isShow: true, icon: 'Folder' },
  { name: "创建模版", path: "CaseTemplate", isShow: true, icon: 'Plus' },
  { name: "提示词列表", path: "WordList", isShow: true, icon: 'List' },
  { name: "提示词管理", path: "WordManage", isShow: true, icon: 'Setting' },
  // { name: "标签管理", path: "TagList", isShow: true, icon: 'SetUp' },
  { name: "批量书写", path: "GenerateCases", isShow: true, icon: 'Edit' },
  { name: "任务上传对比", path: "TaskUpload", isShow: true, icon: 'Edit' },
  { name: "病例任务病例查询", path: "TaskList", isShow: true, icon: 'List' },
  // { name: "手动上传病例", path: "HandCaseUpload", isShow: true, icon: 'Plus' },
  { name: "任务数量查看", path: "HisTask", isShow: true, icon: 'Histogram' },
  {
    name: "统计管理",
    isShow: true,
    path: "StatisticsCollection",
    icon: 'Folder',
    children: [
      { name: "统计", path: "Statistics", isShow: true, icon: 'Histogram' },
      { name: "病历书写统计", path: "writingChart", isShow: true, icon: 'Histogram' },
    ]
  },
  { name: "医生列表", path: "DoctorList", isShow: true, icon: 'User' },
  { name: "新日常病例生成", path: "NewGenerateCases", isShow: true, icon: 'Edit' },
  { name: "生成病例列表", path: "GenerateCaseList", isShow: true, icon: 'List' },
  { name: "药品管理", path: "DrugManagement", isShow: true, icon: 'List' },
  { name: "标签管理", path: "Tag", icon: 'List', isShow: is_dev_str.value == 'dist_zs',  },
  {
    name: "AI管理",
    isShow: true,
    path: "AICollection",
    icon: 'Folder',
    children: [
      { name: "mcpSql对话", path: "AIButler", icon: 'Mic' },
      { name: "AI管家", path: "AiTest", icon: 'Mic' },
    ]
  },
  {
    isShow: is_dev_str.value !== 'dist_zs',
    name: "标签管理",
    path: "TagCollection",
    icon: 'Folder',
    children: [
      { name: "标签操作历史记录", path: "TagOperationHistory", icon: 'Reading' },
      { name: "单标签库", path: "TagLibrary", icon: 'Reading' },
      { name: "多标签库", path: "TagLibraryFuhe", icon: 'Reading' },
      { name: "待处理标签库", path: "tagLibraryList", icon: 'Reading' }
    ]
  },
  { name: "日志查询", path: "LogQuery", isShow: true, icon: 'List' },

]

if (isTestEnv.value) {
  menu.push({ name: "测试his病例系统", path: "TestHis", isShow: true, })
}

// 当前激活的菜单
const activeMenu = computed(() => route.path)


// 切换侧边栏
const toggleSidebar = () => {
  isCollapse.value = !isCollapse.value
}
function handleMenuSelect() {
  // 菜单点击后，确保滚动到激活项
  scrollToActiveMenu();
}
// 滚动到当前激活菜单项
function scrollToActiveMenu() {
  nextTick(() => {
    const menuEl = menuRef.value?.$el || menuRef.value;
    const activeItem = menuEl?.querySelector('.el-menu-item.is-active, .el-sub-menu__title.is-active');
    const scrollWrap = menuScrollbar.value?.$refs?.wrap || menuScrollbar.value?.wrapRef || null;
    if (activeItem && scrollWrap) {
      // 计算 activeItem 相对于 scrollWrap 的距离
      const activeRect = activeItem.getBoundingClientRect();
      const wrapRect = scrollWrap.getBoundingClientRect();
      const offsetTop = activeRect.top - wrapRect.top + scrollWrap.scrollTop;
      const itemHeight = activeItem.offsetHeight;
      const wrapHeight = scrollWrap.clientHeight;
      const scrollTop = scrollWrap.scrollTop;

      // 判断激活项是否在可视区
      if (
        offsetTop < scrollTop || // 上方不可见
        offsetTop + itemHeight > scrollTop + wrapHeight // 下方不可见
      ) {
        // 让激活项垂直居中
        scrollWrap.scrollTop = offsetTop - wrapHeight / 2 + itemHeight / 2;
      }
    }
  });
}

// 页面加载和路由变化时滚动
onMounted(scrollToActiveMenu)
watch(() => route.path, scrollToActiveMenu)
</script>

<style>
.webkit-scrollbar {
  -webkit-app-region: no-drag;

  &:hover {

    /* 滚动条滑块（里面小方块） */
    &::-webkit-scrollbar-thumb {
      border-radius: 2px;
      background: #a1c7fe;
    }

    /* 滚动条轨道 */
    &::-webkit-scrollbar-track {
      background: rgba(161, 199, 254, 0.2);
      border-radius: 2px;
    }
  }

  /* 滚动条样式 */
  &::-webkit-scrollbar {
    width: 0px;
    /*  设置纵轴（y轴）轴滚动条 */
    height: 0px;
    /*  设置横轴（x轴）轴滚动条 */
  }

  /* 滚动条滑块（里面小方块） */
  &::-webkit-scrollbar-thumb {
    border-radius: 0;
    background: rgba(0, 0, 0, 0);
  }

  /* 滚动条轨道 */
  &::-webkit-scrollbar-track {
    border-radius: 0;
    background: rgba(0, 0, 0, 0);
  }
}

* {
  margin: 0;
  padding: 0;
}

.layout-container {
  height: 100vh;
}

.aside {
  background-color: #304156;
  transition: width 0.3s;
  display: flex;
  flex-direction: column;
  height: 100vh;
  padding: 0;
  box-sizing: border-box;
  min-width: 70px;
  max-width: 200px;
}

.logo {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #fff;
  flex-shrink: 0;
  border-bottom: 1px solid #dcdfe6;
  padding: 0 10px;
}

.menu {
  border: none !important;
  box-sizing: border-box;
  width: 100%;
  min-width: 0;
  padding: 0;
  margin: 0;
  flex: 1 1 0;
}

.menu .el-menu-item.is-active,
.menu .el-sub-menu.is-active>.el-sub-menu__title {
  background-color: #409EFF !important;
  color: #fff !important;
}

.el-menu {
  width: 100% !important;
}

.header {
  background-color: #fff;
  border-bottom: 1px solid #dcdfe6;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
}

.toggle-sidebar {
  font-size: 20px;
  cursor: pointer;
}


.main {
  background-color: #f0f2f5;
  padding: 20px;
}

.toggle-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 70px;
  width: 70px;
  cursor: pointer;
}

.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s;
}
.fade-enter, .fade-leave-to {
  opacity: 0;
}
</style>