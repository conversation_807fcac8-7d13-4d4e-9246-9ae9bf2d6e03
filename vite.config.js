import { defineConfig,loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'

const pathResolve = (dir)=> {
  return resolve(__dirname, '.', dir)
}

export default defineConfig(({ command, mode }) => {
  const env = loadEnv(mode, process.cwd())

  return {
    base: './',
    resolve:{
      alias:{
        '@': pathResolve('./src'),
      }
    },
    server: {
      host: '0.0.0.0',
      port: 3001,
      https:false,
      cors:true,
      hmr:true,
    },
    build:{
      // outDir: 'ai_case', // 设置打包目录
      outDir: env.VITE_APP_OUT_DIR, // 设置打包目录
      // outDir: 'dist_test', // 设置打包目录
    },
    plugins: [
      vue(),
      AutoImport({
        resolvers: [ElementPlusResolver()],
      }),
      Components({
        resolvers: [ElementPlusResolver()],
      }),
    ]
  }
})
