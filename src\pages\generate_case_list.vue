<template>
    <div class="tableData">
        <h3>{{ route.meta.title }}</h3>
        <div class="topMenu">
            <div class="menuBox">
                <el-input style="display: none;"></el-input>
                <el-select class="menu" v-model="curHisUser" placeholder="医生名称" clearable @change="getGridDataAgain">
                    <el-option v-for="item in doctorList" :key="item.id" :label="item.username" :value="item.id" />
                </el-select>
                <el-input class="menu" placeholder="患者名称" v-model="curUserName" clearable
                    @change="getGridDataAgain"></el-input>
                <el-select class="menu" v-model="curState" placeholder="任务类型" clearable @change="getGridDataAgain">
                    <el-option v-for="item in taskTypeList" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>

                <el-select class="menu" v-model="curIsSys" placeholder="生成方式" clearable @change="getGridDataAgain">
                    <el-option label="前端手动创建" value="0" />
                    <el-option label="系统自动创建" value="1" />
                </el-select>

                <el-select class="menu" v-model="curUploadState" placeholder="上传状态" clearable
                    @change="getGridDataAgain">
                    <el-option label="未上传" value="not_upload" />
                    <el-option label="上传成功" value="upload_success" />
                    <el-option label="上传失败" value="upload_fail" />
                </el-select>
            </div>
            <div class="menuBox">
                <el-date-picker v-model="time1" type="datetimerange" range-separator="To" start-placeholder="创建开始时间"
                    end-placeholder="创建结束时间" @change="getGridDataAgain" />
            </div>
            <el-button type="primary" @click="tagDownload">导出</el-button>
            <el-button
                type="primary"
                @click="batchWriting"
                :disabled="selectedRows.length === 0 || batchWritingStatus.isProcessing"
                :loading="batchWritingStatus.isProcessing"
            >
                <span v-if="batchWritingStatus.isProcessing">
                    批量书写中 ({{ batchWritingStatus.current }}/{{ batchWritingStatus.total }})
                </span>
                <span v-else>
                    批量书写{{ selectedRows.length > 0 ? `(${selectedRows.length})` : '' }}
                </span>
            </el-button>
        </div>
        <div v-if="selectedRows.length > 0" class="selection-info">
            <span>已选择 {{ selectedRows.length }} 项</span>
            <span v-if="selectedRows.length > 10" style="color: #f56c6c; margin-left: 10px;">
                最多只能选择10个任务进行批量书写
            </span>
        </div>
        <el-table ref="tableRef" class="table" :data="filteredTableData" v-loading="load" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <!-- <el-table-column label="任务ID" prop="id" width="100" ></el-table-column> -->
            <el-table-column width="80" label="患者名称" prop="pat_name"></el-table-column>
            <el-table-column width="80" label="医生名称" prop="doctor_name"></el-table-column>
            <el-table-column width="120" label="任务类型" prop="state"></el-table-column>
            <el-table-column width="120" label="生成方式" prop="is_sys_display">
                <template #default="{ row }">
                    <p>{{ row.is_sys == 0 ? '前端手动创建' : '系统自动创建' }}</p>
                </template>
            </el-table-column>
            <el-table-column label="病例内容" prop="document">
                <!-- <template #default="{ row }">
                    <el-button type="primary" @click="lookContent(row)">查看</el-button>
                </template> -->
                <template #default="{ row }">
                    <el-scrollbar max-height="400px">
                        <div v-html="formatDocument(row.document)" style="white-space: pre-wrap;word-break: break-all;">
                        </div>
                    </el-scrollbar>
                </template>
            </el-table-column>
                        <el-table-column label="新病例内容" prop="document">
                <template #default="{ row }">
                    <el-scrollbar max-height="400px">
                        <div v-html="generatedContent[row.id]" style="white-space: pre-wrap;word-break: break-all;">
                        </div>
                    </el-scrollbar>
                </template>
            </el-table-column>

            <el-table-column width="160" label="病区" prop="department"></el-table-column>
            <el-table-column width="120" label="生成时间" prop="create_time"></el-table-column>
            <el-table-column width="80" label="上传状态" prop="upload_status_filter_display">
                <!-- <template #default="{ row }">
                    <p v-show="row.upload_status_filter == 'not_upload'">未上传</p>
                    <p v-show="row.upload_status_filter == 'upload_success'">上传成功</p>
                    <p v-show="row.upload_status_filter == 'upload_fail'">上传失败</p>
                </template> -->
            </el-table-column>
            <el-table-column label="操作" width="260" >
                <!-- <template #default="{ row }">
                    <el-button type="primary" @click="lookContent(row)">查看</el-button>
                </template> -->
                <template #default="{ row }">
                    <div>

                    <el-button
                        type="primary"
                        @click="writeTask(row)"
                        :disabled="row.isWriting || (batchWritingStatus.isProcessing && isRowInBatch(row.id)) || !checkTaskTypeSupported(row.state)"
                        :loading="row.isWriting || (batchWritingStatus.isProcessing && isRowInBatch(row.id))"
                    >
                        <span v-if="row.isWriting || (batchWritingStatus.isProcessing && isRowInBatch(row.id))">生成中...</span>
                        <span v-else-if="!checkTaskTypeSupported(row.state)">不支持生成</span>
                        <span v-else>生成病例</span>
                    </el-button>
                    <el-button type="primary" @click="viewTask(row)">查看内容</el-button>
                    </div>

                </template>
            </el-table-column>
        </el-table>
        <Pagination style="margin-top: 10px; display: flex; justify-content: flex-end;" :currentPage="currentPage"
            :pageSize="pageSize" :total="totalItems" @current-change="handleCurrentChange"
            @size-change="handleSizeChange" />
    </div>
    <el-dialog v-model="dialogVisible" :title="getDialogTitle()" width="90%" draggable>
        <template #default>
            <div v-if="dialogContent.isLoading" style="text-align: center; padding: 40px;">
                <el-icon class="is-loading" style="font-size: 24px;"><Loading /></el-icon>
                <p style="margin-top: 15px; font-size: 16px;">加载中...</p>
            </div>
            <div v-else class="content-container">
                <div class="content-section">
                    <div class="section-header">
                        <h4>原始病例内容</h4>
                    </div>
                    <el-scrollbar max-height="600px" class="section-scrollbar">
                        <div class="section-content">
                            <div v-if="!dialogContent.originalContent || dialogContent.originalContent === '暂无原始内容'"
                                 style="text-align: center; padding: 40px; color: #909399;">
                                <el-icon><Document /></el-icon>
                                <p style="margin-top: 10px;">暂无原始内容</p>
                            </div>
                            <div v-else style="white-space: pre-wrap; word-break: break-all; line-height: 1.6;">
                                <div v-html="formatDocument(dialogContent.originalContent)"></div>
                            </div>
                        </div>
                    </el-scrollbar>
                </div>

                <div class="content-section">
                    <div class="section-header">
                        <h4>新生成病例内容</h4>
                    </div>
                    <el-scrollbar max-height="600px" class="section-scrollbar">
                        <div class="section-content">
                            <div v-if="!dialogContent.generatedContent || dialogContent.generatedContent === '暂无生成内容'"
                                 style="text-align: center; padding: 40px; color: #909399;">
                                <el-icon><Document /></el-icon>
                                <p style="margin-top: 10px;">暂无生成内容</p>
                            </div>
                            <div v-else style="white-space: pre-wrap; word-break: break-all; line-height: 1.6;">
                                <div v-html="formatDocument(dialogContent.generatedContent)"></div>
                            </div>
                        </div>
                    </el-scrollbar>
                </div>

                <div class="content-section">
                    <div class="section-header">
                        <h4>接口数据</h4>
                    </div>
                    <el-scrollbar max-height="600px" class="section-scrollbar">
                        <div class="section-content">
                            <div v-if="dialogContent.apiContent === '获取接口数据失败，请稍后重试'"
                                 style="text-align: center; padding: 40px; color: #f56c6c;">
                                <el-icon><Warning /></el-icon>
                                <p style="margin-top: 10px;">{{ dialogContent.apiContent }}</p>
                            </div>
                            <div v-else-if="!dialogContent.apiContent || dialogContent.apiContent === '暂无接口数据'"
                                 style="text-align: center; padding: 40px; color: #909399;">
                                <el-icon><Document /></el-icon>
                                <p style="margin-top: 10px;">暂无接口数据</p>
                            </div>
                            <div v-else style="white-space: pre-wrap; word-break: break-all; line-height: 1.6;">
                                <div v-html="formatDocument(dialogContent.apiContent)"></div>
                            </div>
                        </div>
                    </el-scrollbar>
                </div>
            </div>
        </template>
    </el-dialog>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { ElMessage } from 'element-plus';
import { Loading, Warning, Document } from '@element-plus/icons-vue';
import c from "@/utils/config";
import Pagination from '@/components/Pagination.vue';
import dayjs from 'dayjs'
import { json } from 'd3';
import { useRoute } from 'vue-router';
import { baseUrl } from '../utils/baseUrl';
import * as XLSX from 'xlsx';
import config from '../utils/config';


const route = useRoute();
const api = config.py_api_url
const doctorList = ref([]) // 医生列表
const taskTypeList = ref([]) // 任务类型列表
const curState = ref('')
const curIsSys = ref('')
const curUploadState = ref('')
function formatDocument(doc) {
    if (!doc) return '';
    let obj = doc;
    if (typeof doc === 'string') {
        try {
            obj = JSON.parse(doc);
        } catch {
            return doc; // 不是JSON就原样返回
        }
    }
    // 优化缩进和换行，key加粗，内容自动换行，缩进更明显，移除空格，序号换行
    function pretty(obj, indent = 0) {
        if (typeof obj !== 'object' || obj === null) return String(obj);
        if (Array.isArray(obj)) {
            return obj.map(item => pretty(item, indent + 1)).join('<br>');
        }
        return Object.entries(obj).map(([k, v]) => {
            if (typeof v === 'object' && v !== null) {
                return `<div style="padding-left:${indent * 24}px; font-weight:bold;">${k}：</div>` + pretty(v, indent + 1);
            } else {
                // 移除所有空格
                let content = String(v).replace(/\s+/g, '');
                // 将（1）（2）等序号前加换行
                content = content.replace(/(\（\d+\）)/g, '<br>$1');
                // 将以符号或非汉字/字母结尾后跟1. 1、①、一、等序号前加换行（包括行首）
                content = content.replace(
                    /([^\u4e00-\u9fa5a-zA-Z0-9]|^)((\d+[\.、])|[①②③④⑤⑥⑦⑧⑨⑩]|[一二三四五六七八九十][\.、])/g,
                    '$1<br>$2'
                );
                // 将类似2025-06-19的日期前加换行（包括行首和符号后）
                content = content.replace(
                    /([^\d]|^)(\d{4}-\d{2}-\d{2})/g,
                    '$1<br>$2'
                );
                // 保留原有换行符，内容缩进更明显
                content = content
                    .replace(/\\n/g, '<br>' + '&nbsp;'.repeat((indent + 1) * 4))
                    .replace(/\n/g, '<br>' + '&nbsp;'.repeat((indent + 1) * 4));
                return `<div style="padding-left:${indent * 24}px;"><b>${k}：</b>${content}</div>`;
            }
        }).join('');
    }
    return pretty(obj);
}
// function writeTask(row){
//     console.log(1123,row);
    
// }
// 查看内容方法
const viewTask = async (row) => {
    try {
        // 显示加载状态
        dialogVisible.value = true
        dialogContent.value = {
            originalContent: null,
            generatedContent: null,
            apiContent: null,
            isLoading: true,
            currentRow: row
        }

        // 打印 row 对象以查看数据结构
        console.log('Row data:', row)

        // 设置原始病例内容
        dialogContent.value.originalContent = row.document || '暂无原始内容'

        // 设置新生成的病例内容
        dialogContent.value.generatedContent = generatedContent.value[row.id] || '暂无生成内容'

        // 构建请求参数 - 先检查字段是否存在
        const pid = row.pid || row.patient_id || row.pat_id || ''
        const pvid = row.pvid || row.visit_id || row.pv_id || ''

        console.log('Request params:', { pid, pvid })

        // 检查必要参数
        if (!pid && !pvid) {
            dialogContent.value.apiContent = '缺少必要参数 pid 或 pvid'
            dialogContent.value.isLoading = false
            ElMessage.warning('缺少必要参数，无法获取接口数据')
            return
        }

        const params = new URLSearchParams({
            pid: pid,
            pvid: pvid
        })

        const response = await fetch(`${api}/v1/api/format_case/case_xml?${params}`, {
            method: 'GET',
            headers: {
                'x-api-key': 'API655c18174462a06ede7ffbfa5aca4270c645b9b78ca30817e3a1bd496f34e04b',
                'authorization': 'Bearer 6787c226c3eae7.874922141Z3hNBMuGI'
            }
        })

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`)
        }

        const result = await response.json()

        // 设置接口数据内容
        if (result.data) {
            dialogContent.value.apiContent = result.data
        } else {
            dialogContent.value.apiContent = '暂无接口数据'
        }

    } catch (error) {
        console.error('获取内容失败:', error)
        dialogContent.value.apiContent = '获取接口数据失败，请稍后重试'
        ElMessage.error('获取接口数据失败')
    } finally {
        dialogContent.value.isLoading = false
    }
}
// 处理表格选择变化
const handleSelectionChange = (selection) => {
    selectedRows.value = selection
}

// 检查任务是否在批量处理中且未完成
const isRowInBatch = (taskId) => {
    return batchWritingStatus.value.batchTaskIds.includes(taskId) &&
           !batchWritingStatus.value.completedTaskIds.includes(taskId)
}

// 批量书写状态
const batchWritingStatus = ref({
    isProcessing: false,
    current: 0,
    total: 0,
    currentTaskId: null, // 当前正在处理的任务ID
    batchTaskIds: [], // 批量处理中的所有任务ID列表
    completedTaskIds: [] // 已完成的任务ID列表
})

// 批量书写功能
const batchWriting = async () => {
    if (selectedRows.value.length === 0) {
        ElMessage.warning('请先选择要书写的任务')
        return
    }

    if (selectedRows.value.length > 10) {
        ElMessage.warning('最多只能选择10个任务进行批量书写')
        return
    }

    // 过滤出支持生成的任务
    const supportedTasks = selectedRows.value.filter(row => checkTaskTypeSupported(row.state))
    const unsupportedTasks = selectedRows.value.filter(row => !checkTaskTypeSupported(row.state))

    if (unsupportedTasks.length > 0) {
        const unsupportedNames = unsupportedTasks.map(row => `${row.pat_name}(${row.state})`).join('、')
        ElMessage.warning(`以下任务类型暂时不支持生成病例，将跳过：${unsupportedNames}`)
    }

    if (supportedTasks.length === 0) {
        ElMessage.warning('所选任务均不支持生成病例')
        return
    }

    // 设置批量书写状态
    batchWritingStatus.value = {
        isProcessing: true,
        current: 0,
        total: supportedTasks.length,
        currentTaskId: null,
        batchTaskIds: supportedTasks.map(task => task.id), // 设置所有批量处理的任务ID
        completedTaskIds: [] // 重置已完成任务列表
    }

    ElMessage.info(`开始批量书写 ${supportedTasks.length} 个任务...`)

    let successCount = 0
    let failCount = 0
    const failedTasks = []

    // 循环调用单次书写
    for (let i = 0; i < supportedTasks.length; i++) {
        const row = supportedTasks[i]
        batchWritingStatus.value.current = i + 1
        batchWritingStatus.value.currentTaskId = row.id // 设置当前处理的任务ID

        try {
            await writeTask(row, true) // 传入 isBatch = true
            successCount++
            ElMessage.success(`${row.pat_name} 的任务书写成功 (${i + 1}/${supportedTasks.length})`)
        } catch (error) {
            failCount++
            failedTasks.push(row.pat_name)
            ElMessage.error(`${row.pat_name} 的任务书写失败 (${i + 1}/${supportedTasks.length})`)
        } finally {
            // 无论成功还是失败，都将任务标记为已完成，这样按钮就不再loading
            batchWritingStatus.value.completedTaskIds.push(row.id)
        }

        // 添加延迟避免请求过于频繁
        if (i < supportedTasks.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 1000))
        }
    }

    // 重置批量书写状态
    batchWritingStatus.value = {
        isProcessing: false,
        current: 0,
        total: 0,
        currentTaskId: null,
        batchTaskIds: [], // 清空批量任务ID列表
        completedTaskIds: [] // 清空已完成任务ID列表
    }

    // 显示最终结果
    if (failCount === 0) {
        ElMessage.success(`批量书写完成！全部 ${successCount} 个任务书写成功`)
    } else {
        ElMessage.warning(`批量书写完成！成功：${successCount}，失败：${failCount}${failedTasks.length > 0 ? '，失败任务：' + failedTasks.join('、') : ''}`)
    }

    // 清空选择
    tableRef.value.clearSelection()
    selectedRows.value = []
}
// 当前搜索医师姓名
const curUserName = ref('')
// 当前搜索医师账号
const curHisUser = ref('')
// 计算属性：根据 curUserName 过滤医生名称
const filteredTableData = computed(() => {
    return tableData.value
    //   if (!curUserName.value && !curHisUser.value && !curState.value && !curIsSys.value && !curUploadState.value) { 
    //   }

    //   if (curUserName.value || curHisUser.value || curState.value || curIsSys.value || curUploadState.value) {
    //     return tableData.value.filter(item =>
    //       item.pat_name && item.pat_name.includes(curUserName.value) && 
    //       item.doctor_name && item.doctor_name.includes(curHisUser.value) &&
    //       item.is_sys_display && item.is_sys_display.includes(curIsSys.value) &&
    //       item.status_display && item.status_display.includes(curUploadState.value) &&
    //       item.state && item.state.includes(curState.value)
    //     )
    //   }
})

const load = ref(false)
const tableData = ref([])

// 表格引用和多选相关
const tableRef = ref(null)
const selectedRows = ref([])

// 分页相关数据
const currentPage = ref(1)
const pageSize = ref(10)
const totalItems = ref(0)
// 状态
// 重置弹窗
const dialogVisible = ref(false)



const time1 = ref([]) // 时间区间

const getGridData = () => {
    load.value = true

    // 处理时间筛选
    let create_time__gte = ''
    let create_time__lte = ''
    if (Array.isArray(time1.value) && time1.value.length === 2) {
        create_time__gte = dayjs(time1.value[0]).format('YYYY-MM-DD HH:mm:ss')
        create_time__lte = dayjs(time1.value[1]).format('YYYY-MM-DD HH:mm:ss')
    }

    const params = {
        page: currentPage.value,
        page_size: pageSize.value,
        doctor_id: curHisUser.value, // 医生名称
        pat_name__icontains: curUserName.value,   // 患者名称
        state: curState.value,                    // 任务类型
        is_sys: curIsSys.value,                   // 生成方式
        upload_status_filter: curUploadState.value,      // 上传状态
        create_time__gte,                         // 创建时间起
        create_time__lte,                         // 创建时间止
    }
    // 过滤空参数
    const filteredParams = Object.entries(params).filter(([key, value]) => value !== '' && value !== undefined && value !== null);
    const queryString = new URLSearchParams(filteredParams).toString();

    fetch(api + `/v1/api/rpa_task_list/rpa_task?${queryString}`, {
        method: 'GET',
        headers: {
            'x-api-key': 'API655c18174462a06ede7ffbfa5aca4270c645b9b78ca30817e3a1bd496f34e04b',
        },
    })
        .then(response => response.json())
        .then(res => {
            tableData.value = res.data.records
            totalItems.value = res.data.page_info.total_count
            load.value = false;
        })
        .catch(error => {
            load.value = false;
            ElMessage.error('获取数据失败，请稍后重试')
        });
}

const getDoctorList = () => {
    fetch(api + `/v1/api/rpa_task_list/doctor_list`, {
        method: 'GET',
        headers: {
            'x-api-key': 'API655c18174462a06ede7ffbfa5aca4270c645b9b78ca30817e3a1bd496f34e04b',
        },
    })
        .then(response => response.json())
        .then(res => {
            console.log(res);
            doctorList.value = res.data.records
        })
        .catch(error => {
        });
}

// 获取任务类型列表
const getTaskTypeList = () => {
    fetch(api + `/v1/api/medical-record-status/list`, {
        method: 'GET',
        headers: {
            'x-api-key': 'API655c18174462a06ede7ffbfa5aca4270c645b9b78ca30817e3a1bd496f34e04b',
            'authorization': 'Bearer 6787c226c3eae7.874922141Z3hNBMuGI'
        },
    })
        .then(response => response.json())
        .then(res => {
            console.log('任务类型列表:', res);
            if (res.code === 0 && res.data && res.data.data) {
                // 将接口返回的数据转换为选项格式
                taskTypeList.value = Object.keys(res.data.data).map(key => ({
                    label: key,
                    value: key,
                    status: res.data.data[key] // 保存状态信息，用于后续判断是否支持生成
                }))
            }
        })
        .catch(error => {
            console.error('获取任务类型列表失败:', error);
            ElMessage.error('获取任务类型列表失败，请稍后重试')
        });
}
const getGridDataAgain = () => {
    currentPage.value = 1
    getGridData()
}



// 弹框内容数据
const dialogContent = ref({
    originalContent: null,    // 原始病例内容
    generatedContent: null,   // 新生成的病例内容
    apiContent: null,         // 接口返回的数据
    isLoading: false,
    currentRow: null
})

// 生成弹窗标题
const getDialogTitle = () => {
    if (dialogContent.value.currentRow) {
        const row = dialogContent.value.currentRow
        return `病例内容详情 (任务ID:${row.id}，患者名称:${row.pat_name})`
    }
    return '病例内容详情'
}

const lookContent = (row) => {

    dialogVisible.value = true
    // 设置当前行数据
    currentContent.value = row.document || '无内容'
}

// 处理页码变化
const handleCurrentChange = (val) => {
    currentPage.value = val
    // 页码变化时重新获取数据
    getGridData()
}

// 处理每页显示条数变化
const handleSizeChange = (val) => {
    pageSize.value = val
    // 重置为第一页
    currentPage.value = 1
    // 重新获取数据
    getGridData()
}

// 标签下载
const tagDownload = () => {
    let xlsxData = dealXlsxData(filteredTableData.value);

    // 创建工作簿
    const wb = XLSX.utils.book_new();
    // 将数据转换为工作表
    const ws = XLSX.utils.json_to_sheet(xlsxData);

    // 自动列宽
    let cols = getAutoWidth(xlsxData);
    ws['!cols'] = cols;

    // 将工作表添加到工作簿
    XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');
    // 导出 Excel 文件
    XLSX.writeFile(wb, '病例列表' + '.xlsx');
}

// 计算每列的最大宽度
function getAutoWidth(data) {
    const colWidth = [];
    if (data.length === 0) return [];
    const keys = Object.keys(data[0]);
    for (let i = 0; i < keys.length; i++) {
        let maxLen = keys[i].length;
        for (let j = 0; j < data.length; j++) {
            let val = data[j][keys[i]];
            if (val == null) val = '';
            val = String(val);
            // 中文字符宽度按2算
            const len = val.replace(/[^\x00-\xff]/g, '00').length;
            if (len > maxLen) maxLen = len;
        }
        colWidth.push({ wch: maxLen + 2 }); // +2 适当留白
    }
    return colWidth;
}
const dealXlsxData = (data) => {
    let newData = []
    data.forEach((item) => {
        newData.push({
            "患者名称": item.pat_name,
            "医生名称": item.doctor_name,
            "任务类型": item.state,
            "生成方式": item.is_sys_display== 0 ? '前端手动创建' : '系统自动创建',
            "病例内容": item.document,
            "病区": item.department,
            "生成时间": item.create_time,
            "上传状态": item.upload_status_filter_display,
        })
    })
    return newData
}


// 存储生成的内容
const generatedContent = ref({})

// 从 localStorage 加载内容
const loadFromStorage = () => {
  const stored = localStorage.getItem('generated_case_content')
  if (stored) {
    try {
      generatedContent.value = JSON.parse(stored)
    } catch (error) {
      console.error('解析存储内容失败:', error)
    }
  }
}

// 保存到 localStorage
const saveToStorage = () => {
  localStorage.setItem('generated_case_content', JSON.stringify(generatedContent.value))
}

// 检查任务类型是否支持生成病例
const checkTaskTypeSupported = (taskType) => {
  const taskTypeInfo = taskTypeList.value.find(item => item.value === taskType)
  return taskTypeInfo && taskTypeInfo.status === 'new'
}

// 书写方法
const writeTask = async (row, isBatch = false) => {
  try {
    // 检查任务类型是否支持生成
    if (!checkTaskTypeSupported(row.state)) {
      const message = '该类型暂时不支持生成病例'
      if (!isBatch) {
        ElMessage.warning(message)
      }
      throw new Error(message)
    }

    row.isWriting = true // 添加加载状态

    const response = await fetch(`${api}/v1/api/llm_sse/dispatch_task`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': 'API655c18174462a06ede7ffbfa5aca4270c645b9b78ca30817e3a1bd496f34e04b',
      },
      body: JSON.stringify({ task_id: row.id })
    })

    if (!response.ok) {
      throw new Error('网络请求失败')
    }

    const result = await response.json()
    console.log(123,result);
    // 存储生成的内容
    generatedContent.value[row.id] =Object.values(result?.data?.prompt)[0]
    saveToStorage()

    // 只在非批量模式下显示成功消息
    if (!isBatch) {
      ElMessage.success('病例生成成功')
    }
  } catch (error) {
    console.error('生成病例失败:', error)
    // 只在非批量模式下显示错误消息
    if (!isBatch) {
      ElMessage.error('生成病例失败')
    }
    // 重新抛出错误，让批量处理能够捕获
    throw error
  } finally {
    row.isWriting = false
  }
}

// 页面加载时从存储中恢复内容
onMounted(() => {
  loadFromStorage()
  getDoctorList()
  getTaskTypeList()
  getGridData()
})
onUnmounted(() => {
})
</script>
<style scoped>
.tableData {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    background-color: #ffffff;
    padding: 10px;
    box-sizing: border-box;
}

h3 {
    height: 40px;
}

.topMenu {
    display: flex;
    justify-content: flex-start;
    margin-bottom: 10px;
    flex-wrap: wrap;
    gap: 5px;
}

.menuBox {
    display: flex;
    justify-content: flex-start;
    margin-bottom: 10px;
    flex-wrap: wrap;
    gap: 5px;
}

.table {
    flex: 1;
    overflow-y: auto;
}

.tag_list {
    padding: 24px;
}

.tag_lists {
    height: 600px;
    overflow-y: scroll;
}

.menu {
    width: 200px;
}

.pass_select {
    display: flex;
    justify-content: center;
    align-items: center;
}

.selection-info {
    padding: 8px 12px;
    background-color: #f0f9ff;
    border: 1px solid #b3d8ff;
    border-radius: 4px;
    margin-bottom: 10px;
    font-size: 14px;
    color: #409eff;
}

/* 弹框内容样式 */
.content-container {
    display: flex;
    gap: 16px;
    height: 650px;
}

.content-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    border: 1px solid #e4e7ed;
    border-radius: 6px;
    overflow: hidden;
}

.section-header {
    background-color: #f5f7fa;
    padding: 12px 16px;
    border-bottom: 1px solid #e4e7ed;
}

.section-header h4 {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    color: #303133;
}

.section-scrollbar {
    flex: 1;
}

.section-content {
    padding: 16px;
    min-height: 100%;
}
</style>

