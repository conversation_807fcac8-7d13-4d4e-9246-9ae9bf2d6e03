<template>
  <div class="input" v-loading="load">
    <div class="input-list">

      <div class="list">
        <div class="list-item" v-for="(item,index) in listings" :key="index">
          <h4>{{item.document_type}}</h4>
          <div class="text">{{item.document_content}}</div>
        </div>
      </div>
    </div>
    <div class="tableData">
      <div class="input-s">
        <div class="name-label">病区</div>
        <el-select v-model="ward_id" @change="getUser" class="m-2" placeholder="病区筛选" size="large">
          <el-option
              v-for="item in select"
              :key="item.id"
              :label="item.item_name"
              :value="item.id"
          />
        </el-select>
      </div>
      <el-table :data="user_list" style="width: 100%;height: 800px;">
        <el-table-column label="用户列表" width="200px">
          <template #default="scope">
            <el-button :type="index==scope.row.pid?'primary':''" size="small" @click="handleClick(scope.row)">{{scope.row.pat_name}}</el-button>
            <div>
              {{scope.row.disease}}
            </div>
          </template>
        </el-table-column>
        <el-table-column>
          <template #header>
            <el-input v-model="search" placeholder="输入名称" />
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>

  <div class="tab_list" v-if='select_row.pvid'>
    <el-pagination background :current-page="tab_type" layout="prev, pager, next" @current-change="tabClick" :page-size="1" :total="parseInt(select_row.pvid)" />
  </div>
  <div>
    <el-button @click="getDoctorAdvice">查询医嘱内容</el-button>
  </div>


  <el-dialog
      v-model="show_see"
      title="医嘱内容"
      width="80%"
  >
    <div style="width: 100%;height:400px;">
      <el-table :data="see_list" style="width: 100%;height: 400px">
        <el-table-column prop="order_content" label="内容" />
        <el-table-column prop="order_exe_time_start" label="开始时间"/>
        <el-table-column prop="order_exe_time_end" label="结束时间" />
      </el-table>
    </div>
  </el-dialog>

</template>

<script setup>
import { ref,computed } from 'vue';
import { post } from '@/utils/request.js'
import c from "@/utils/config";
import { message } from '@/utils/message'

const show_see = ref(false)
const see_list = ref([])
const type_list=ref([
  "入院记录(合作)",
  "入院记录(不合作)",
  "首次病程记录",
  "上级医师查房记录",
  "日常病程记录",
  "阶段小结",
  "交班记录",
  "接班记录",
  "出院记录",
])

const count = ref(0);
const aip_url = c.aip_url;
const load = ref(false)
const select = ref([])
const ward_id = ref("22")

const search = ref("")
const tableData = ref([])
const listings = ref([])
const pid = ref()

const select_row = ref({})
const tab_type = ref("")
const doctorAdvice = ref([])

// 获取医嘱
const getDoctorAdvice = ()=>{
  show_see.value = true;
  post(`${aip_url}/patient/getYz`, JSON.stringify({pid:select_row.value.pid})).then(res => {
    doctorAdvice.value = res
    see_list.value = doctorAdvice.value.map((item)=>{
      let {
        order_content,
        order_drask,
        order_once_qunt,
        order_total_qunt,
        order_once_qunt_unit,
        order_exe_time_start,
        order_exe_time_end,
      } = item
      return {
        order_content,
        order_drask,
        order_once_qunt,
        order_total_qunt,
        order_once_qunt_unit,
        order_exe_time_start,
        order_exe_time_end
      }
    })
    show_see.value = true;
    load.value = false
  }).catch(error => {
    load.value = false
    show_see.value = false;
    console.error('发生错误:', error);
  });
}

const user_list = computed(() =>
    tableData.value.filter((data_list) =>
        !search.value ||
        data.pat_name.toLowerCase().includes(search.value.toLowerCase())
    )
)

const tabClick = (index)=>{
  tab_type.value = index
  getPatientCase(select_row.value.pid,index)
}

const doubleCount = computed(() => {
  return count.value * 2;
});

const increment = () => {
  count.value++;
};

const autoResize = ()=>{
  let textarea = document.querySelectorAll("textarea")
  textarea.forEach((obj)=>{
    obj.style.height = (obj.scrollHeight + 20) + 'px';
  });
}

const getUser = ()=>{
  console.log(ward_id.value)
  let url = `${aip_url}/patient/getAbnormalList`
  post(url, JSON.stringify({ward_id:ward_id.value})).then(res => {
    tableData.value = res.data
  }).catch(error => {
    console.error('发生错误:', error);
  });
}

const getWardList = ()=>{
  let url = `${aip_url}/AiCase/getWardList`
  post(url, JSON.stringify({})).then(res => {
    select.value = res.data
    getUser()
  }).catch(error => {
    console.error('发生错误:', error);
  });
}

getWardList()

const handleClick = (row)=>{
  tab_type.value =parseInt(row.pvid)
  pid.value =  row.pid
  select_row.value = row;
  getPatientCase(row.pid,row.pvid)
}

const getPatientCase = (pid,pvid)=>{
  load.value = true
  let url = `${aip_url}/AiCase/getPatientCase`
  post(url, JSON.stringify({pid,pvid})).then(res => {
    listings.value = res.data;
    setTimeout(()=>{
      load.value = false
      autoResize()
    },1000)
  }).catch(error => {
    load.value = false
    console.error('发生错误:', error);
  });
}
</script>

<style scoped>
.input{
  height:calc(100% - 100px);
  display: flex;
  padding: 15px;
}
.tableData{
  width: 20%;
  border-left: 1px solid dodgerblue;
  padding-left: 10px;
}
.input-list{
  width: 80%;
}
.el-textarea__inner{
  height: 100%;
}
.showtext{
  border: 1px solid #000000;
}
.input-s{
  margin-bottom: 5px;
  display: flex;
  align-items: center;
}
.tab_list{
  /* height: 40px; */
  padding-top: 15px;
}
.name-label{
  width: 50px;
}
.list{
  height: 100%;
  overflow-y: auto;
}
.list-item{
  margin-bottom: 15px;

}
h4{
  margin-bottom:10px;
}
.text {
  white-space: pre-line; /* 或者使用 pre-wrap */
  border: 1px solid #eee;
  font-size: 14px;
  padding: 15px;
}
.tab_list{
  position: absolute;
  left: 30%;
  bottom: 40px;
}
</style>