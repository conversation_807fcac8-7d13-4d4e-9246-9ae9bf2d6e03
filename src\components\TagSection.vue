<template>
    <div class="tag-section-header">
        <h3>{{ title }}</h3>
        <div class="section-tools">
            <el-input v-if="showSearch" :placeholder="searchPlaceholder" v-model="searchValue" clearable 
                style="width: 200px;min-width: 100px;" @input="onSearchInput"></el-input>
            <div class="search-actions">
                <slot name="searchs"></slot>
            </div>
            <div class="header-actions">
                <slot name="actions"></slot>
            </div>
        </div>
    </div>
</template>

<script setup>
const props = defineProps({
    title: String,
    showSearch: { type: Boolean, default: true },
    searchPlaceholder: String,
    searchValue: String,
})
const emit = defineEmits(['update:searchValue', 'searchInput'])

function onSearchInput(val) {
    emit('update:searchValue', val)
    emit('searchInput', val)
}
</script>

<style scoped>
.tag-section-header {
    display: flex;
    margin-bottom: 15px;
    flex-direction: column;
}

.tag-section-header h3 {
    margin-bottom: 8px;
    font-weight: bold;
}

.section-tools {
    display: flex;
    gap: 10px;
    margin-top: 4px;
    justify-content: space-between;
}

.search-actions{
    display: flex;
    gap: 10px;
}
.header-actions {
    display: flex;
}
</style>