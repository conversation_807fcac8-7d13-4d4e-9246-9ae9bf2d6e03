<template>
    <div class="tableData">
        <h3>{{ route.meta.title }}</h3>
        <div class="topMenu">
            <div class="menuBox">
                <el-select v-model="selectedDepartment" placeholder="筛选科室" @change="getTagPage1"
                    style="width: 200px;padding-left: 10px;"  clearable>
                    <el-option :label="item.label" :value="item.value" v-for="item in department_list" :key="item.id" />
                </el-select>
                <el-select v-model="selectedStatus" placeholder="筛选状态" @change="getTagPage1"
                    style="width: 200px;padding-left: 10px;"  clearable>
                    <el-option label="未使用" value="0"/>
                    <el-option label="已审核" value="1"/>
                    <el-option label="未审核" value="2"/>
                </el-select>
                <el-input class="search-doctor" placeholder="搜索医生名称" v-model="curUserName"  clearable></el-input>
                <el-input class="search-doctor" placeholder="搜索账号" v-model="curHisUser"  clearable></el-input>
            </div>
        </div>
        <el-table  class="table" :data="filteredTableData" v-loading="load">
            <el-table-column type="index" label="生成" width="100"></el-table-column>
            <el-table-column label="医生名称" prop="username"></el-table-column>
            <el-table-column label="账号" prop="his_user"></el-table-column>
            <el-table-column label="密码" prop="his_pwd"></el-table-column>
            <el-table-column label="审核状态" prop="status">
                <template #default="{ row }">
                    <el-tag v-if="row.status === 0" type="info">未使用</el-tag>
                    <el-tag v-if="row.status === 2" type="danger">未审核</el-tag>
                    <el-tag v-if="row.status === 1" type="success">已审核</el-tag>
                </template>
            </el-table-column>
            <el-table-column label="关联时间" prop="update_time"></el-table-column>
            <el-table-column label="科室名称" prop="dept_name"></el-table-column>
            <el-table-column label="操作">
                <template #default="{ row }">
                    <el-button type="text" @click="resetData(row)">重置</el-button>
                    <el-button type="text" @click="updateStatus(row)">更新状态</el-button>
                    <el-button type="text" @click="updatePassword(row)">修改密码</el-button>
                </template>
            </el-table-column>
        </el-table>
        <Pagination style="margin-top: 10px; display: flex; justify-content: flex-end;" :currentPage="currentPage"
            :pageSize="pageSize" :total="totalItems" @current-change="handleCurrentChange"
            @size-change="handleSizeChange" />
    </div>


    <el-dialog
        v-model="resetDialogVisible"
        title="更新状态"
        width="25%"
        draggable
    >
        <template #default>
            <div class="content" style="text-align: center;height: 100px;">
                <p>是否重置医生账号？</p>
            </div>
        </template>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="resetDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="resetDataConfirm">确认</el-button>
            </span>
        </template>
    </el-dialog>
    <el-dialog
        v-model="statusDialogVisible"
        title="更新状态"
        width="25%"
        draggable
    >
        <template #default>
            <div class="content" style="text-align: center;margin-bottom: 30px; height: 30px;">
                <p>是否通过审核？</p>
            </div>
            <!-- <div class="pass_select">
                <el-button
                    :type="auditStatus === 1 ? 'success' : 'default'"
                    @click="auditStatus = 1"
                    style="margin-right: 10px;"
                >通过</el-button>
                <el-button
                    :type="auditStatus === 2 ? 'danger' : 'default'"
                    @click="auditStatus = 2"
                >不通过</el-button>
            </div> -->
        </template>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="statusDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="updateStatusConfirm">确认</el-button>
            </span>
        </template>
    </el-dialog>
    <el-dialog
        v-model="passwordDialogVisible"
        title="修改密码"
        width="25%"
        draggable
    >
        <template #default>
            <div class="content" style="text-align: center; height: 200px;">
                <p style="margin-bottom: 10px;">当前密码：{{ currentRow.his_pwd }}</p>
                <el-input v-model="password" type="password" placeholder="请输入密码" style="margin-bottom: 20px;"></el-input>
            </div>
        </template>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="passwordDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="updatePasswordConfirm">确定</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { ElMessage } from 'element-plus';
import { post } from '@/utils/request.js'
import c from "@/utils/config";
import { message } from '@/utils/message'
import { json } from 'd3';
import Pagination from '@/components/Pagination.vue';
import { useRoute } from 'vue-router';

const route = useRoute();

const load = ref(false)
const tableData = ref([])

const tableHeight = ref(0)
// 分页相关数据
const currentPage = ref(1)
const pageSize = ref(10)
const totalItems = ref(0)
// 科室
const selectedDepartment = ref('')
const department_list = ref([])

// 状态
const selectedStatus = ref('')
const status_list = ref([])

// 重置弹窗
const resetDialogVisible = ref(false)
// 状态弹窗
const statusDialogVisible = ref(false)
// 默认审核通过
const auditStatus = ref(1) 

// 密码弹窗
const passwordDialogVisible = ref(false)
const password = ref('')

// 当前搜索医师姓名
const curUserName = ref('')
// 当前搜索医师账号
const curHisUser = ref('')
// 计算属性：根据 curUserName 过滤医生名称
const filteredTableData = computed(() => {
  if (!curUserName.value && !curHisUser.value) {
    return tableData.value
  }
  if (curUserName.value || curHisUser.value) {
    return tableData.value.filter(item =>
      item.username && item.username.includes(curUserName.value) && 
      item.his_user && item.his_user.includes(curHisUser.value)
    )
  }
})


const api_url = c.api_url;


// 计算表格高度的函数
const calculateTableHeight = () => {
    // 获取视窗高度
    const windowHeight = window.innerHeight
    // 预留其他元素的空间（如页头、页尾等），这里预留 100px
    tableHeight.value = windowHeight - 150
}


const getTag = () => {
    const params = {
        page: currentPage.value,
        limit: pageSize.value
    }
    if (selectedDepartment.value) {
        params.dept_id = selectedDepartment.value
    }
    if (typeof selectedStatus.value !== 'undefined' && selectedStatus.value !== null) {
        params.status = selectedStatus.value
    }

    load.value = true;
         post(api_url + "/doctor/getDoctorCheckList", JSON.stringify(params)).then(res => {
        tableData.value = res.data.length ? res.data : res.data.data
        totalItems.value = res.total || res.data.total  || (res.length || 0)
        load.value = false;
    }).catch(error => {
        load.value = false;
        console.error('获取数据失败', error)
    });
}
const getTagPage1 = () => {
    currentPage.value = 1
    getTag()
}


// 处理页码变化
const handleCurrentChange = (val) => {
    currentPage.value = val
    // 页码变化时重新获取数据
    getTag()
}

// 处理每页显示条数变化
const handleSizeChange = (val) => {
    pageSize.value = val
    // 重置为第一页
    currentPage.value = 1
    // 重新获取数据
    getTag()
}

// 获取状态和科室
const getOption = () => {
    let url = `${api_url}/doctor/getOption`
    load.value = true
         post(url, JSON.stringify({})).then(res => {
        department_list.value = res.data.department
        status_list.value = res.data.status
        load.value = false
    }).catch(error => {
        load.value = false
        console.error('发生错误:', error);
    });
}

const resetDataConfirm = () => {
    if (!currentRow.value) return    
         post(api_url + "/doctor/resetAccount", JSON.stringify({
         id: currentRow.value.id
     })).then(res => {
        if (res.error_code === 0) {
            ElMessage.success('状态更新成功')
            resetDialogVisible.value = false
            getTag() 
        } else {
            ElMessage.error(res.message || '状态更新失败')
        }
    }).catch(error => {
        ElMessage.error('状态更新失败')
        console.error('发生错误:', error)
    })
}
const updateStatusConfirm = () => {
    if (!currentRow.value) return    
         post(api_url + "/doctor/updateStatus", JSON.stringify({
         id: currentRow.value.id
     })).then(res => {
        if (res.error_code === 0) {
            ElMessage.success('状态更新成功')
            statusDialogVisible.value = false
            getTag() 
        } else {
            ElMessage.error(res.message || '状态更新失败')
        }
    }).catch(error => {
        ElMessage.error('状态更新失败')
        console.error('发生错误:', error)
    })
}

const updatePasswordConfirm = () => {
    if (!currentRow.value || !password.value) {
        ElMessage.warning('请输入密码')
        return
    }
         post(api_url + "/doctor/updateDoctor", JSON.stringify({
         id: currentRow.value.id,
         his_pwd: password.value
     })).then(res => {
        if (res.error_code === 0) {
            ElMessage.success('密码修改成功')
            passwordDialogVisible.value = false
            password.value = '' // 清空密码输入
            getTag() 

        } else {
            ElMessage.error(res.message || '密码修改失败')
        }
    }).catch(error => {
        ElMessage.error('密码修改失败')
        console.error('发生错误:', error)
    })
}

// 更新状态
const currentRow = ref(null)

const updateStatus = (row) => {
    currentRow.value = row
    statusDialogVisible.value = true
    // 默认选中当前行的状态，如果没有则默认1
    auditStatus.value = row.status === 2 ? 2 : 1
}

// 修改密码
const updatePassword = (row) => {
    currentRow.value = row
    password.value = '' // 清空之前的输入
    passwordDialogVisible.value = true
}
// 重置
const resetData = (row) => {
    currentRow.value = row
    resetDialogVisible.value = true
}
onMounted(() => {
    getTag()
    getOption()
    calculateTableHeight()
    window.addEventListener('resize', calculateTableHeight)
})
onUnmounted(() => {
    window.removeEventListener('resize', calculateTableHeight)
})
</script>
<style scoped>
.tableData {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    background-color: #ffffff;
    padding: 10px;
    box-sizing: border-box;
}
h3 {
    height: 40px;
}
.topMenu {
    display: flex;
    justify-content: flex-start;
    margin-bottom: 10px;
    flex-wrap: wrap;
    gap: 5px;
}

.menuBox {
    display: flex;
    justify-content: flex-start;
    margin-bottom: 10px;
    flex-wrap: wrap;
    gap: 5px;
}

.table {
    flex: 1;
    overflow-y: auto;
}
.tag_list {
    padding: 24px;
}

.tag_lists {
    height: 600px;
    overflow-y: scroll;
}
.search-doctor{
    width: 200px;
    margin-left: 10px;
}
.pass_select {
    display: flex;
    justify-content: center;
    align-items: center;
}
</style>