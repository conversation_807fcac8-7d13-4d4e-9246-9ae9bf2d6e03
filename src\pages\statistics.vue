<template>
<div class="statistics-container">

    <el-row :gutter="20">
        <el-col :span="24">
            <el-card class="chart-card">
                <div ref="dailyChart" class="chart"></div>
            </el-card>
        </el-col>
    </el-row>
    <el-row :gutter="20" class="mt-20">
        <el-col :span="12">
            <el-card class="chart-card">
                <div ref="weeklyChart" class="chart"></div>
            </el-card>
        </el-col>
        <el-col :span="12">
            <el-card class="chart-card">
                <div ref="monthlyChart" class="chart"></div>
            </el-card>
        </el-col>
    </el-row>
    <el-row :gutter="20" class="mt-20">
        <el-col :span="24">
            <el-card class="chart-card">
                <el-form :inline="true">
                    <el-form-item label="时间范围">
                        <el-date-picker
                            v-model="dateRange"
                            type="daterange"
                            range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            format="YYYY-MM-DD"
                            value-format="YYYY-MM-DD"
                            @change="handleDateChange"
                        />
                    </el-form-item>
                </el-form>
                <div ref="doctorChart" class="chart"></div>
            </el-card>
        </el-col>
    </el-row>
    <el-row :gutter="20" class="mt-20">
        <el-col :span="24">
            <el-card class="chart-card">
                <el-form :inline="true">
                    <el-form-item label="时间范围">
                        <el-date-picker
                            v-model="newDateRange"
                            type="daterange"
                            range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            format="YYYY-MM-DD"
                            value-format="YYYY-MM-DD"
                            @change="handleNewDateChange"
                        />
                    </el-form-item>
                    <el-form-item>
                        <el-radio-group v-model="dataSource" @change="switchDataSource">
                            <el-radio-button label="today">今日</el-radio-button>
                            <el-radio-button label="other">遗漏</el-radio-button>
                        </el-radio-group>
                    </el-form-item>
                </el-form>
                <div ref="newTaskChart" class="chart"></div>
            </el-card>
        </el-col>
    </el-row>
    <el-row :gutter="20">
        <el-col :span="24">
            <el-card class="chart-card">
                <el-form :inline="true">
                    <el-form-item label="选择日期">
                        <el-date-picker
                            v-model="doctorStateDate"
                            type="date"
                            placeholder="选择日期"
                            format="YYYY-MM-DD"
                            value-format="YYYY-MM-DD"
                            @change="handleDoctorStateDateChange"
                        />
                    </el-form-item>
                </el-form>
                <div ref="doctorStateChart" class="chart"></div>
            </el-card>
        </el-col>
    </el-row>
</div>
</template>

<script setup>
import * as echarts from 'echarts'
import { ref, onMounted } from 'vue'
import { post } from '@/utils/request.js'
import c from "@/utils/config";

const api_url = c.api_url;

// 定义统一的颜色方案
const chartColors = [
    '#5470c6', // 系统任务
    '#91cc75', // 手动任务
    '#fac858', // 自动书写
    '#ee6666', // 手动书写
    '#73c0de', // 自动上传
    '#3ba272', // 手动上传
    '#fc8452', // 待处理
    '#9a60b4', // 运行中
    '#ea7ccc', // 已完成
    '#48b'    // 总数
];

const dailyChart = ref(null)
const weeklyChart = ref(null)
const monthlyChart = ref(null)
const doctorChart = ref(null)
const doctorStateChart = ref(null)
const newTaskChart = ref(null)
let dailyChartInstance = null
let weeklyChartInstance = null
let monthlyChartInstance = null
let doctorChartInstance = null
let doctorStateChartInstance = null
let newTaskChartInstance = null
let dateRange = ref([])
let newDateRange = ref([])
const doctorStateDate = ref(getFormattedDate())
const dataSource = ref('today')
const originalData = ref(null);
const filteredData = ref(null);
const todayData = ref(null);
const otherData = ref(null);
const currentTaskData = ref(null);

function getFormattedDate() {
    const date = new Date();
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
}

const getTaskData = ()=>{
  post(`${api_url}/total/taskData`, JSON.stringify({})).then(res => {
    originalData.value = res.data
    applyFilters();
  }).catch(error => {
    console.error('发生错误:', error);
  });
}

const getTodayTaskData = ()=>{
  post(`${api_url}/total/todayTaskData`, JSON.stringify({})).then(res => {
    todayData.value = res.data
    if (dataSource.value === 'today') {
      currentTaskData.value = res.data;
      applyNewFilters();
    }
  }).catch(error => {
    console.error('发生错误:', error);
  });
}

const getDocumentDataTogether = ()=>{
  post(`${api_url}/total/todayTaskData`, JSON.stringify({})).then(res => {
    initDoctorStateChart(res.data)
  }).catch(error => {
    console.error('发生错误:', error);
  });
}

const getOtherTaskData = ()=>{
  post(`${api_url}/total/otherTaskData`, JSON.stringify({})).then(res => {
    otherData.value = res.data
    if (dataSource.value === 'other') {
      currentTaskData.value = res.data;
      applyNewFilters();
    }
  }).catch(error => {
    console.error('发生错误:', error);
  });
}

const applyFilters = () => {
    if (!originalData.value) {
        return;
    }
    
    // 深拷贝原始数据
    let result;
    let doctorArray;
    
    // 检查数据结构，确定是否需要处理
    if (Array.isArray(originalData.value)) {
        result = JSON.parse(JSON.stringify(originalData.value));
        doctorArray = result;
    } else if (originalData.value.doctor && Array.isArray(originalData.value.doctor)) {
        result = JSON.parse(JSON.stringify(originalData.value));
        doctorArray = result.doctor;
    } else {
        return;
    }
    
    // 如果没有设置日期范围，使用原始数据
    if (!dateRange.value || dateRange.value.length !== 2 || !dateRange.value[0] || !dateRange.value[1]) {
        filteredData.value = result;
        initCharts(filteredData.value);
        return;
    }
    
    const startDate = new Date(dateRange.value[0]);
    const endDate = new Date(dateRange.value[1]);
    
    // 创建一个新的医生数组，用于存储筛选后的结果
    const filteredDoctorArray = doctorArray.map(doctor => {
        // 创建医生数据的深拷贝
        const doctorCopy = JSON.parse(JSON.stringify(doctor));
        
        if (doctorCopy.dates && Array.isArray(doctorCopy.dates) && doctorCopy.data && Array.isArray(doctorCopy.data)) {
            const filteredIndices = [];
            
            doctorCopy.dates.forEach((dateStr, index) => {
                const date = new Date(dateStr);
                if (date >= startDate && date <= endDate) {
                    filteredIndices.push(index);
                }
            });
            
            // 更新日期和数据数组
            doctorCopy.dates = filteredIndices.map(i => doctorCopy.dates[i]);
            doctorCopy.data = filteredIndices.map(i => doctorCopy.data[i]);
        }
        
        return doctorCopy;
    });
    
    // 更新结果
    if (Array.isArray(result)) {
        // 如果原始数据是数组，直接替换
        result = filteredDoctorArray;
    } else {
        // 如果原始数据是对象，更新doctor字段
        result.doctor = filteredDoctorArray;
    }
    
    filteredData.value = result;
    initCharts(filteredData.value);
}

const applyNewFilters = () => {
    if (!currentTaskData.value) {
        return;
    }
    
    // 深拷贝原始数据
    let result;
    let doctorArray;
    
    // 检查数据结构，确定是否需要处理
    if (Array.isArray(currentTaskData.value)) {
        result = JSON.parse(JSON.stringify(currentTaskData.value));
        doctorArray = result;
    } else if (currentTaskData.value.doctor && Array.isArray(currentTaskData.value.doctor)) {
        result = JSON.parse(JSON.stringify(currentTaskData.value));
        doctorArray = result.doctor;
    } else {
        return;
    }
    
    // 如果没有设置日期范围，使用原始数据
    if (!newDateRange.value || newDateRange.value.length !== 2 || !newDateRange.value[0] || !newDateRange.value[1]) {
        initNewTaskChart(currentTaskData.value);
        return;
    }
    
    const startDate = new Date(newDateRange.value[0]);
    const endDate = new Date(newDateRange.value[1]);
    
    // 创建一个新的医生数组，用于存储筛选后的结果
    const filteredDoctorArray = doctorArray.map(doctor => {
        // 创建医生数据的深拷贝
        const doctorCopy = JSON.parse(JSON.stringify(doctor));
        
        if (doctorCopy.dates && Array.isArray(doctorCopy.dates) && doctorCopy.data && Array.isArray(doctorCopy.data)) {
            const filteredIndices = [];
            
            doctorCopy.dates.forEach((dateStr, index) => {
                const date = new Date(dateStr);
                if (date >= startDate && date <= endDate) {
                    filteredIndices.push(index);
                }
            });
            
            // 更新日期和数据数组
            doctorCopy.dates = filteredIndices.map(i => doctorCopy.dates[i]);
            doctorCopy.data = filteredIndices.map(i => doctorCopy.data[i]);
        }
        
        return doctorCopy;
    });
    
    // 更新结果
    if (Array.isArray(result)) {
        // 如果原始数据是数组，直接替换
        result = filteredDoctorArray;
    } else {
        // 如果原始数据是对象，更新doctor字段
        result.doctor = filteredDoctorArray;
    }
    
    initNewTaskChart(result);
}

const handleDateChange = () => {
    applyFilters();
}

const handleNewDateChange = () => {
    applyNewFilters();
}

const switchDataSource = () => {
    currentTaskData.value = dataSource.value === 'today' ? todayData.value : otherData.value;
    applyNewFilters();
}


const initCharts = (data) => {
    dailyChartInstance = echarts.init(dailyChart.value)
    dailyChartInstance.setOption({
        color: chartColors,
        title: {
            text: '每日任务统计',
            left: 'center'
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            },
            formatter: function(params) {
                let result = params[0].name + '<br/>';
                params.forEach(param => {
                    if (param.value !== 0) {
                        result += param.marker + ' ' + param.seriesName + ': ' + param.value + '<br/>';
                    }
                });
                return result;
            }
        },
        legend: {
            data: ['系统任务', '手动任务', '自动书写', '手动书写', '自动上传', '手动上传', '待处理', '运行中', '已完成', '总数'],
            top: 30
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            data: data.daily.dates,
            axisLabel: {
                rotate: 45
            }
        },
        yAxis: {
            type: 'value'
        },
        series: [
            {
                name: '系统任务',
                type: 'line',
                data: data.daily.data.map(item => item.sys_count)
            },
            {
                name: '手动任务',
                type: 'line',
                data: data.daily.data.map(item => item.manual_count)
            },
            {
                name: '自动书写',
                type: 'line',
                data: data.daily.data.map(item => item.auto_write_count)
            },
            {
                name: '手动书写',
                type: 'line',
                data: data.daily.data.map(item => item.manual_write_count)
            },
            {
                name: '自动上传',
                type: 'line',
                data: data.daily.data.map(item => item.auto_upload_count)
            },
            {
                name: '手动上传',
                type: 'line',
                data: data.daily.data.map(item => item.manual_upload_count)
            },
            {
                name: '待处理',
                type: 'line',
                data: data.daily.data.map(item => item.pending_count)
            },
            {
                name: '运行中',
                type: 'line',
                data: data.daily.data.map(item => item.running_count)
            },
            {
                name: '已完成',
                type: 'line',
                data: data.daily.data.map(item => item.completed_count)
            },
            {
                name: '总数',
                type: 'line',
                data: data.daily.data.map(item => item.total_count)
            }
        ]
    })

    weeklyChartInstance = echarts.init(weeklyChart.value)
    weeklyChartInstance.setOption({
        color: chartColors,
        title: {
            text: '每周任务统计',
            left: 'center'
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            },
            formatter: function(params) {
                let result = params[0].name + '<br/>';
                params.forEach(param => {
                    if (param.value !== 0) {
                        result += param.marker + ' ' + param.seriesName + ': ' + param.value + '<br/>';
                    }
                });
                return result;
            }
        },
        legend: {
            data: ['系统任务', '手动任务', '自动书写', '手动书写', '自动上传', '手动上传', '待处理', '运行中', '已完成', '总数'],
            top: 30
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            data: data.weekly.weeks.map(week => `第${week}周`)
        },
        yAxis: {
            type: 'value'
        },
        series: [
            {
                name: '系统任务',
                type: 'bar',
                data: data.weekly.data.map(item => item.sys_count)
            },
            {
                name: '手动任务',
                type: 'bar',
                data: data.weekly.data.map(item => item.manual_count)
            },
            {
                name: '自动书写',
                type: 'bar',
                data: data.weekly.data.map(item => item.auto_write_count)
            },
            {
                name: '手动书写',
                type: 'bar',
                data: data.weekly.data.map(item => item.manual_write_count)
            },
            {
                name: '自动上传',
                type: 'bar',
                data: data.weekly.data.map(item => item.auto_upload_count)
            },
            {
                name: '手动上传',
                type: 'bar',
                data: data.weekly.data.map(item => item.manual_upload_count)
            },
            {
                name: '待处理',
                type: 'bar',
                data: data.weekly.data.map(item => item.pending_count)
            },
            {
                name: '运行中',
                type: 'bar',
                data: data.weekly.data.map(item => item.running_count)
            },
            {
                name: '已完成',
                type: 'bar',
                data: data.weekly.data.map(item => item.completed_count)
            },
            {
                name: '总数',
                type: 'bar',
                data: data.weekly.data.map(item => item.total_count)
            }
        ]
    })

    monthlyChartInstance = echarts.init(monthlyChart.value)
    monthlyChartInstance.setOption({
        color: chartColors,
        title: {
            text: '每月任务统计',
            left: 'center'
        },
        tooltip: {
            trigger: 'item',
            formatter: function(param) {
                if (param.value === 0) {
                    return null;
                }
                return param.name + ': ' + param.value + ' (' + param.percent + '%)';
            }
        },
        legend: {
            orient: 'vertical',
            left: 'left'
        },
        series: [
            {
                name: '任务统计',
                type: 'pie',
                radius: '50%',
                data: [
                    { value: data.monthly.data[0].sys_count, name: '系统任务' },
                    { value: data.monthly.data[0].manual_count, name: '手动任务' },
                    { value: data.monthly.data[0].auto_write_count, name: '自动书写' },
                    { value: data.monthly.data[0].manual_write_count, name: '手动书写' },
                    { value: data.monthly.data[0].auto_upload_count, name: '自动上传' },
                    { value: data.monthly.data[0].manual_upload_count, name: '手动上传' },
                    { value: data.monthly.data[0].pending_count, name: '待处理' },
                    { value: data.monthly.data[0].running_count, name: '运行中' },
                    { value: data.monthly.data[0].completed_count, name: '已完成' }
                ],
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                }
            }
        ]
    })

    doctorChartInstance = echarts.init(doctorChart.value)
    
    const doctorData = data.doctor || data; // 兼容两种数据结构
    
    const doctorNames = doctorData.map(item => item.doctor_name);
    
    const doctorStats = doctorData.map(doctor => {
        if (!doctor.data || doctor.data.length === 0) {
            return {
                sys_count: 0,
                manual_count: 0,
                auto_write_count: 0,
                manual_write_count: 0,
                auto_upload_count: 0,
                manual_upload_count: 0,
                pending_count: 0,
                running_count: 0,
                completed_count: 0,
                total_count: 0
            };
        }
        
        const result = doctor.data.reduce((acc, curr) => {
            return {
                sys_count: acc.sys_count + (curr.sys_count || 0),
                manual_count: acc.manual_count + (curr.manual_count || 0),
                auto_write_count: acc.auto_write_count + (curr.auto_write_count || 0),
                manual_write_count: acc.manual_write_count + (curr.manual_write_count || 0),
                auto_upload_count: acc.auto_upload_count + (curr.auto_upload_count || 0),
                manual_upload_count: acc.manual_upload_count + (curr.manual_upload_count || 0),
                pending_count: acc.pending_count + (curr.pending_count || 0),
                running_count: acc.running_count + (curr.running_count || 0),
                completed_count: acc.completed_count + (curr.completed_count || 0),
                total_count: acc.total_count + (curr.total_count || 0)
            };
        }, {
            sys_count: 0,
            manual_count: 0,
            auto_write_count: 0,
            manual_write_count: 0,
            auto_upload_count: 0,
            manual_upload_count: 0,
            pending_count: 0,
            running_count: 0,
            completed_count: 0,
            total_count: 0
        });
        
        return result;
    });
    
    doctorChartInstance.setOption({
        color: chartColors,
        title: {
            text: '医生任务统计',
            left: 'center'
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            },
            formatter: function(params) {
                let result = params[0].name + '<br/>';
                params.forEach(param => {
                    if (param.value !== 0) {
                        result += param.marker + ' ' + param.seriesName + ': ' + param.value + '<br/>';
                    }
                });
                return result;
            }
        },
        legend: {
            data: ['系统任务', '手动任务', '自动书写', '手动书写', '自动上传', '手动上传', '待处理', '运行中', '已完成', '总数'],
            top: 30
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            data: doctorNames,
            axisLabel: {
                rotate: 45
            }
        },
        yAxis: {
            type: 'value'
        },
        series: [
            {
                name: '系统任务',
                type: 'bar',
                data: doctorStats.map(item => item.sys_count)
            },
            {
                name: '手动任务',
                type: 'bar',
                data: doctorStats.map(item => item.manual_count)
            },
            {
                name: '自动书写',
                type: 'bar',
                data: doctorStats.map(item => item.auto_write_count)
            },
            {
                name: '手动书写',
                type: 'bar',
                data: doctorStats.map(item => item.manual_write_count)
            },
            {
                name: '自动上传',
                type: 'bar',
                data: doctorStats.map(item => item.auto_upload_count)
            },
            {
                name: '手动上传',
                type: 'bar',
                data: doctorStats.map(item => item.manual_upload_count)
            },
            {
                name: '待处理',
                type: 'bar',
                data: doctorStats.map(item => item.pending_count)
            },
            {
                name: '运行中',
                type: 'bar',
                data: doctorStats.map(item => item.running_count)
            },
            {
                name: '已完成',
                type: 'bar',
                data: doctorStats.map(item => item.completed_count)
            },
            {
                name: '总数',
                type: 'bar',
                data: doctorStats.map(item => item.total_count)
            }
        ]
    })
}

const initNewTaskChart = (data) => {
    if (!newTaskChart.value) return;
    
    if (newTaskChartInstance) {
        newTaskChartInstance.dispose();
    }
    
    newTaskChartInstance = echarts.init(newTaskChart.value);
    
    const taskData = data.doctor || data; // 兼容两种数据结构
    
    const doctorNames = taskData.map(item => item.doctor_name);
    
    const doctorStats = taskData.map(doctor => {
        if (!doctor.data || doctor.data.length === 0) {
            return {
                sys_count: 0,
                manual_count: 0,
                auto_write_count: 0,
                manual_write_count: 0,
                auto_upload_count: 0,
                manual_upload_count: 0,
                pending_count: 0,
                running_count: 0,
                completed_count: 0,
                total_count: 0
            };
        }
        
        const result = doctor.data.reduce((acc, curr) => {
            return {
                sys_count: acc.sys_count + (curr.sys_count || 0),
                manual_count: acc.manual_count + (curr.manual_count || 0),
                auto_write_count: acc.auto_write_count + (curr.auto_write_count || 0),
                manual_write_count: acc.manual_write_count + (curr.manual_write_count || 0),
                auto_upload_count: acc.auto_upload_count + (curr.auto_upload_count || 0),
                manual_upload_count: acc.manual_upload_count + (curr.manual_upload_count || 0),
                pending_count: acc.pending_count + (curr.pending_count || 0),
                running_count: acc.running_count + (curr.running_count || 0),
                completed_count: acc.completed_count + (curr.completed_count || 0),
                total_count: acc.total_count + (curr.total_count || 0)
            };
        }, {
            sys_count: 0,
            manual_count: 0,
            auto_write_count: 0,
            manual_write_count: 0,
            auto_upload_count: 0,
            manual_upload_count: 0,
            pending_count: 0,
            running_count: 0,
            completed_count: 0,
            total_count: 0
        });
        
        return result;
    });
    
    const chartTitle = dataSource.value === 'today' ? '今日任务统计' : '遗漏任务统计';
    
    newTaskChartInstance.setOption({
        color: chartColors,
        title: {
            text: chartTitle,
            left: 'center'
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            },
            formatter: function(params) {
                let result = params[0].name + '<br/>';
                params.forEach(param => {
                    if (param.value !== 0) {
                        result += param.marker + ' ' + param.seriesName + ': ' + param.value + '<br/>';
                    }
                });
                return result;
            }
        },
        legend: {
            data: ['系统任务', '手动任务', '自动书写', '手动书写', '自动上传', '手动上传', '待处理', '运行中', '已完成', '总数'],
            top: 30
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            data: doctorNames,
            axisLabel: {
                rotate: 45
            }
        },
        yAxis: {
            type: 'value'
        },
        series: [
            {
                name: '系统任务',
                type: 'bar',
                data: doctorStats.map(item => item.sys_count)
            },
            {
                name: '手动任务',
                type: 'bar',
                data: doctorStats.map(item => item.manual_count)
            },
            {
                name: '自动书写',
                type: 'bar',
                data: doctorStats.map(item => item.auto_write_count)
            },
            {
                name: '手动书写',
                type: 'bar',
                data: doctorStats.map(item => item.manual_write_count)
            },
            {
                name: '自动上传',
                type: 'bar',
                data: doctorStats.map(item => item.auto_upload_count)
            },
            {
                name: '手动上传',
                type: 'bar',
                data: doctorStats.map(item => item.manual_upload_count)
            },
            {
                name: '待处理',
                type: 'bar',
                data: doctorStats.map(item => item.pending_count)
            },
            {
                name: '运行中',
                type: 'bar',
                data: doctorStats.map(item => item.running_count)
            },
            {
                name: '已完成',
                type: 'bar',
                data: doctorStats.map(item => item.completed_count)
            },
            {
                name: '总数',
                type: 'bar',
                data: doctorStats.map(item => item.total_count)
            }
        ]
    });
}
const initDoctorStateChart = (data) =>{
    doctorStateChartInstance = echarts.init(doctorStateChart.value)
    
    // 使用与 doctorStats 相同的结构处理数据
    const doctorStateData = data.doctor || data; // 兼容两种数据结构
    
    const doctorStateNames = doctorStateData.map(item => item.doctor_name);
    
    const doctorStateStats = doctorStateData.map(doctor => {
        if (!doctor.state_stats || Object.keys(doctor.state_stats).length === 0) {
            return {
                "日常病程记录": 0,
                "日常病程记录(异常)": 0,
                "上级医师查房记录": 0,
                "阶段小结": 0,
                "入院记录(首次)": 0,
                "出院查房记录": 0,
                "出院记录": 0,
                "入院记录(内转)": 0
            };
        }
        
        return doctor.state_stats;
    });
    
    // 获取所有病程记录类型
    const allStateTypes = new Set();
    doctorStateStats.forEach(stats => {
        Object.keys(stats).forEach(type => allStateTypes.add(type));
    });
    const stateTypes = Array.from(allStateTypes);
    
    doctorStateChartInstance.setOption({
        color: chartColors,
        title: {
            text: '医生病程记录类型统计',
            left: 'center'
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            },
            formatter: function(params) {
                let result = params[0].name + '<br/>';
                params.forEach(param => {
                    if (param.value !== 0) {
                        result += param.marker + ' ' + param.seriesName + ': ' + param.value + '<br/>';
                    }
                });
                return result;
            }
        },
        legend: {
            data: stateTypes,
            top: 30,
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            top:'30%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            data: doctorStateNames,
            axisLabel: {
                rotate: 45
            }
        },
        yAxis: {
            type: 'value'
        },
        series: stateTypes.map(type => ({
            name: type,
            type: 'bar',
            data: doctorStateStats.map(stats => stats[type] || 0)
        }))
    })
}
const handleDoctorStateDateChange = () => {
    getDocumentDataTogether();
}

onMounted(() => {
    getTaskData();
    getTodayTaskData();
    getOtherTaskData();
    getDocumentDataTogether();
    
    // 默认设置为今日数据
    dataSource.value = 'today';
    currentTaskData.value = todayData.value;
    
    window.addEventListener('resize', () => {
        dailyChartInstance?.resize()
        weeklyChartInstance?.resize()
        monthlyChartInstance?.resize()
        doctorChartInstance?.resize()
        doctorStateChartInstance?.resize()
        newTaskChartInstance?.resize()
    })
})

defineExpose({
    dailyChart,
    weeklyChart,
    monthlyChart,
    doctorChart,
    doctorStateChart,
    dateRange,
})
</script>

<style scoped>
.statistics-container {
    /* padding: 20px; */
}

.chart-card {
    margin-bottom: 20px;
}

.chart {
    height: 400px;
    width: 100%;
}

.mt-20 {
    margin-top: 20px;
}

.mb-20 {
    margin-bottom: 20px;
}

.filter-card {
    margin-bottom: 20px;
}
</style>
