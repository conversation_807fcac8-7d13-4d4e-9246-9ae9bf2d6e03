<template>
  <div class="tableData">
    <h3>{{ route.meta.title }}</h3>
    <div class="topMenu">
        <div class="menuBox">
          <el-button type="primary" @click="createTemplate">新建模版</el-button>
        </div>
    </div>
    <el-table class="table" :data="tableData" style="width: 100%;height: 800px" v-loading="load">
      <el-table-column type="index" width="50" />
      <el-table-column prop="disease" label="病种" width="180" />
      <el-table-column prop="name" label="模版内容">
        <template #default="scope">
          <div>
            <el-input type="textarea" v-model="scope.row.content"></el-input>
          </div>
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" width="120">
        <template #default="scope">
          <el-button link type="primary" size="small" @click="saveTemplate(scope.row)">保存</el-button>
          <el-button link type="warning" size="small" @click="delTemplate(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>

  <el-dialog
      v-model="create_show"
      title="添加模版"
      width="75%"
  >
      <div>
        <el-form-item label="病种名称">
<!--          <el-select v-model="form.disease" class="m-2" placeholder="Select" size="large">-->
<!--            <el-option-->
<!--                v-for="item in diseaseList"-->
<!--                :key="item.name"-->
<!--                :label="item.name"-->
<!--                :value="item.name"-->
<!--            />-->
<!--          </el-select>-->
          <el-input v-model="form.disease"></el-input>
        </el-form-item>
        <el-form-item label="模版内容">
          <el-input type="textarea" v-model="form.content"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="submitForm">立即创建</el-button>
        </el-form-item>
      </div>
  </el-dialog>
</template>

<script setup>
  import {ref,nextTick} from 'vue';
  import { post } from '@/utils/request.js'
  import { message } from '@/utils/message'
  const load = ref(false)
  const tableData = ref([])
  const create_show = ref(false)

  import c from "@/utils/config";
  import { useRoute } from 'vue-router';

  const route = useRoute();


  const api_url = c.api_url;


  const diseaseList = ref([])
  const form = ref({
    disease:"",
    content:""
  })

  const autoResize = (id)=>{
    let textarea = document.getElementById(id)
    textarea.style.height = 'auto'; // 重置高度
    textarea.style.height = textarea.scrollHeight + 'px'; // 根据内容高度调整
  }


  // 获取病种列表
  const getDiseaseList = ()=>{
    load.value = true
    let url = `${api_url}/patient/getDiseaseList`
    post(url, JSON.stringify({})).then(res => {
      console.log(res.data)
      diseaseList.value = res.data
      load.value = false
    }).catch(error => {
      load.value = false
      console.error('发生错误:', error);
    });
  }
  //  创建模版
  const submitForm = ()=>{
    load.value = true
    post(`${api_url}/patient/addTemplate/`, JSON.stringify(form.value)).then(data_list => {
      create_show.value = false
      getTempList()
      message("保存成功！",'success')
      load.value = false
    }).catch(error => {
      load.value = false
      console.error('请求失败:', error);
    });
  }

  const createTemplate = ()=>{
    create_show.value = true
  }
  // 删除模版
  const delTemplate = (row)=>{
    load.value = true
    post(`${api_url}/patient/delTemplate`, JSON.stringify({id:row.id})).then(data_list => {
      tableData.value = tableData.value.filter((item)=>item.id!==row.id)
      // getTempList()
      message("删除成功！",'success')
      load.value = false
    })
    .catch(error => {
      load.value = false
      console.error('请求失败:', error);
    });
  }

  // 保存模版
  const saveTemplate = (row)=>{
      let {
        id,
        general_condition,
        cognitive_activities,
        emotional_activities,
        willpower_action,
        check_result,
        treatment_options,
        precautions,
      }=row

      let postdata = {
        general_condition,
        cognitive_activities,
        emotional_activities,
        willpower_action,
        check_result,
        treatment_options,
        precautions,
        content:`${general_condition}${cognitive_activities}${emotional_activities}${willpower_action}${check_result}${treatment_options}${precautions}。`
      }
      load.value = true
      post(`${api_url}/patient/editTemplate`, JSON.stringify({
        id:row.id,
        disease:row.disease,
        content:row.content
      })).then(data_list => {
        message("保存成功！",'success')
        load.value = false
      })
      .catch(error => {
        load.value = false
        console.error('请求失败:', error);
      });
  }
  //获取模版列表
  const getTempList = ()=>{
    load.value = true
    post(`${api_url}/patient/getTemplateList/`, JSON.stringify({})).then(res => {
      tableData.value = res.data
      // message("保存成功！",'success')
      load.value = false
    })
    .catch(error => {
      load.value = false
      console.error('请求失败:', error);
    });
  }
  getTempList()
  getDiseaseList()
</script>

<style scoped>
.tableData {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    background-color: #ffffff;
    padding: 10px;
    box-sizing: border-box;
}
h3 {
    height: 40px;
}
.topMenu {
    display: flex;
    justify-content: flex-start;
    margin-bottom: 10px;
    flex-wrap: wrap;
    gap: 5px;
}

.menuBox {
    display: flex;
    justify-content: flex-start;
    margin-bottom: 10px;
    flex-wrap: wrap;
    gap: 5px;
}

.table {
    flex: 1;
    overflow-y: auto;
}
  .case_type{
      display: flex;
  }
  .case_type_list{
    display: flex;
    flex-wrap: wrap;
  }
  .case_type_list .case_type{
    width: 100%;
  }
  .type_border{
    margin-right: 5px;
    width: 14%;
  }
  .type_border .content-item{

  }
  .tag{
    font-size: 12px;
  }
  .item-input{
    border: none;
    height: 40px;
    resize: none;
    overflow: hidden; /* 隐藏滚动条 */
    outline: none;
    width: 100%;
  }
  .content-item-1{
    border-bottom: 1px solid #00ffff;
  }
  .content-item-2{
    border-bottom: 1px solid #00ccff;
  }
  .content-item-3{
    border-bottom: 1px solid #0099ff;
  }
  .content-item-4{
    border-bottom: 1px solid #0066ff;
  }
  .content-item-5{
    border-bottom: 1px solid #0033ff;
  }
  .content-item-6{
    border-bottom: 1px solid #f7ba2a;
  }
  .content-item-7{
    border-bottom: 1px solid #0000ff;
  }
  .tag-1{color:#00ffff;}
  .tag-2{color:#00ccff;}
  .tag-3{color:#0099ff;}
  .tag-4{color:#0066ff;}
  .tag-5{color:#0033ff;}
  .tag-6{color:#f7ba2a;}
  .tag-7{color:#0000ff;}
</style>