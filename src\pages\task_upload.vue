<template>
  <div class="tableData">
    <h3>{{ route.meta.title }}</h3>
    <div class="topMenu">
      <div class="menuBox">
        <el-button style="margin-right: 15px;" type="primary" @click="viewAdviceHistory">导出成功写入({{ success_num.length
          }})</el-button>
        <el-button style="margin-right: 15px;" type="warning" @click="seeCount">查看统计</el-button>
        <el-date-picker v-model="dateRange" type="datetimerange" range-separator="To" start-placeholder="开始时间"
          end-placeholder="结束时间" @change="selectDate" />
      </div>
    </div>
    <div v-if="doctor_names_list.length > 0">
      上传医生：{{ doctor_names_list }}
    </div>
    <el-table class="table" :data="tableData" style="width: 100%" border :max-height="tableHeight" v-loading="load">
      <el-table-column type="index" prop="pid" label="编号" width="50" />
      <el-table-column prop="pid" label="患者id" width="80" />
      <el-table-column prop="pat_name" label="患者名称" width="80" />
      <el-table-column prop="state" label="任务类型" />
      <el-table-column prop="doctor_name" label="医生" />
      <el-table-column prop="document" label="AI内容" width="500">
        <template #default="scope">
          <!-- <div class="content" :class="`content${scope.row.pid}`"> {{ scope.row.document }}</div> -->
          <div class="content" :class="`content${scope.row.pid}`" v-html="formatDocument(scope.row.document)" style="white-space: pre-wrap;word-break: break-all;"></div>

        </template>
      </el-table-column>
      <el-table-column prop="document" label="HIS内容" width="500">
        <template #default="scope">
          <!-- <div class="content" :class="`content${scope.row.pid}`"> {{ scope.row.his_case }}</div> -->
          <div class="content" :class="`content${scope.row.pid}`" v-html="formatDocument(scope.row.his_case)" style="white-space: pre-wrap;word-break: break-all;"></div>

        </template>
      </el-table-column>

      <el-table-column prop="document" label="内容对比" width="500">
        <template #default="scope">
          <div :id="`doctor_case_${scope.row.task_id}`">
            <!-- <div class="content" :class="`content${scope.row.pid}`" v-html="scope.row.his_diff"></div> -->
          <div class="content" :class="`content${scope.row.pid}`" v-html="formatDocument(scope.row.his_diff)" style="white-space: pre-wrap;word-break: break-all;"></div>

          </div>
        </template>
      </el-table-column>

      <el-table-column prop="content" label="上传原因" />
      <el-table-column prop="edit_time" label="书写时间" />
      <el-table-column prop="update_time" label="操作时间" />
      <el-table-column prop="edit_time" label="创建动作">
        <template #default="scope">
          <el-tag v-if="scope.row.is_sys === 0">手动创建</el-tag>
          <el-tag v-if="scope.row.is_sys === 1">系统创建</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="edit_time" label="书写动作">
        <template #default="scope">
          <el-tag v-if="scope.row.is_auto_write === 2">手动书写</el-tag>
          <el-tag v-if="scope.row.is_auto_write === 1">自动书写</el-tag>
          <el-tag v-if="scope.row.is_auto_write === 0">待书写</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="edit_time" label="执行上传动作">
        <template #default="scope">
          <el-tag v-if="scope.row.is_auto_upload === 2">手动上传</el-tag>
          <el-tag v-if="scope.row.is_auto_upload === 1">自动上传</el-tag>
          <el-tag v-if="scope.row.is_auto_upload === 0">待上传</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="功能" width="200" fixed="right">
        <template #default="scope">
          <el-button type="primary" @click="open(scope)">{{ scope.row.show ? '收起' : '展开' }}</el-button>
          <el-button type="primary" @click="lookMore(scope)">查看详情</el-button>
        </template>
      </el-table-column>
      <el-table-column label="生成" width="120" :filters="[
        { text: '上传成功', value: 2 },
      ]" :filter-method="filterStatus" fixed="right">
        <template #default="scope">
          <el-button type="primary" v-if="scope.row.status === 0" @click="upload(scope.row)">待上传</el-button>
          <el-button type="primary" v-if="scope.row.status !== 2" @click="upload(scope.row)">上传</el-button>
          <el-button type="success" v-if="scope.row.status === 2">上传成功</el-button>
        </template>
      </el-table-column>
    </el-table>
    <Pagination style="margin-top: 10px; display: flex; justify-content: flex-end;" :currentPage="currentPage"
      :pageSize="pageSize" :total="totalItems" @current-change="handleCurrentChange" @size-change="handleSizeChange" />
  </div>



  <el-dialog v-model="show_count_see" title="数据统计" width="80%" draggable>
    <div>
      <table style="width: 100%;border: 1px solid #000000;">
        <tr class="tr" style="border: 1px solid #000000">
          <th>医生名称</th>
          <th>书写患者人数</th>
          <th>不修改人数</th>
          <th>修改人数</th>
          <th>10字以内</th>
          <th>10-50字</th>
          <th>50-100字</th>
          <th>100字以上</th>
          <th>添加字数</th>
          <th>删除字数</th>
          <th>移动字数</th>
          <th>总字数</th>
          <th>修改率</th>
        </tr>
        <tr class="tr" style="text-align: center;margin-bottom: 15px" v-for="(item, index) in count_list" :key="index">
          <td>{{ index }}</td>
          <td>{{ item.num }}人</td>
          <td>{{ item.no_modify }}人</td>
          <td>{{ item.yes_modify }}人</td>
          <td>{{ item.modify_10 }}人</td>
          <td>{{ item.modify_10_50 }}人</td>
          <td>{{ item.modify_50_100 }}人</td>
          <td>{{ item.modify_100 }}人</td>
          <td style="color: #67c23a;font-weight: bold;">{{ item.count_add_num }}</td>
          <td style="color: red;font-weight: bold;">{{ item.count_del_num }}</td>
          <td style="color: red;font-weight: bold;">{{ item.move_num }}</td>
          <td style="color: #409eff;font-weight: bold;">{{ item.count_text_mum }}</td>

          <td style="color: #409eff;font-weight: bold;" v-if="item.count_del_num + item.count_add_num !== 0">
            {{ (((item.count_del_num + item.count_add_num) / item.str_ai_doctor_num) * 100).toFixed(2) }}%</td>
          <td style="color: #409eff;font-weight: bold;" v-else>0</td>
        </tr>
        <tr class="tr trbg" style="text-align: center;font-weight: bold;border-top: 2px solid green">
          <td>统计</td>
          <td>{{ count_num.user_num }}人</td>
          <td>{{ count_num.no_modify }}人</td>
          <td>{{ count_num.no_modify > 0 ? count_num.user_num - count_num.no_modify : 0 }}人</td>

          <td>{{ count_num.modify_10 }}人</td>
          <td>{{ count_num.modify_10_50 }}人</td>
          <td>{{ count_num.modify_50_100 }}人</td>
          <td>{{ count_num.modify_100 }}人</td>

          <td>{{ count_num.count_add_num }}</td>
          <td>{{ count_num.count_del_num }}</td>
          <td>{{ count_num.count_move_num }}</td>
          <td>{{ count_num.count_text_mum }}</td>
          <td>{{ count_num.rate }}%</td>
        </tr>
      </table>
    </div>
  </el-dialog>

  <el-dialog v-model="showDetailDialog" title="详情" width="80%" draggable>
    <div style="display: flex; gap: 20px;">
      <div style="flex:1;">
        <h4>AI内容</h4>
        <div class="content" style="height:600px;overflow:auto;">
          <div  v-html="formatDocument(detailRow.document)" style="white-space: pre-wrap;word-break: break-all;"></div>
        </div>
      </div>
      <div style="flex:1;">
        <h4>HIS内容</h4>
        <div class="content" style="height:600px;overflow:auto;">
          <div  v-html="formatDocument(detailRow.his_case)" style="white-space: pre-wrap;word-break: break-all;"></div>
        </div>
      </div>
      <div style="flex:1;">
        <h4>内容对比</h4>
        <div class="content" style="height:600px;overflow:auto;">
          <div  v-html="formatDocument(detailRow.his_diff)" style="white-space: pre-wrap;word-break: break-all;"></div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue';
import { message } from '@/utils/message';
import { diffWords } from 'diff';
import c from "@/utils/config";
import { post } from '@/utils/request.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import { useRoute } from 'vue-router';
import dayjs from 'dayjs'

const a = '    今日xx主任医师查房，认真询问病史、查体后指示：患者赵平，男，39岁，于2025年06月24日10时10分01秒因“反复躯体疼痛，心慌，眠差8+年，复发加重5年”门诊入院。主要表现：患者存在躯体不适、焦虑情绪、睡眠障碍、兴趣减退、认知功能下降及自主神经功能紊乱等症状。治疗经过：患者曾多次住院治疗，使用药物包括度洛西汀片60mgbid、坦度螺酮胶囊10mgtid、奥氮平5mgqd、阿普唑仑片0.6mgqn、赛乐特40mgqd、氯硝西泮片1mgbid、硫必利、舍曲林、文拉法辛胶囊75mgtid、氯硝西泮0.5mgam1mgqn等，但疗效欠佳或因副作用停药。2023年7月23日以"焦虑性抑郁症"收入院治疗。既往史：患者曾撞墙自杀，后被家属阻拦。个人史：性格内向，兴趣爱好为打牌，吸烟20余年，20支/天，偶饮酒，不能胜任工作或学习，学习成绩一般，人际关系好。家族史：否认两系三代类似疾病史。查体：皮肤巩膜无黄染，全身浅表淋巴结未扪及；双肺呼吸音清晰，未闻及明显干湿罗音；心律齐，各瓣膜区未闻及明显病理性杂音；腹平软，无压痛、反跳痛及肌紧张，未扪及包块，肝脾肋下未及，肠鸣音3-4次/分；双下肢无水肿；生理反射存在，病理征未引出。精神检查：患者年貌相符，衣着整洁，接触主动。意识清楚，定向力准确，对答切题，否认幻觉、否认妄想症状。注意力不集中，情感反应基本协调，存在抑郁和焦虑情绪，无明显情感高涨或低落，未见冲动怪异行为。智能、记忆检查正常。自知力部分存在，社会功能受损。睡眠差，进食差。辅助检查：暂无补充。诊断：1、焦虑性抑郁症；2、睡眠障碍；3、右肾囊肿。诊断依据：1）焦虑性抑郁症：症状学标准：患者存在持续的情绪低落、兴趣丧失、焦虑、消极观念及行为、躯体不适等症状；严重程度标准：症状对患者日常生活和社会功能造成显著影响；病程标准：病程持续8年以上。2）睡眠障碍：症状学标准：患者存在入睡困难、早醒、睡眠维持困难等症状；严重程度标准：症状对患者日间功能造成显著影响；病程标准：病程持续5年以上。鉴别诊断：①双相情感障碍：患者存在情绪低落、兴趣丧失、焦虑等症状，故需要与双相情感障碍鉴别，但患者没有躁狂或轻躁狂发作的病史，且情绪低落持续时间较长，符合抑郁症的表现，故排除。②精神分裂症：患者存在情绪低落、兴趣丧失、焦虑等症状，故需要与精神分裂症鉴别，但患者没有幻觉、妄想或其他阳性症状，且病程中没有出现明显的思维紊乱或行为异常，故排除。治疗方案：在院期间积极调整治疗方案:2.继续予以奥沙西泮片7.5mgpoam、15mgpoqn、7.5mgpopm抗焦虑；奥氮平片(齐鲁)1.25mgpoqn稳定情绪；枸橼酸坦度螺酮片10mgpotid抗焦虑；盐酸度洛西汀肠溶胶囊30mgpoam抗抑郁；复方苁蓉益智胶囊1.2gpotid改善认知。0.9％氯化钠注射液250.0ml+维生素B6注射液100.0mgivqd补充营养。3.脑磁治疗、脑反射治疗促进疾病康复，提供康复指导。4.定期进行精神科相关量表检查，客观评估患者病情变化，协助治疗。5.护理上注意症状支配下出现冲动自伤、伤人、毁物、出走等行为。注意患者饮食情况及生活照顾，密切观察患者病情变化。'

const route = useRoute();

const api_url = c.api_url;

const show_count_see = ref(false)

const tableData = ref([])
const load = ref(false)
import * as XLSX from 'xlsx';

const count_list = ref([])

const currentDate = new Date();
const year = currentDate.getFullYear();
const month = currentDate.getMonth() + 1; // 月份从0开始，需要加1
const day = currentDate.getDate();

const tableHeight = ref(0)
const success_num = ref([])
const data_value = ref(year + "-" + month + "-" + day)
const doctor_names_list = ref([])
const count_num = ref({})

const dateRange = ref([]);

// dateRange.value = [
//   `${year}-${month}-${day}`,
//   `${year}-${month}-${day}`
// ]

function formatDocument(doc,indent = 0) {
  if (!doc) return '';
  let content = String(doc)
    .replace(/([。！？])/g, '$1<br>')
    .replace(/\n/g, '<br>')
    .replace(/([ ]{2,})/g, match => '&nbsp;'.repeat(match.length)) // 多空格缩进
    .replace(/(\（\d+\）)/g, '<br>$1')
    .replace(/(\d+）)/g, '<br>$1')    // 1）2）3）换行
    .replace(
      /([^\u4e00-\u9fa5a-zA-Z0-9]|^)((\d+[\.、])|[①②③④⑤⑥⑦⑧⑨⑩]|[一二三四五六七八九十][\.、])/g,
      '$1<br>$2'
    )
    // 加粗所有“xxx：”样式的标题
    .replace(/([\u4e00-\u9fa5_a-zA-Z0-9]+：)/g, '<b>$1</b>');
  return content;
}
const open = (scope) => {
  scope.row.show = !scope.row.show;
  if (scope.row.show) {
    let docs = document.querySelectorAll(".content" + scope.row.pid)
    docs.forEach(doc => {
      doc.style.height = "auto"
      doc.style.overflow = "hidden"
    })
  } else {
    let docs = document.querySelectorAll(".content" + scope.row.pid)
    docs.forEach(doc => {
      doc.style.height = "200px"
      doc.style.overflow = "auto"
    })
  }
  // 展开或收起后滚动当前行到最佳位置
  nextTick(() => {
    // 获取当前行的 el-table__row
    const table = document.querySelector('.el-table__body-wrapper .el-table__body')
    if (table) {
      // 查找当前行
      const rows = table.querySelectorAll('.el-table__row')
      for (let row of rows) {
        // 通过患者id或唯一标识判断
        if (row.innerText.includes(scope.row.pat_name)) {
          row.scrollIntoView({ block: 'top', behavior: 'smooth' })
          break
        }
      }
    }
  })
}
const showDetailDialog = ref(false)
const detailRow = ref({})

const lookMore = (scope) => {
  detailRow.value = scope.row
  showDetailDialog.value = true
}

const seeCount = () => {
  countCase()
  show_count_see.value = true;
}

// 选择时间
const selectDate = () => {
  console.log(data_value.value)
  getWord()
}

// 计算表格高度
const calculateTableHeight = () => {
  const windowHeight = window.innerHeight
  // 减去按钮区域高度(60px)和页面边距(40px)
  tableHeight.value = windowHeight - 100 - 20
}

onMounted(() => {
  calculateTableHeight()
  window.addEventListener('resize', calculateTableHeight)
})

onUnmounted(() => {
  window.removeEventListener('resize', calculateTableHeight)
})

const filterStatus = (value, row) => {
  if (value === '') {
    return true
  } else {
    return row.status === value
  }
}
const upload = (row) => {
  searchValueName(row)
}
// 导出输出成功函数
const viewAdviceHistory = () => {
  let list = tableData.value.filter((item) => item.status === 2).map((item) => {
    return {
      "医生": item.doctor_name,
      "患者": item.pat_name
    }
  })
  // 创建工作簿
  const wb = XLSX.utils.book_new();
  // 将数据转换为工作表
  const ws = XLSX.utils.json_to_sheet(list);
  // 将工作表添加到工作簿
  XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');
  // 导出 Excel 文件
  XLSX.writeFile(wb, '写入名单.xlsx');
}
// 统计数据
const countCase = () => {

  let count_del_num = 0;
  let count_add_num = 0;
  let count_text_mum = 0;
  let user_num = 0;
  let no_modify = 0;
  let rate = 0;
  let count_move_num = 0;

  let modify_10 = 0;
  let modify_10_50 = 0;
  let modify_50_100 = 0;
  let modify_100 = 0;

  let arr = {}
  tableData.value = tableData.value.map((item) => {

    item.del_num = 0;
    item.add_num = 0;
    item.text_num = 0;
    item.no_modify = 0;
    item.yes_modify = 0;
    item.move_num = 0;
    item.str_ai_doctor_num = 0;
    item.modify_10 = 0;
    item.modify_10_50 = 0;
    item.modify_50_100 = 0;
    item.modify_100 = 0;

    item.text_num = item.document.length;

    let set2 = item.his_case
    let add_str_arr = []
    let del_str_arr = []
    let add_str = []
    let del_str = []

    let doc = document.getElementById("doctor_case_" + item.task_id)
    let add = doc.querySelectorAll(".diff-added")
    let del = doc.querySelectorAll(".diff-removed")

    del.forEach((obj) => {
      // item.del_num+=obj.innerText.length
      del_str_arr.push(obj.innerText)
    })

    add.forEach((obj, i) => {
      if (i > 0 && i < (add.length - 1)) {
        // item.add_num+=obj.innerText.length
        add_str_arr.push(obj.innerText)
      }
    })

    add_str = add_str_arr.filter((row) => {
      return del_str_arr.indexOf(row) === -1
    })

    del_str = del_str_arr.filter((row) => {
      return add_str_arr.indexOf(row) === -1
    })

    add_str = add_str.join("").replace(/\s/g, '').replace(/[，。！？、\s]/g, '')
    del_str = del_str.join("").replace(/\s/g, '').replace(/[，。！？、\s]/g, '')

    item.add_num += add_str.length
    item.del_num += del_str.length



    let move_str = add_str_arr.filter(value => del_str_arr.includes(value));
    item.move_num = move_str.join("").replace(/\s/g, '').length
    set2 = set2.replace(/\s/g, '')
    if (item.document.replace(/\s/g, '') === set2) {
      if (set2) {
        item.no_modify += 1
        no_modify += 1
      }
    } else {
      if (set2) {
        let len = item.add_num + item.del_num
        item.yes_modify += 1
        if (len < 10) {
          item.modify_10 += 1
          modify_10 += 1;
        } else if (len >= 10 && len < 50) {
          item.modify_10_50 += 1
          modify_10_50 += 1;
        } else if (len >= 50 && len < 100) {
          item.modify_50_100 += 1
          modify_50_100 += 1;
        } else if (len >= 100) {
          item.modify_100 += 1;
          modify_100 += 1;
        }
      }
    }

    if (set2.length > 0) {
      item.str_ai_doctor_num = item.document.length + set2[0].replace(/\s/g, '').length
    }

    count_del_num += item.del_num;
    count_add_num += item.add_num;
    count_text_mum += item.text_num
    user_num += 1
    count_move_num += item.move_num

    if (arr[item.doctor_name]) {
      let r = arr[item.doctor_name]
      arr[item.doctor_name] = {
        num: r.num += 1,
        count_del_num: r.count_del_num + item.del_num,
        count_add_num: r.count_add_num + item.add_num,
        count_text_mum: r.count_text_mum + item.text_num,
        no_modify: r.no_modify + item.no_modify,
        yes_modify: r.yes_modify + item.yes_modify,
        str_ai_doctor_num: r.str_ai_doctor_num + item.str_ai_doctor_num,
        move_num: r.move_num + item.move_num,
        modify_10: item.modify_10 + r.modify_10,
        modify_10_50: item.modify_10_50 + r.modify_10_50,
        modify_50_100: item.modify_50_100 + r.modify_50_100,
        modify_100: item.modify_100 + r.modify_100,
      }
    } else {
      arr[item.doctor_name] = {
        num: 1,
        count_del_num: item.del_num,
        count_add_num: item.add_num,
        count_text_mum: item.text_num,
        no_modify: item.no_modify,
        yes_modify: item.yes_modify,
        str_ai_doctor_num: item.str_ai_doctor_num,
        move_num: item.move_num,
        modify_10: item.modify_10,
        modify_10_50: item.modify_10_50,
        modify_50_100: item.modify_50_100,
        modify_100: item.modify_100,
      }
    }
    return item
  })

  count_list.value = arr

  for (let key in arr) {
    let item = arr[key]
    rate += (item.num / user_num) * ((item.count_del_num + item.count_add_num) / item.str_ai_doctor_num)
  }

  count_num.value = {
    count_del_num,
    count_add_num,
    count_text_mum,
    user_num,
    no_modify,
    count_move_num,
    rate: (rate * 100).toFixed(2),
    modify_10,
    modify_10_50,
    modify_50_100,
    modify_100
  }

}
const searchValueName = (row) => {
  load.value = true;
  post(`${api_url}/AiCase/exeTask`, JSON.stringify({ doctor_id: row.doctor_id, task_id: row.task_id })).then(res => {
    message(res.data)
    load.value = false
  }).catch(error => {
    load.value = false
  });
}

const getWord = (row) => {
  // 处理时间筛选
  let create_time__gte = ''
  let create_time__lte = ''
  if (Array.isArray(dateRange.value) && dateRange.value.length === 2) {
      create_time__gte = dayjs(dateRange.value[0]).format('YYYY-MM-DD HH:mm:ss')
      create_time__lte = dayjs(dateRange.value[1]).format('YYYY-MM-DD HH:mm:ss')
  }
  load.value = true;
  let data_list = {
    start_date: create_time__gte,
    end_date: create_time__lte
  }
  post(`${api_url}/AiCase/getExeCaseList`, JSON.stringify(data_list)).then(res => {
    let doctor_names = []
    tableData.value = res.data.map((row) => {
      row.his_case = row.his_case.replace(/^\d{4}年\d{2}月\d{2}日\d{2}时\d{2}分\t/, '').replace(/主任医师查房记录	/, '').replace(/主治医师查房记录	/, '')
      let his_case = row.his_case.split('医师签名')
      if (his_case[0]) {
        row.his_case = his_case[0]
      }

      if (doctor_names.indexOf(row.doctor_name) === -1) {
        doctor_names.push(row.doctor_name)
      }
      let isJson = isJsonString(row.document)
      if (isJson) {
        let json_content = JSON.parse(row.document)
        row.document = stringifyObject(json_content);
      }

      if (row.document && row.his_case) {
        let sttr = markDifferences(row.document, row.his_case)
        row.his_diff = sttr.result1
      }
      console.log(row)
      return row
    })
    success_num.value = tableData.value.filter((item) => item.status === 2)
    doctor_names_list.value = doctor_names

    load.value = false
  }).catch(error => {
    load.value = false
  });
}

function stringifyObject(obj, parentKey = '') {
  return Object.entries(obj).map(([key, value]) => {
    const newKey = parentKey ? `${parentKey}.${key}` : key; // 处理嵌套键
    if (typeof value === 'object' && value !== null) {
      return stringifyObject(value, newKey); // 递归处理嵌套对象
    }
    return `${newKey}: ${value}`; // 拼接键值对
  }).join(' ');
}

function isJsonString(str) {
  try {
    JSON.parse(str);
    return true; // 是 JSON 字符串
  } catch (e) {
    return false; // 不是 JSON 字符串
  }
}

getWord()

const markDifferences = (str1, str2) => {
  const diffResult = diffWords(str1, str2)
  const result1 = diffResult.map(part => {
    if (part.added) {
      return `<span class="diff-added">${part.value}</span>`;
    } else if (part.removed) {
      return `<span class="diff-removed">${part.value}</span>`;
    } else {
      return part.value;
    }
  }).join('');

  const result2 = diffResult.map(part => {
    if (part.added) {
      return `<span class="diff-added">${part.value}</span>`;
    } else if (part.removed) {
      return `<span class="diff-removed">${part.value}</span>`;
    } else {
      return part.value;
    }
  }).join('');

  return { result1, result2 };
}

</script>

<style>
/* 让表格内容顶部对齐 */
.el-table .el-table__cell {
  vertical-align: top;
}
.content {
  display: block;
  height: 200px;
  overflow-y: auto;
  /* 可选：让内容内的文本也顶部对齐 */
  white-space: pre-wrap;
}

.tableData {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  background-color: #ffffff;
  padding: 10px;
  box-sizing: border-box;
}

h3 {
  height: 40px;
}

.topMenu {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 10px;
  flex-wrap: wrap;
  gap: 5px;
}

.menuBox {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 10px;
  flex-wrap: wrap;
  gap: 5px;
}

.table {
  flex: 1;
  overflow-y: auto;
}

.diff-added {
  color: green
}

.diff-removed {
  color: rgb(255, 17, 0)
}
</style>
