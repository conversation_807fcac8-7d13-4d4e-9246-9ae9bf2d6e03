<template>
    <div class="tableData">
        <h3>{{ route.meta.title }}</h3>
        <div class="topMenu">
            <div class="menuBox">
                <el-input class="menu" placeholder="Loki查询语句" v-model="query" clearable
                    @input="debouncedGetGridDataAgain"></el-input>
                <el-input class="menu" placeholder="关键字查询" v-model="contain_kw" clearable
                    @input="debouncedGetGridDataAgain"></el-input>
                <el-input class="menu" placeholder="最大数量" v-model="limit" clearable
                    @input="debouncedGetGridDataAgain"></el-input>
                <el-select class="menu" v-model="direction" placeholder="查询方向" clearable
                    @change="debouncedGetGridDataAgain">
                    <el-option label="正序" value="BACKWARD"></el-option>
                    <el-option label="倒序" value="FORWARD"></el-option>
                </el-select>
                <el-select class="menu" v-model="logLevel" placeholder="日志级别" clearable
                    @change="debouncedGetGridDataAgain">
                    <el-option label="DEBUG" value="DEBUG"></el-option>
                    <el-option label="INFO" value="INFO"></el-option>
                    <el-option label="WARNING" value="WARNING"></el-option>
                    <el-option label="ERROR" value="ERROR"></el-option>
                </el-select>
                <el-date-picker class="menu" v-model="dateRange" type="datetimerange" range-separator="至"
                    start-placeholder="开始时间" end-placeholder="结束时间" format="YYYY-MM-DD HH:mm:ss"
                    value-format="YYYY-MM-DD HH:mm:ss" @change="debouncedGetGridDataAgain" />
            </div>
            <!-- <el-button type="primary" @click="refreshData">刷新</el-button> -->
        </div>
        <el-table ref="tableRef" class="table" :data="filteredTableData" v-loading="load"
            @selection-change="handleSelectionChange">
            <el-table-column width="300" label="文件" prop="fileName">
                <template #default="{ row }">
                    <span>{{ row.fileName }}</span>
                </template>
            </el-table-column>
            <el-table-column width="180" label="时间戳" prop="timestamp">
                <template #default="{ row }">
                    <span>{{ formatTimestamp(row.timestamp) }}</span>
                </template>
            </el-table-column>
            <el-table-column width="120" label="级别" prop="level">
                <template #default="{ row }">
                    <el-tag :type="getLogLevelType(row.level)" size="small">{{ row.level }}</el-tag>
                </template>
            </el-table-column>
            <el-table-column width="120" label="模块" prop="module"></el-table-column>
            <el-table-column label="SQL语句" prop="sql">
                <template #default="{ row }">
                    <div class="sql-content">
                        <!-- <el-button type="text" @click="showSqlDetail(row)" size="small">查看详情</el-button> -->
                        <div class="sql-preview">{{ getSqlPreview(row.sql) }}</div>
                    </div>
                </template>
            </el-table-column>
            <!-- <el-table-column width="120" label="参数" prop="params">
                <template #default="{ row }">
                    <el-button type="text" @click="showParamsDetail(row)" size="small">查看参数</el-button>
                </template>
            </el-table-column> -->
            <el-table-column width="120" label="操作">
                <template #default="{ row }">
                    <div>
                        <el-button type="primary" size="small" @click="showSqlDetail(row)">查看详情</el-button>
                        <!-- <el-button type="primary" size="small" @click="copyLog(row)">复制</el-button> -->
                    </div>
                </template>
            </el-table-column>
        </el-table>
        <!-- <Pagination style="margin-top: 10px; display: flex; justify-content: flex-end;" :currentPage="currentPage"
            :pageSize="pageSize" :total="totalItems" :pageSizes="[20, 30, 40, 50, 100]"
            @current-change="handleCurrentChange" @size-change="handleSizeChange" /> -->

        <!-- SQL详情弹窗 -->
        <el-dialog v-model="sqlDialogVisible" title="日志详情" width="80%">
            <div class="sql-detail">
                <h4>日志内容：</h4>
                <div class="code-block">
                    <pre><code v-html="currentSql"></code></pre>
                </div>
            </div>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="sqlDialogVisible = false">关闭</el-button>
                    <el-button type="primary" @click="copyToClipboard(getRawContent())">复制内容</el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Loading, Warning, Document } from '@element-plus/icons-vue';
import c from "@/utils/config";
import Pagination from '@/components/Pagination.vue';
import dayjs from 'dayjs'
import { json } from 'd3';
import { useRoute } from 'vue-router';
import { baseUrl } from '../utils/baseUrl';
import * as XLSX from 'xlsx';
import config from '../utils/config';

// 防抖函数
const debounce = (func, delay) => {
    let timeoutId;
    return function (...args) {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => func.apply(this, args), delay);
    };
};

const route = useRoute();
const api = config.py_api_url
const api1 = baseUrl.url10
const selectedRows = ref([]) // 选中的行

// 处理表格选择变化
const handleSelectionChange = (selection) => {
    selectedRows.value = selection
}

// 搜索相关
const limit = ref(100)
const query = ref('')
const contain_kw = ref('')
const direction = ref('BACKWARD')
const logLevel = ref('')
const dateRange = ref([])

// 计算属性：根据搜索条件过滤数据
const filteredTableData = computed(() => {
    let filtered = tableData.value

    if (logLevel.value) {
        filtered = filtered.filter(item => item.level === logLevel.value)
    }

    return filtered
})

const load = ref(false)
const tableData = ref([])

// 表格引用和多选相关
const tableRef = ref(null)

// 分页相关数据
const currentPage = ref(1)
const pageSize = ref(100)
const totalItems = ref(0)

// 弹窗相关
const sqlDialogVisible = ref(false)
const currentSql = ref('')
const currentParams = ref('')

// 格式化时间戳
const formatTimestamp = (timestamp) => {
    if (!timestamp) return ''
    return dayjs(timestamp).format('YYYY-MM-DD HH:mm:ss')
}


const getLogLevelType = (level) => {
    const levelMap = {
        'DEBUG': 'info',
        'INFO': 'success',
        'WARNING': 'warning',
        'ERROR': 'danger'
    }
    return levelMap[level] || 'info'
}

// 获取SQL预览
const getSqlPreview = (sql) => {
    if (!sql) return ''
    return sql.length > 100 ? sql.substring(0, 100) + '...' : sql
}

// 显示SQL详情
const showSqlDetail = (row) => {
    const rawContent = row.raw || row.body || ''
    currentSql.value = formatLogContent(rawContent)
    currentParams.value = JSON.stringify(row.params, null, 2) || ''
    sqlDialogVisible.value = true
}

// 获取原始内容（用于复制）
const getRawContent = () => {
    // 移除HTML标签，获取纯文本内容
    return currentSql.value.replace(/<[^>]*>/g, '')
}

// 格式化日志内容
const formatLogContent = (content) => {
    if (!content) return ''
    
    // 检查是否包含HTTP错误信息
    if (content.includes('HTTP error') || content.includes('Server error')) {
        return formatHTTPErrorLog(content)
    }
    
    // 检查是否包含数据库连接信息
    if (content.includes('Closed connection') || content.includes('with params:')) {
        return formatDatabaseLog(content)
    }
    
    // 检查是否包含SQL语句
    if (content.includes('UPDATE') || content.includes('INSERT') || content.includes('SELECT') || content.includes('DELETE')) {
        return formatSQLLog(content)
    }
    
    // 检查是否包含JSON参数
    if (content.includes('[') && content.includes(']')) {
        return formatJSONLog(content)
    }
    
    return content
}

// 格式化数据库连接日志
const formatDatabaseLog = (content) => {
    const parts = content.split(' | ')
    if (parts.length >= 4) {
        const timestamp = parts[0]
        const level = parts[1]
        const module = parts[2]
        const message = parts[3]
        
        let formattedMessage = message
        // 格式化参数部分
        if (message.includes('with params:')) {
            const [action, paramsStr] = message.split('with params:')
            try {
                const params = JSON.parse(paramsStr.trim())
                formattedMessage = `${action}\n参数:\n${JSON.stringify(params, null, 2)}`
            } catch (e) {
                formattedMessage = message
            }
        }
        
        return `时间: ${timestamp}\n级别: ${level}\n模块: ${module}\n操作: ${formattedMessage}`
    }
    return content
}

// 格式化SQL日志
const formatSQLLog = (content) => {
    const parts = content.split(' | ')
    if (parts.length >= 4) {
        const timestamp = parts[0]
        const level = parts[1]
        const module = parts[2]
        const sql = parts[3]
        
        let formattedSQL = sql
        // 提取并格式化参数
        const paramsMatch = sql.match(/\[(.*)\]/)
        if (paramsMatch) {
            try {
                const params = JSON.parse(paramsMatch[1])
                const sqlWithoutParams = sql.replace(/\[.*\]/, '')
                
                // 美化SQL语句显示
                const formattedSQLStatement = formatSQLStatement(sqlWithoutParams)
                
                // 美化参数显示
                const formattedParams = formatSQLParams(params)
                
                formattedSQL = `SQL语句:\n${formattedSQLStatement}\n\n参数:\n${formattedParams}`
            } catch (e) {
                formattedSQL = sql
            }
        }
        
        return `时间: ${timestamp}\n级别: ${level}\n模块: ${module}\n${formattedSQL}`
    }
    return content
}

// 格式化SQL语句
const formatSQLStatement = (sql) => {
    if (!sql) return ''
    
    // 替换常见的SQL关键字，使其更易读
    let formatted = sql
        .replace(/UPDATE\s+/gi, 'UPDATE ')
        .replace(/SET\s+/gi, '\nSET ')
        .replace(/WHERE\s+/gi, '\nWHERE ')
        .replace(/INSERT\s+INTO\s+/gi, 'INSERT INTO ')
        .replace(/VALUES\s+/gi, '\nVALUES ')
        .replace(/SELECT\s+/gi, 'SELECT ')
        .replace(/FROM\s+/gi, '\nFROM ')
        .replace(/JOIN\s+/gi, '\nJOIN ')
        .replace(/ON\s+/gi, '\nON ')
        .replace(/GROUP\s+BY\s+/gi, '\nGROUP BY ')
        .replace(/ORDER\s+BY\s+/gi, '\nORDER BY ')
        .replace(/LIMIT\s+/gi, '\nLIMIT ')
        .replace(/AND\s+/gi, '\nAND ')
        .replace(/OR\s+/gi, '\nOR ')
    
    // 添加语法高亮标记
    formatted = formatted
        .replace(/\b(UPDATE|SET|WHERE|INSERT|INTO|VALUES|SELECT|FROM|JOIN|ON|GROUP|BY|ORDER|LIMIT|AND|OR)\b/gi, '<span class="sql-keyword">$1</span>')
        .replace(/`([^`]+)`/g, '<span class="sql-table">`$1`</span>')
        .replace(/'([^']+)'/g, '<span class="sql-string">\'$1\'</span>')
        .replace(/\b(\d+)\b/g, '<span class="sql-number">$1</span>')
    
    return formatted
}

// 格式化SQL参数
const formatSQLParams = (params) => {
    if (!Array.isArray(params)) return JSON.stringify(params, null, 2)
    
    const formattedParams = []
    params.forEach((param, index) => {
        let formattedParam = param
        
        // 处理特殊类型
        if (param === null || param === undefined) {
            formattedParam = 'NULL'
        } else if (typeof param === 'string') {
            formattedParam = `'${param}'`
        } else if (param instanceof Date || (typeof param === 'object' && param.datetime)) {
            // 处理datetime对象
            formattedParam = `'${param}'`
        } else if (typeof param === 'boolean') {
            formattedParam = param ? 'TRUE' : 'FALSE'
        } else if (typeof param === 'number') {
            formattedParam = param.toString()
        }
        
        formattedParams.push(`参数${index + 1}: ${formattedParam}`)
    })
    
    return formattedParams.join('\n')
}

// 格式化HTTP错误日志
const formatHTTPErrorLog = (content) => {
    const parts = content.split(' | ')
    
    // 处理不同格式的HTTP错误日志
    if (parts.length >= 4) {
        // 标准格式：时间 | 级别 | 模块 | 错误信息
        const timestamp = parts[0]
        const level = parts[1]
        const module = parts[2]
        const errorMessage = parts[3]
        
        return formatHTTPErrorContent(timestamp, level, module, errorMessage)
    } else if (parts.length === 3) {
        // 简化格式：时间 | 级别 | 错误信息
        const timestamp = parts[0]
        const level = parts[1]
        const errorMessage = parts[2]
        
        return formatHTTPErrorContent(timestamp, level, '', errorMessage)
    }
    
    return content
}

// 格式化HTTP错误内容
const formatHTTPErrorContent = (timestamp, level, module, errorMessage) => {
    // 解析HTTP错误信息
    let formattedError = errorMessage
    
    // 提取HTTP状态码
    const statusMatch = errorMessage.match(/('(\d+)\s+([^']+)')/)
    if (statusMatch) {
        const statusCode = statusMatch[2]
        const statusText = statusMatch[3]
        formattedError = `HTTP状态: <span class="error-status">${statusCode} ${statusText}</span>`
    }
    
    // 提取URL
    const urlMatch = errorMessage.match(/for url '([^']+)'/)
    if (urlMatch) {
        const url = urlMatch[1]
        formattedError += `\n请求URL: <span class="error-url">${url}</span>`
    }
    
    // 提取查询参数
    const queryMatch = urlMatch ? urlMatch[1].match(/\?([^']+)/) : null
    if (queryMatch) {
        const queryParams = decodeURIComponent(queryMatch[1])
        formattedError += `\n查询参数:\n<span class="error-params">${formatQueryParams(queryParams)}</span>`
    }
    
    const moduleInfo = module ? `\n模块: ${module}` : ''
    return `时间: ${timestamp}\n级别: <span class="error-level">${level}</span>${moduleInfo}\n错误信息:\n${formattedError}`
}

// 格式化查询参数
const formatQueryParams = (queryString) => {
    const params = new URLSearchParams(queryString)
    const formattedParams = []
    
    for (const [key, value] of params.entries()) {
        formattedParams.push(`  ${key}: ${value}`)
    }
    
    return formattedParams.join('\n')
}

// 格式化JSON日志
const formatJSONLog = (content) => {
    const parts = content.split(' | ')
    if (parts.length >= 4) {
        const timestamp = parts[0]
        const level = parts[1]
        const module = parts[2]
        const data = parts[3]
        
        let formattedData = data
        // 尝试解析JSON
        try {
            const jsonMatch = data.match(/\[(.*)\]/)
            if (jsonMatch) {
                const jsonData = JSON.parse(jsonMatch[1])
                formattedData = `数据:\n${JSON.stringify(jsonData, null, 2)}`
            }
        } catch (e) {
            formattedData = data
        }
        
        return `时间: ${timestamp}\n级别: ${level}\n模块: ${module}\n${formattedData}`
    }
    return content
}

// 显示参数详情
const showParamsDetail = (row) => {
    currentSql.value = ''
    currentParams.value = JSON.stringify(row.params, null, 2) || ''
    sqlDialogVisible.value = true
}

// 复制日志
const copyLog = (row) => {
    const logText = `时间: ${formatTimestamp(row.timestamp)}\n级别: ${row.level}\n模块: ${row.module}\nSQL: ${row.sql}\n参数: ${JSON.stringify(row.params, null, 2)}`
    copyToClipboard(logText)
    ElMessage.success('日志已复制到剪贴板')
}

// 复制到剪贴板
const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text).then(() => {
        ElMessage.success('已复制到剪贴板')
    }).catch(() => {
        ElMessage.error('复制失败')
    })
}

const getGridData = () => {
    load.value = true
    // 处理时间筛选
    let start_time = ''
    let end_time = ''
    if (Array.isArray(dateRange.value) && dateRange.value.length === 2) {
        start_time = dayjs(dateRange.value[0]).format('YYYY-MM-DD HH:mm:ss')
        end_time = dayjs(dateRange.value[1]).format('YYYY-MM-DD HH:mm:ss')
    }

    const params = {
        limit: limit.value,
        start_time,
        end_time,
        query: query.value,
        contain_kw: contain_kw.value,
        direction: direction.value,
        // page: currentPage.value,
        // page_size: pageSize.value,
        // name: curName.value,
        // category_level1: curCategory.value, 
        // efficacy: curEfficacy.value, 
    }
    // 过滤空参数
    const filteredParams = Object.entries(params).filter(([key, value]) => value !== '' && value !== undefined && value !== null);
    const queryString = new URLSearchParams(filteredParams).toString();

    fetch(api + `/v1/api/for_php/medical-record-status/loki-range-query?${queryString}`, {
        method: 'GET',
        headers: {
            'x-api-key': 'API655c18174462a06ede7ffbfa5aca4270c645b9b78ca30817e3a1bd496f34e04b',
            'authorization': 'Bearer 6787c226c3eae7.874922141Z3hNBMuGI'
        },
    })
        .then(response => response.json())
        .then(res => {
            tableData.value = []
            // 处理返回的日志数据
            if (res.data && res.data.data && res.data.data.result && res.data.data.result.length > 0) {
                res.data.data.result.forEach(data => {
                    
                     data.values.map((item,index) => {
                        const logLine = item[1] // 日志内容在第二个元素
                        const parts = logLine.split(' | ')
                        
                        tableData.value.push({
                            body: item,
                            fileName: data.stream.filename,
                            timestamp: parts[0],
                            level: parts[1],
                            module: parts[2],
                            sql: parts[3],
                            params: extractParams(parts[3]),
                            raw: logLine
                        })
                    })
                })
            } else {
                tableData.value = []
            }
            
            totalItems.value = tableData.value.length
            load.value = false;
        })
        .catch(error => {
            load.value = false;
            ElMessage.error('获取数据失败，请稍后重试')
        });
}

// 提取SQL参数
const extractParams = (sqlLine) => {
    try {
        const paramsMatch = sqlLine.match(/\[(.*)\]/)
        if (paramsMatch) {
            return JSON.parse(paramsMatch[1])
        }
        return []
    } catch (error) {
        return []
    }
}

// 刷新数据
const refreshData = () => {
    getGridData()
}

const getGridDataAgain = () => {
    currentPage.value = 1
    getGridData()
}

// 创建防抖版本的 getGridDataAgain
const debouncedGetGridDataAgain = debounce(getGridDataAgain, 500);

// 处理页码变化
const handleCurrentChange = (val) => {
    currentPage.value = val
    // 页码变化时重新获取数据
    getGridData()
}

// 处理每页显示条数变化
const handleSizeChange = (val) => {
    pageSize.value = val
    // 重置为第一页
    currentPage.value = 1
    // 重新获取数据
    getGridData()
}

onMounted(() => {
    getGridData()
})
onUnmounted(() => {
})
</script>
<style scoped>
.tableData {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    background-color: #ffffff;
    padding: 10px;
    box-sizing: border-box;
}

h3 {
    height: 40px;
}

.topMenu {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    flex-wrap: wrap;
    gap: 5px;
}

.menuBox {
    display: flex;
    justify-content: flex-start;
    margin-bottom: 10px;
    flex-wrap: wrap;
    gap: 5px;
}

.table {
    flex: 1;
    overflow-y: auto;
}

.menu {
    width: 200px;
}

.sql-content {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.sql-preview {
    font-family: 'Courier New', monospace;
    font-size: 12px;
    color: #666;
    background-color: #f5f5f5;
    padding: 5px;
    border-radius: 3px;
    word-break: break-all;
}

.sql-detail {
    max-height: 600px;
    overflow-y: auto;
}

.sql-detail h4 {
    margin-bottom: 10px;
    color: #333;
}

.code-block {
    background-color: #1e1e1e;
    border-radius: 6px;
    padding: 16px;
    margin: 10px 0;
    overflow-x: auto;
    color: #d4d4d4;
    border: 1px solid #3c3c3c;
}

.code-block pre {
    margin: 0;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', 'Liberation Mono', 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.6;
    color: #d4d4d4;
    white-space: pre-wrap;
    word-wrap: break-word;
}

/* SQL语句高亮 */
.code-block pre .sql-keyword {
    color: #569cd6;
    font-weight: bold;
}

.code-block pre .sql-table {
    color: #4ec9b0;
}

.code-block pre .sql-string {
    color: #ce9178;
}

.code-block pre .sql-number {
    color: #b5cea8;
}

.code-block pre .sql-comment {
    color: #6a9955;
}

/* HTTP错误高亮 */
.code-block pre .error-status {
    color: #f44336;
    font-weight: bold;
}

.code-block pre .error-url {
    color: #2196f3;
    word-break: break-all;
}

.code-block pre .error-params {
    color: #ff9800;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', 'Liberation Mono', 'Courier New', monospace;
}

.code-block pre .error-level {
    color: #f44336;
    font-weight: bold;
}

.code-block code {
    font-family: inherit;
    background: none;
    padding: 0;
    border: none;
    color: inherit;
}
</style>
