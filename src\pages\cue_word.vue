<template>
  <div class="input" v-loading="load">
    <div class="input-list">
      <el-input style="height: 100%" v-model="value" type="textarea" />
    </div>

<!--    <div class="tableData">-->
<!--      <el-table :data_list="user_list" style="width: 100%;height: 800px;">-->
<!--        <el-table-column fixed="right" label="名称">-->
<!--          <template #default="scope">-->
<!--            <el-button :type="index==scope.row.id?'primary':''" size="small" @click="handleClick(scope.row)">{{scope.row.patient_name}}({{scope.row.id}})</el-button>-->
<!--          </template>-->
<!--        </el-table-column>-->
<!--      </el-table>-->
<!--    </div>-->
  </div>
  <div class="showtext">
    {{text}}
  </div>
  <div class="showtext" v-if="doctorAdvice.length>0">
      <h4>医嘱信息</h4>
      <el-table :data="doctorAdvice">
        <el-table-column fixed="right" label="医嘱内容">
          <template #default="scope">
              <span>{{scope.row.order_content}}</span>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="医生嘱托">
          <template #default="scope">
            <span>{{scope.row.order_drask}}</span>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="单次用量">
          <template #default="scope">
            <span>{{scope.row.order_once_qunt}}</span>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="总给予量">
          <template #default="scope">
            <span>{{scope.row.order_total_qunt}}</span>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="剂量单位">
          <template #default="scope">
            <span>{{scope.row.order_once_qunt_unit}}</span>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="开始时间">
          <template #default="scope">
            <span>{{scope.row.order_exe_time_start}}</span>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="结束时间">
          <template #default="scope">
            <span>{{scope.row.order_exe_time_end}}</span>
          </template>
        </el-table-column>
      </el-table>
  </div>
  <div>
    <el-button :type="btn_type===item.type?'primary':''"
       v-for="(item,index) in type_list"
       :key="index"
       @click="select_word(item.type)"
    >
      {{item.name}}
    </el-button>
<!--    <el-button type="primary" v-for="(item,index) in type_list" :key="index">Primary</el-button>-->
    <br>
    <el-button type="primary" @click="sub" v-loading="load">提交</el-button>
<!--    <el-button type="warning" @click="seeAdvice">查看医嘱</el-button>-->
<!--    <el-button type="warning" @click="upload">上传提示词</el-button>-->
  </div>

</template>
<script setup>
  import { ref } from 'vue';
  import { post } from '@/utils/request.js'

  const doctorAdvice = ref([])
  const user_list = ref([])
  const value = ref("")
  const tableData = ref([])
  const index = ref("")
  const text = ref("")
  const load = ref(false)
  const select_row = ref({})

  import c from "@/utils/config";
import { baseUrl } from '../utils/baseUrl';
  const api_url = c.api_url;


  const type_list=ref([
    // {type:1, name:"日常病程记录"},
    // {type:22, name:"日常病程记录(异常)"},
    // {type:2, name:"首次病程记录"},
    // {type:3, name:"首次病程记录(内转)"},
    // {type:4, name:"入院记录(合作)"},
    // {type:6, name:"入院记录(不合作)"},
    // {type:7, name:"入院记录(复发)"},
    // {type:8, name:"入院记录(内转)"},
    //
    // {type:51, name:"出院记录"},
    // {type:52, name:"出院查房记录"},
    // {type:63, name:"上级医师查房"},
    // {type:74, name:"三级医师查房(主治)"},
    // {type:75, name:"三级医师查房(主任)"},
    // {type:10, name:"阶段小结"},
  ])

  const btn_type = ref(1)
  const select_word=(type)=>{
    btn_type.value = type
    handleClick(select_row.value)
  }

  const sub = ()=>{
    text.value = "生成中..."
    load.value = true
    let url = `${baseUrl.hospital_zhixuee}/center/llm`
    let data = {
      messages:[
        {
          role:"user",
          content:value.value
        }
      ]
    }
    post(url, JSON.stringify(data)).then(res => {
      console.log(res)
      text.value = res
      load.value = false
    }).catch(error => {
      load.value = false
      console.error('发生错误:', error);
    });
  }
  // 获取提示词
  const handleClick= (row)=>{
    select_row.value = row;
    index.value = row.id
    load.value = true;
    let url = api_url+"/Patient/getPrompt"
    post(url,JSON.stringify({id:row.id,type:btn_type.value})).then(res => {
      load.value = false;
      value.value = res.data
    }).catch(error => {
      load.value = false;
      console.error('发生错误:', error);
    });
  }

  const getlist = ()=>{
    let url = api_url+"/Patient/getList"
    post(url, '').then(res => {
      console.log(6666,res);
      
      let row = res.data[0]
      handleClick(row)
      user_list.value = res.data
    }).catch(error => {
      console.error('发生错误:', error);
    });
  }
  getlist()
</script>

<style>
.el-textarea__inner{
  height: 100%;
}
</style>
<style scoped>
.input{
  height: 80%;
  display: flex;
}
.tableData{
  width: 15%;
}
.input-list{
  width: 100%;
}
.showtext{
  border: 1px solid #000000;
  font-size: 14px;
  padding: 15px;
  white-space: pre-line;
}

</style>