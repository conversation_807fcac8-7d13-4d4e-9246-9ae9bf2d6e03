import CryptoJS from 'crypto-js'

const _key = 'YB19Mjk5hVhFX9Rt';
const key = CryptoJS.enc.Latin1.parse(_key);
const iv = CryptoJS.enc.Latin1.parse(_key);


export default {
	decrypt(word){
		let encryptedHexStr = CryptoJS.enc.Latin1.parse(word);
		let srcs = CryptoJS.enc.Latin1.stringify(encryptedHexStr);
		
		var encrypted = CryptoJS.AES.decrypt(srcs, key, {
		 iv: iv,
		 mode:CryptoJS.mode.CBC,
		 padding:CryptoJS.pad.ZeroPadding
		});
		
		let decryptedStr = encrypted.toString(CryptoJS.enc.Utf8)
		return decryptedStr.toString();
	},
	//加密
	encrypt(word) {
	  var encrypted = CryptoJS.AES.encrypt(word, key, {
	   iv: iv,
	   mode:CryptoJS.mode.CBC,
	   padding:CryptoJS.pad.ZeroPadding
	  });
	  return encrypted.toString();
	 },
	 base64(word){
		return CryptoJS.enc.Base64.parse(word)
	 },
	 decryptBase64(word){
		// 先解析Base64格式的加密数据
		let encryptedData = word;
		
		var decrypted = CryptoJS.AES.decrypt(
			{ ciphertext: encryptedData },
			CryptoJS.enc.Utf8.parse(_key),
			{
				iv: CryptoJS.enc.Utf8.parse(_key),
				mode: CryptoJS.mode.CBC,
				padding: CryptoJS.pad.Pkcs7
			}
		);
		
		let decryptedStr = decrypted.toString(CryptoJS.enc.Utf8);
		return decryptedStr;
	}
}
