import { ElMessage, ElMessageBox, ElLoading } from 'element-plus';

//提示信息
export const message = (message = '提示信息', type = 'info') => {
  // success
  // warning
  // info
  // error
  ElMessage({
    message: message,
    type: type,
  });
};
//确认框
export const confirm = (message = '内容提示', title = '提示') => {
  return new Promise((resolve, reject) => {
    ElMessageBox.confirm(message, title, {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
      .then(() => {
        resolve(true);
      })
      .catch(() => {
        reject(false);
      });
  });
};

export const loading = () => {
  return ElLoading.service();
};
