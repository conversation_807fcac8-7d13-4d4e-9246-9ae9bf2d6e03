import { createApp } from 'vue'
import App from './App.vue'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import zhCn from 'element-plus/es/locale/lang/zh-cn'
import router from '@/router' // 路由管理
import * as ElementPlusIconsVue from '@element-plus/icons-vue';
const app = createApp(App)
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component);
}
app.config.errorHandler = (err, vm, info) => {
  console.log(err)
  console.error('全局错误捕获:', err);
  console.error('错误信息:', info);
};

app.config.warnHandler = (msg, vm, trace) => {
  // console.log(msg)
  // console.warn('全局警告捕获:', msg);
  // console.warn('警告信息:', trace);
}

app.use(ElementPlus, {
  locale: zhCn,
})
app.use(router)
app.mount('#app')
