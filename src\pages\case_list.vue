<template>
  <div style="border-bottom: 1px solid #eeeeee;">
    <el-button v-if="select_row.inpno" @click="getDoctorAdvice">查询医嘱</el-button>
    <el-button v-if="select_row.inpno" @click="getDoctorTest">检验检查</el-button>
  </div>
  <div class="input" v-loading="load">
    <div class="input-list">
      <div class="list">
        <div class="list-item" v-for="(item,index) in listings" :key="index">
          <h4>{{item.document_type}}</h4>
          <div class="text">{{item.document_content}}</div>
        </div>
      </div>
    </div>
    <div class="tableData">
      <div class="input-s">
        <div class="name-label">病区</div>
        <el-select @change="getUser" v-model="ward_id"  placeholder="病区筛选">
          <el-option
               v-for="item in select_list"
              :key="item.id"
              :label="item.item_name"
              :value="item.id"
          >
          </el-option>
        </el-select>
        <el-button type="primary" @click="seeDisease">查看病区病种</el-button>
      </div>
      <el-table :data="user_list" style="width: 100%;height: 800px;">
        <el-table-column label="用户列表">
          <template #header>
            <el-input v-model="search" placeholder="输入名称" />
          </template>
          <template #default="scope">
            <el-button :type="pid===scope.row.pid?'primary':''"  @click="handleClick(scope.row)">{{scope.row.pat_name}}({{scope.row.inpno}})</el-button>
            <div>{{scope.row.disease}}</div>
            <el-button type="primary" @click="getTagList(scope.row)">查看标签</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>

  <div class="tab_list" v-if='select_row.pvid'>
    <el-pagination background :current-page="parseInt(tab_type)" layout="prev, pager, next" @current-change="tabClick" :page-size="1" :total="parseInt(select_row.pvid)" />
  </div>

  <el-dialog
      v-model="show_see"
      title="医嘱信息"
      width="80%"
      draggable
  >
    <div style="width: 100%;height:400px;overflow-y: scroll" v-loading="load">
      <el-table :data="see_list" style="width: 100%;height: 400px">
        <el-table-column type="index" width="50" />
        <el-table-column prop="order_content" label="医嘱内容"
            :filters="[
              { text: '检验', value:'C'},
              { text: '检查', value:'D'},
              { text: '治疗', value:'E'},
              { text: '手术', value:'F'},
              { text: '麻醉', value:'G'},
              { text: '护理', value:'H'},
              { text: '膳食', value:'I'},
              { text: '输血', value:'K'},
              { text: '输氧', value:'L'},
              { text: '材料', value:'M'},
              { text: '其他', value:'Z'},
              { text: '用药', value:'5'},
            ]"
            :filter-method="filterCitemType"
            filter-placement="bottom-end"
        />
        <el-table-column prop="order_freq_name" label="用法" />


        <el-table-column prop="order_once_qunt" label="单次用量">
          <template #default="scope">
            <div v-if="scope.row.order_once_qunt">{{scope.row.order_once_qunt}}  {{scope.row.order_once_qunt_unit}}</div>
          </template>
        </el-table-column>
        <el-table-column prop="order_total_qunt" label="总给予量">
          <template #default="scope">
            <div v-if="scope.row.order_total_qunt">{{scope.row.order_total_qunt}}  {{scope.row.order_once_qunt_unit}}</div>
          </template>
        </el-table-column>
        <el-table-column prop="apply_time" label="开嘱时间" />
        <el-table-column prop="order_exe_time_start" label="开始执行时间"/>
        <el-table-column prop="order_exe_time_end" label="执行终止时间" />
        <el-table-column prop="order_expidate_type" label="医嘱期效"
             :filters="[
              { text: '长嘱', value: 0 },
              { text: '临嘱', value: 1},
            ]"
          :filter-method="filterExpidate"
          filter-placement="bottom-end"
        >
          <template #default="scope">
            <span v-if="scope.row.order_expidate_type==1">临嘱</span>
            <span v-else>长嘱</span>
          </template>
        </el-table-column>

        <el-table-column prop="order_status" label="医嘱状态">
          <template #default="scope">
            <span v-if="scope.row.order_status==-1">未生效的暂存医嘱</span>
            <span v-if="scope.row.order_status==1">新开</span>
            <span v-if="scope.row.order_status==2">校对疑问</span>
            <span v-if="scope.row.order_status==3">已校对</span>
            <span v-if="scope.row.order_status==4">已作废</span>
            <span v-if="scope.row.order_status==5">已重整</span>
            <span v-if="scope.row.order_status==6">已暂停</span>
            <span v-if="scope.row.order_status==7">已启用</span>
            <span v-if="scope.row.order_status==8">已停止</span>
            <span v-if="scope.row.order_status==9">已确认停止</span>
          </template>
        </el-table-column>

      </el-table>
    </div>
  </el-dialog>

  <el-dialog
      v-model="test_show_see"
      title="检查信息"
      width="90%"
      draggable
  >
    <div  style="width: 100%;height:500px;" v-loading="test_load">
      <el-tabs v-model="activeName" class="demo-tabs">
        <el-tab-pane label="检验" name="first">
          <el-table :data="doctorTestList.jy" style="width: 100%;height: 400px">
            <el-table-column prop="order_content" label="检验内容" width="100"  />
            <el-table-column prop="apply_time" label="申请时间"/>
            <el-table-column label="检验结果">
              <template #default="scope">
                  <div  v-for="item in scope.row.result">
                    <span class="title_item">名称：{{item.loitem_cname}}</span>
                    <span class="title_item">指标参考值：{{item.loitem_rv}}</span>
                    <span class="title_item">指标结果值：{{item.order_rpt_result}}</span>
                  </div>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="检查" name="second">
          <el-table :data="doctorTestList.jc" style="width: 100%;height: 400px">
            <el-table-column type="index" width="50" />
            <el-table-column prop="order_content" label="检查内容" />
            <el-table-column prop="apply_time" label="申请时间"/>
            <el-table-column label="检验结果">
              <template #default="scope">
                <div  v-for="item in scope.row.result">
                  <span class="title_item">报告时间：{{item.report_time}}</span>
                  <span class="title_item">检查结果：{{item.order_rpt_result}}</span>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>
    </div>
  </el-dialog>

  <el-dialog
      v-model="show_disease"
      title="病种"
      width="75%"
  >
    <div style="width: 100%;height:400px;overflow-y: scroll">
        患者总人数：{{user_list.length}}人
    </div>
  </el-dialog>

  <el-dialog
      v-model="show_tag"
      title="标签"
      width="75%"
  >
    <div style="width: 100%;height:400px;overflow-y: scroll" v-loading="loads">
      <div v-for="(item,index) in tag_list" :key="index">
          <div style="margin-bottom: 15px;">
            <h3>{{item.document_type}}--{{item.label}}--{{item.update_time}}</h3>
            <el-tag v-for="row in item.content.split(',')">{{row}}</el-tag>
          </div>
      </div>
    </div>
  </el-dialog>

</template>

<script setup>
  import { ref,computed } from 'vue';
  import { post } from '@/utils/request.js'
  import c from "@/utils/config";
  import { message } from '@/utils/message'
  const activeName = ref("first")
  const show_see = ref(false)
  const see_list = ref([])
  const show_disease = ref(false)
  const test_show_see = ref(false)
  const show_tag = ref(false)
  const api_url = c.api_url;
  const filterExpidate = (value,row)=>{

    return row.order_expidate_type == value
  }

  const count = ref(0);




  const load = ref(false)
  const select_list = ref([])
  const ward_id = ref("22")

  const search = ref("")
  const tableData = ref([])
  const listings = ref([])
  const pid = ref()

  const select_row = ref({})
  const tab_type = ref("")
  const test_load = ref(false)

  const disease = ref([])
  const doctorTestList = ref({jc:[],jy:[]})
  const tag_list = ref([])
  const loads = ref(false)

  const getTagList = (row)=>{
    show_tag.value = true
    loads.value = true
         post(`${api_url}/Patient/getLabels`, JSON.stringify({pid:row.pid})).then(res => {
      tag_list.value = res.data
      console.log(tag_list.value)
      loads.value = false
    }).catch(error => {
      loads.value = false
    });
  }

  const filterCitemType = (value,row)=>{
    return row.citem_type === value
  }

  // 检验 检查 报告
  const getDoctorTest = ()=>{
    test_load.value = true;
    test_show_see.value = true;
    doctorTestList.value= {
      jc:[],
      jy:[]
    }
         post(`${api_url}/patient/getYzRs`, JSON.stringify({pid:select_row.value.pid,pvid:tab_type.value})).then(res => {
      let jc = []
      let jy = []
      test_load.value = false;
      res.data.jc.forEach((item)=>{
        let index = jc.findIndex((row)=>row.order_conten===item.order_content)
        if(index===-1){
          jc.push(item)
        }
      })

      res.data.jy.forEach((item)=>{
        let index = jy.findIndex((row)=>row.order_content===item.order_content)
        if(index===-1){
          jy.push(item)
        }
      })
      doctorTestList.value = {
        jc,
        jy
      }
    }).catch(error => {
      test_load.value = false;
      load.value = false
      show_see.value = false;
      console.error('发生错误:', error);
    });
  }
  // 获取医嘱内容
  const getDoctorAdvice = ()=>{
    see_list.value=[]
    show_see.value = true;
    load.value = true
         post(`${api_url}/patient/getYz`, JSON.stringify({pid:select_row.value.pid,pvid:tab_type.value})).then(res => {
      see_list.value = res.map((item)=>{
        let {
          order_content,
          order_drask,
          order_once_qunt,
          order_total_qunt,
          order_once_qunt_unit,
          order_exe_time_start,
          order_exe_time_end,
          order_freq_name,
          conterm_time,
          order_expidate_type,
          citem_type,
          order_status,
          apply_time
        } = item
        return {
          order_freq_name,
          conterm_time,
          order_content,
          order_drask,
          order_once_qunt,
          order_total_qunt,
          order_once_qunt_unit,
          order_exe_time_start,
          order_exe_time_end,
          order_expidate_type,
          citem_type,
          order_status,
          apply_time
        }
      })
      show_see.value = true;
      load.value = false
    }).catch(error => {
      load.value = false
      show_see.value = false;
      console.error('发生错误:', error);
    });
  }

  const user_list = computed(() =>
      tableData.value.filter((data_list) =>
          !search.value ||
          data.pat_name.toLowerCase().includes(search.value.toLowerCase())
      )
  )

  const tabClick = (index)=>{
    console.log(index)
    tab_type.value = index
    getPatientCase(select_row.value.pid,index)
  }

  const autoResize = ()=>{
    let textarea = document.querySelectorAll("textarea")
    textarea.forEach((obj)=>{
      obj.style.height = (obj.scrollHeight + 20) + 'px';
    });
  }

  const seeDisease = ()=>{
    // 精神分裂症
    tableData.value.forEach((row)=>{
      console.log(row)
    })
  }

  const getUser = ()=>{
    console.log({ward_id:ward_id.value})
    load.value = true

    let url = `${api_url}/AiCase/getPatientList`
         post(url, JSON.stringify({ward_id:ward_id.value})).then(res => {
      tableData.value= res.data;
      load.value = false
    }).catch(error => {
      load.value = false
      console.error('发生错误:', error);
    });
  }

  const getWardList = ()=>{
    let url = `${api_url}/AiCase/getWardList`
    load.value = true
         post(url, JSON.stringify({})).then(res => {
      select_list.value = res.data
      getUser()
      load.value = false
    }).catch(error => {
      load.value = false
      console.error('发生错误:', error);
    });
  }
  getWardList()
  const handleClick = (row)=>{
    tab_type.value =parseInt(row.pvid)
    pid.value =  row.pid
    select_row.value = row;
    getPatientCase(row.pid,row.pvid)
  }
  let arr_list = []
  const getPatientCase = (pid,pvid)=>{
    load.value = true
    let url = `${api_url}/AiCase/getPatientCase`
         post(url, JSON.stringify({pid,pvid})).then(res => {
      listings.value = res.data;
      setTimeout(()=>{
        load.value = false
        autoResize()
      },1000)
      res.data.forEach((item)=>{
        if(item.document_type==='日常病程记录'){
          arr_list.push(item.document_content)
        }
      })
      console.log(arr_list)

    }).catch(error => {
      load.value = false
      console.error('发生错误:', error);
    });
  }
</script>

<style scoped>
.title_item{
  margin-right: 15px;
}
.input{
  height:calc(100% - 100px);
  display: flex;
  padding: 15px;
}
.tableData{
  width: 20%;
  border-left: 1px solid dodgerblue;
  padding-left: 10px;
}
.input-list{
  width: 80%;
}
.el-textarea__inner{
  height: 100%;
}
.showtext{
  border: 1px solid #000000;
}
.input-s{
  margin-bottom: 5px;
  display: flex;
  align-items: center;
}
.tab_list{
  /* height: 40px; */
  padding-top: 15px;
}
.name-label{
  width:80px;
}
.list{
  height: 100%;
  overflow-y: auto;
}
.list-item{
  margin-bottom: 15px;

}
h4{
  margin-bottom:10px;
}
.text {
  white-space: pre-line; /* 或者使用 pre-wrap */
  border: 1px solid #eee;
  font-size: 14px;
  padding: 15px;
}
.tab_list{
  position: absolute;
  left: 30%;
  bottom: 40px;
}
</style>