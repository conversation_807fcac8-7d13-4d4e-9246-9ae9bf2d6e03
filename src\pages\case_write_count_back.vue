<template>
    <div class="date-box">
      <div class="filters">
        <div>
          <el-date-picker
              v-model="date_value"
              type="date"
              placeholder="书写日期"
              @change="selectDate"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              size="default"
          />
        </div>
        <div style="width: 200px;margin-left: 15px">
          <el-select
              v-model="sellect_type"
              placeholder="选择类型"
              @change="selectType"
          >
            <el-option
                v-for="item in type_list"
                :key="item"
                :label="item"
                :value="item"
            />
          </el-select>
        </div>
        <div>
          <el-button v-if="date_value===date_v" style="margin-left: 10px" @click="writeContent" type="primary">一键上传his</el-button>
          <el-button type="primary" @click="batch_case" v-if="date_value===date_v">批量生成病例</el-button>
          <el-button type="text" @click="see_new">更新记录</el-button>

          <el-button type="warning">未执行：{{no_num - info_num}}</el-button>
          <el-button type="success">执行完成：{{success_num}}</el-button>
          <el-button type="danger">执行失败：{{error_num}}</el-button>
          <el-button type="info" @click="refresh">刷新</el-button>

          <el-button type="info" @click="statisticPage">统计</el-button>

        </div>
      </div>
    </div>

    <el-table v-loading="load" :data="tableData" height="800px" border style="width: 100%">
      <el-table-column type="index" width="50" />
      <el-table-column prop="patient_name" label="患者名称" width="120"
         :filters="[
            { text: '已修改', value:2 },
            { text: '未修改', value:1 },
          ]"
         :filter-method="filterModify"
         filter-placement="bottom-end"
      >
        <template #default="scope">
          <el-button type="primary" @click="see(scope.row)" text>{{scope.row.patient_name}}</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="text1" label="AI书写">
        <template #default="scope">
          <div v-html="scope.row.ai_case_text"></div>
        </template>
      </el-table-column>
      <el-table-column prop="text1" label="内部修改">
        <template #default="scope">
          <div>
<!--            <div v-html="scope.row.ai_case_edit_text"></div>-->
            <div contenteditable="true" v-if="scope.row.ai_case_edit" :id="'edit_text_'+scope.row.or_id" class="edit-text">{{scope.row.ai_case_edit}}</div>
            <el-button size="default" v-if="scope.row.ai_case_edit" @click="saveEdit(scope.row,'case')">保存编辑</el-button>
          </div>

<!--          <el-button size="primary" v-if="scope.row.ai_case_edit_text" @click="saveEdit(scope.row,'case')">对比</el-button>-->

        </template>
      </el-table-column>
      <el-table-column prop="text2" label="医院生(红色代表：删除，绿色：添加)">
        <template #default="scope">
          <div :id="`doctor_case${scope.row.or_id}`" v-html="scope.row.doctor_case_text"></div>
        </template>
      </el-table-column>

      <el-table-column label="错误原因">
        <template #default="scope">
          <el-input type="text" @keydown.enter="saveMark(scope.row)" v-model="scope.row.mark"></el-input>
        </template>
      </el-table-column>

<!--      <el-table-column label="确认状态" width="120px">-->
<!--        <template #default="scope">-->
<!--          {{scope.row.doctor_signature}}-->
<!--        </template>-->
<!--      </el-table-column>-->

      <el-table-column label="上传状态" width="100px">
        <template #default="scope">
          <span v-if="scope.row.ai_case_status===0">未执行</span>
          <span class="success"  v-if="scope.row.ai_case_status===2">完成</span>
          <span class="error" v-if="scope.row.ai_case_status===3">失败</span>
        </template>
      </el-table-column>


      <el-table-column label="病例时间" width="100px">
        <template #default="scope">
          <span>{{scope.row.docment_datetime}}</span>
        </template>
      </el-table-column>

      <el-table-column label="书写时间" width="100px">
        <template #default="scope">
          <span>{{scope.row.edit_time}}</span>
        </template>
      </el-table-column>
      <el-table-column label="病种" width="100px">
        <template #default="scope">
            {{scope.row.disease}}
        </template>
      </el-table-column>

      <el-table-column
          label="医生"
          width="80px"
          prop="doctor_name"
          :filters="[
            { text: '罗叶', value: '罗叶' },
            { text: '张艳', value: '张艳' },
            { text: '何静', value: '何静' },
            { text: '任凤', value: '任凤' },
          ]"
         :filter-method="filterTag"
         filter-placement="bottom-end"
      >
        <template #default="scope">
          {{scope.row.doctor_name}}
        </template>
      </el-table-column>
      <el-table-column label="是否查阅" width="100px">
        <template #default="scope">
          <span v-if="scope.row.doctor_sign">已查阅</span>
          <span v-else>未查阅</span>
        </template>
      </el-table-column>
      <el-table-column label="书写" width="200px">
        <template #default="scope">
<!--          <el-button type="info" @click="seeAdvice(scope.row.advice_list)" size="small" v-if="scope.row.advice_list.length>0">异常</el-button>-->
<!--          <el-button type="danger" @click="clearAdvice(scope.row)" size="small" v-if="scope.row.advice_list.length>0">清除任务</el-button>-->
          <el-button type="primary" v-if="date_value===date_v&&scope.row.advice_list.length===0" size="small" @click="write(scope.row)">生成病例</el-button>
          <el-button type="warning" v-if="scope.row.advice_list.length===0" size="small" @click="reverse_write(scope.row)">反向对比</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog
        v-model="dialogVisible"
        title="病例记录"
        width="80%"
    >
      <template #default>
        <div class="content" v-loading="load">
          <div class="list-item" v-for="(item,index) in listings" :key="index">
            <h4>{{item.document_type}}</h4>
            <div class="text">{{item.document_content}}</div>
          </div>
        </div>
        <div>
          <el-pagination background :current-page="page" layout="prev, pager, next" @current-change="tabClick" :page-size="1" :total="parseInt(select_row.pvid)" />
        </div>
      </template>
    </el-dialog>

    <el-dialog
        v-model="show_see"
        title="Tips"
        width="80%"
    >
      <el-table :data="see_list" style="width: 100%">
        <el-table-column prop="order_content" label="内容" />
        <el-table-column prop="order_exe_time_start" label="开始时间"/>
        <el-table-column prop="order_exe_time_end" label="结束时间" />
      </el-table>
    </el-dialog>

    <el-dialog
        v-model="show_count_see"
        :title="`统计日期${date_value}`"
        width="80%"
    >
      <div>
        <table style="width: 100%;border: 1px solid #000000;">
          <tr class="tr" style="border: 1px solid #000000">
            <th>医生名称</th>
            <th>书写患者人数</th>
            <th>不修改人数</th>
            <th>修改人数</th>
            <th>10字以内</th>
            <th>10-50字</th>
            <th>50-100字</th>
            <th>100字以上</th>
            <th>添加字数</th>
            <th>删除字数</th>
            <th>移动字数</th>
            <th>总字数</th>
            <th>修改率</th>
          </tr>
          <tr class="tr" style="text-align: center;margin-bottom: 15px" v-for="(item,index) in count_list" :key="index">
            <td>{{index}}</td>
            <td>{{item.num}}人</td>
            <td>{{item.no_modify}}人</td>
            <td>{{item.no_modify>0?item.num-item.no_modify:0}}人</td>
            <td>{{item.modify_10}}人</td>
            <td>{{item.modify_10_50}}人</td>
            <td>{{item.modify_50_100}}人</td>
            <td>{{item.modify_100}}人</td>
            <td style="color: #67c23a;font-weight: bold;">{{item.count_add_num}}</td>
            <td style="color: red;font-weight: bold;">{{item.count_del_num}}</td>
            <td style="color: red;font-weight: bold;">{{item.move_num}}</td>
            <td style="color: #409eff;font-weight: bold;">{{item.count_text_mum}}</td>

            <td style="color: #409eff;font-weight: bold;">{{(((item.count_del_num+item.count_add_num)/item.str_ai_doctor_num)*100).toFixed(2)}}%</td>
          </tr>
          <tr class="tr trbg" style="text-align: center;font-weight: bold;border-top: 2px solid green">
            <td>统计</td>
            <td>{{count_num.user_num}}人</td>
            <td>{{count_num.no_modify}}人</td>
            <td>{{count_num.no_modify>0?count_num.user_num - count_num.no_modify:0}}人</td>

            <td>{{count_num.modify_10}}人</td>
            <td>{{count_num.modify_10_50}}人</td>
            <td>{{count_num.modify_50_100}}人</td>
            <td>{{count_num.modify_100}}人</td>

            <td>{{count_num.count_add_num}}</td>
            <td>{{count_num.count_del_num}}</td>
            <td>{{count_num.count_move_num}}</td>
            <td>{{count_num.count_text_mum}}</td>
            <td>{{count_num.rate}}%</td>
          </tr>
        </table>
      </div>
    </el-dialog>

</template>

<script setup>
  import {ref} from 'vue';
  import {diffWords } from 'diff';
  import { post } from '@/utils/request.js'
  import c from "@/utils/config";
  import { message, confirm } from '@/utils/message'
import { baseUrl } from '../utils/baseUrl';
  let today = new Date()
  const year = today.getFullYear();
  const month = today.getMonth() + 1; // 月份从 0 开始，需要加 1
  const day = today.getDate();
  const show_see = ref(false)
  const see_list = ref([])

  const date_v = `${year}-${month<10?'0'+month:month}-${day<10?'0'+day:day}`

  const date_value = ref(date_v)

  const load = ref(false)
  const dialogVisible = ref(false)
  const listings = ref([])
  const page = ref(1)
  const select_row = ref({})
  const tableData = ref([])
  const select_value = ref("")
  const success_num = ref(0)
  const error_num = ref(0)
  const no_num = ref(0)
  const info_num = ref(0)
  const generate_num = ref(0)

  const doctor_list = ref({});
  const count_list = ref([])
  const show_count_see = ref(false);

  const count_num = ref({})
  const sellect_type = ref("")

  const isBtn = ref(false);
  if(localStorage.getItem("batch_case")){
    isBtn.value = true;
  }
  //1.rpa 执行任务日常病例记录
  //2.提示词9个类型（完成）
  //3.我们自

  const selectType = ()=>{
    console.log(sellect_type.value)
    getList()
  }


  const filterModify = (value, row)=>{
    return row.is_yes_modify === value
  }
  const filterTag = (value, row) => {
    return row.doctor_name === value
  }

  const refresh = ()=>{
      getList()
  }

  const type_list=ref([
    "入院记录(合作)",
    "入院记录(不合作)",
    "首次病程记录",
    "上级医师查房记录",
    "日常病程记录",
    "阶段小结",
    "交班记录",
    "接班记录",
    "出院记录",
  ])

  //统计数据
  const extractTimeFromChineseDate = (str)=>{
    let regex = /(\d{4})年(\d{2})月(\d{2})日(\d{2})时(\d{2})分/; // 匹配 YYYY年MM月DD日HH时MM分 格式
    let match = str.match(regex);
    if (match) {
      let year = match[1];
      let month = match[2];
      let day = match[3];
      let hour = match[4];
      let minute = match[5];
      return `${year}年${month}月${day}日 ${hour}时${minute}`;
    } else {
      return null;
    }
  }

  const removeDateTimeFromString = (str)=>{
    // 正则表达式匹配日期时间格式，例如 "YYYY-MM-DD HH:MM:SS"
    return str.replace(/\d{4}年\d{1,2}月\d{1,2}日\d{1,2}时\d{1,2}分/g, '').trim();
  }
  const statisticPage = ()=>{

    tableData.value = tableData.value.map((item)=>{
      item.del_num = 0;
      item.add_num = 0;
      item.text_num = 0;
      item.no_modify = 0;
      item.move_num = 0;
      item.str_ai_doctor_num = 0;
      item.modify_10 = 0;
      item.modify_10_50 = 0;
      item.modify_50_100 = 0;
      item.modify_100 = 0;
      return item;
    })

    let count_del_num = 0;
    let count_add_num = 0;
    let count_text_mum = 0;
    let user_num = 0;
    let no_modify = 0;
    let rate = 0;
    let count_move_num = 0;

    let modify_10=0;
    let modify_10_50=0;
    let modify_50_100 = 0;
    let modify_100 = 0;

    let arr = {}
    let list = tableData.value.filter((row)=>row.doctor_case_text).map((item)=>{
       let doc = document.getElementById(`doctor_case${item.or_id}`)
       let set2 = item.doctor_case
      if(doc){
         let add = doc.querySelectorAll(".diff-added")
         let del = doc.querySelectorAll(".diff-removed")

         let add_str_arr = []
         let del_str_arr = []
         let add_str = []
         let del_str = []

         item.text_num = item.ai_case_edit.length;

         del.forEach((obj)=>{
           // item.del_num+=obj.innerText.length
           del_str_arr.push(obj.innerText)
         })

         add.forEach((obj,i)=>{
           if(i>0&&i<(add.length-1)){
             // item.add_num+=obj.innerText.length
             add_str_arr.push(obj.innerText)
           }
         })

         add_str = add_str_arr.filter((row)=>{
           return del_str_arr.indexOf(row)===-1
         })

          del_str = del_str_arr.filter((row)=>{
            return add_str_arr.indexOf(row)===-1
          })

          add_str = add_str.join("").replace(/\s/g, '').replace(/[，。！？、\s]/g, '')
          del_str = del_str.join("").replace(/\s/g, '').replace(/[，。！？、\s]/g, '')

         item.add_num+=add_str.length
         item.del_num+=del_str.length

         let move_str= add_str_arr.filter(value => del_str_arr.includes(value));
         item.move_num = move_str.join("").replace(/\s/g, '').length

         if(item.ai_case_edit===set2[0].replace(/\s/g, '')){
           if(set2[0]){
             item.no_modify+=1
             no_modify+=1
           }
         }else{
           if(set2[0]){
             let len = item.add_num+item.del_num
             if(len!==0&&len<10){
               item.modify_10+=1
               modify_10+=1;
             }else if(len>10&&len<50){
               item.modify_10_50+=1
               modify_10_50+=1;
             }else if(len>50&&len<100){
               item.modify_50_100+=1
               modify_50_100+=1;
             }else if(len>100){
               item.modify_100+=1;
               modify_100+=1;
             }
           }
         }

         item.str_ai_doctor_num = item.ai_case_edit.length+set2[0].replace(/\s/g, '').length

         count_del_num+=item.del_num;
         count_add_num+=item.add_num;
         count_text_mum+=item.text_num
         user_num+=1
         count_move_num+=item.move_num

         // console.log(set2)
         if(arr[item.doctor_name]){
           let r = arr[item.doctor_name]
           arr[item.doctor_name] = {
             num:r.num+=1,
             count_del_num:r.count_del_num+item.del_num,
             count_add_num:r.count_add_num+item.add_num,
             count_text_mum:r.count_text_mum+item.text_num,
             no_modify:r.no_modify+item.no_modify,
             str_ai_doctor_num:r.str_ai_doctor_num+item.str_ai_doctor_num,
             move_num:r.move_num+item.move_num,
             modify_10:item.modify_10+r.modify_10,
             modify_10_50:item.modify_10_50+r.modify_10_50,
             modify_50_100:item.modify_50_100+r.modify_50_100,
             modify_100:item.modify_100+r.modify_100,
           }
         }else{
           arr[item.doctor_name] = {
             num:1,
             count_del_num:item.del_num,
             count_add_num:item.add_num,
             count_text_mum:item.text_num,
             no_modify:item.no_modify,
             str_ai_doctor_num:item.str_ai_doctor_num,
             move_num:item.move_num,
             modify_10:item.modify_10,
             modify_10_50:item.modify_10_50,
             modify_50_100:item.modify_50_100,
             modify_100:item.modify_100,
           }
         }
       }
      return item
    })

    count_list.value = arr;
    for (let key in arr){
      let item = arr[key]
      rate+=(item.num/user_num)*((item.count_del_num+item.count_add_num)/item.str_ai_doctor_num)
    }

    count_num.value = {
      count_del_num,
      count_add_num,
      count_text_mum,
      user_num,
      no_modify,
      count_move_num,
      rate:(rate*100).toFixed(2),
      modify_10,
      modify_10_50,
      modify_50_100,
      modify_100
    }

    show_count_see.value = true
  }

  // 删除上传病例
  const clearAdvice = (row)=>{
    let url = "/patient/deleteAiCase"
    load.value = true
    post(url, JSON.stringify({
      ai_case_id:row.ai_case_id
    })).then(res => {
      let data_list = res.data;
      load.value = false
      getList()
    }).catch(error => {
      load.value = false
      console.error('发生错误:', error);
    });
  }
  // 批量生成病例

  const batch_case = ()=>{
    localStorage.setItem("batch_case",true)
    let url = `${baseUrl.aip_python_url}/batchCreateMedical`

    let list = tableData.value.filter((item)=>item.advice_list.length===0).filter((item)=>item.ai_case==="").map((item)=>{
      return {
        id:item.or_id,
        last_time:item.last_time
      }
    })

    load.value = true
    post(url, JSON.stringify({
      list
    })).then(res => {
      let data_list = res.data;
      load.value = false
    }).catch(error => {
      load.value = false
      console.error('发生错误:', error);
    });
  }
  // 保存备注
  const saveMark = (row)=>{
    let url = `${baseUrl.hos_zhixuee}/patient/updateAiCase`
    load.value = true
    post(url, JSON.stringify({
      ai_case_id:row.ai_case_id,
      mark:row.mark
    })).then(res => {
      let data_list = res.data;
      load.value = false
    }).catch(error => {
      load.value = false
      console.error('发生错误:', error);
    });

  }
  // 保存编辑
  const saveEdit = (row,type)=>{
     load.value = true;
     let text = document.getElementById('edit_text_'+row.or_id)
    let url = `${baseUrl.hos_zhixuee}/patient/updateAiCase`
    load.value = true
    post(url, JSON.stringify({
      ai_case_id:row.ai_case_id,
      document:text.innerText
    })).then(res => {
      let data_list = res.data;
      load.value = false
    }).catch(error => {
      load.value = false
      console.error('发生错误:', error);
    });
  }

  const seeAdvice = (list)=>{
    show_see.value = true
    see_list.value = list
  }
  // 更新病例
  const see_new = ()=>{
    let url = `${baseUrl.hos_zhixuee}/patient/updateCase`
    load.value = true
    post(url, JSON.stringify({})).then(res => {
      let data_list = res.data;
      load.value = false
      getList()
    }).catch(error => {
      load.value = false
      console.error('发生错误:', error);
    });
  }
  //反向对比
  const reverse_write = (row)=>{
    row.isReverse=!row.isReverse
    let str1 = row.ai_case_edit;
    let str2 = row.doctor_case;
    if(str2){
      let arr_s = row.doctor_case.split("【主要健康问题】主要健康问题")
      str2 = arr_s[1].split("医师签名")[0]
    }
    if(str1&&str2){
      let {result1,result2} = markDifferences(str1,str2)
      if(row.isReverse){
        row.ai_case_text =result1;
        row.doctor_case_text = str2
      }else{
        row.ai_case_text =str1;
        row.doctor_case_text = result2
      }
    }
  }
  // 分页查询
  const tabClick = (index)=>{
    page.value = index
    getPatientCase(select_row.value.or_id,index)
  }
  // 查看病例
  const see = (row)=>{
    select_row.value = row;
    page.value = row.pvid;
    getPatientCase(row.or_id,row.pvid)
  }
  // 获取病例内容
  const getPatientCase = (pid,pvid)=>{
    load.value = true
    let url = `${api_url}/AiCase/getPatientCase`
    post(url, JSON.stringify({pid,pvid})).then(res => {
      listings.value = res.data;
      dialogVisible.value = true
      load.value = false
    }).catch(error => {
      load.value = false
      console.error('发生错误:', error);
    });
  }
  // 一键书写
  const writeContent = ()=>{
    load.value = true
    let url = `${api_url}/patient/startExe`
    post(url, JSON.stringify({})).then(res => {
      load.value = false
    }).catch(error => {
      load.value = false
      console.error('发生错误:', error);
    });
  }
  // 书写
  const write = (row)=>{
    let url = `${api_url}/patient/createRpaTask`
    load.value = true
    post(url, JSON.stringify({
      id:row.or_id,
      last_time:row.last_time
    })).then(res => {
      load.value = false
      row.ai_case = res.data
      row.ai_case_text = res.data
      row.ai_case_edit = res.data
    }).catch(error => {
      load.value = false
      console.error('发生错误:', error);
    });
  }
  const selectDate = (value)=>{
    getList()
  }
  // 获取列表
  const getList = ()=>{
    load.value = true
    let url = `${api_url}/patient/getListByWrite`
    post(url, JSON.stringify({
      date:`${date_value.value} 23:59:59`,
      type:sellect_type.value
    })).then(res => {

      load.value = false
      no_num.value= 0;
      success_num.value=0;
      error_num.value=0;
      info_num.value=0;
      doctor_list.value = {}
      generate_num.value = 0;



      tableData.value = res.data.filter((row)=>row.advice_list.length===0).map((item)=>{
        let datetime = extractTimeFromChineseDate(item.doctor_case)
        let set2 = removeDateTimeFromString(item.doctor_case).split("医师签名")

        item.docment_datetime = datetime
        item.doctor_case = set2[0];
        let str1 = item.ai_case_edit;
        let str2 = item.doctor_case;
        let str3 = item.ai_case;

        item.del_num = 0;
        item.add_num = 0;
        item.text_num = 0;
        item.no_modify = 0;
        item.move_num = 0;
        item.str_ai_doctor_num = 0;
        item.modify_10 = 0;
        item.modify_10_50 = 0;
        item.modify_50_100 = 0;
        item.modify_100 = 0;
        item.is_yes_modify = 1;

        if(item.ai_case_edit!==item.doctor_case.replace(/\s+/g, '')&&item.doctor_case){
          item.is_yes_modify = 2
        }

        if(item.ai_case){
          generate_num.value+=1
        }
        if(item.ai_case_status===0){
          no_num.value+=1
        }
        if(item.ai_case_status===2){
          success_num.value+=1
        }
        if(item.ai_case_status===3){
          error_num.value+=1
        }

        if(item.advice_list.length===0&&item.ai_case_status===2){
          if(doctor_list.value[item.doctor_name]){
            doctor_list.value[item.doctor_name] =  doctor_list.value[item.doctor_name]+","+item.patient_name
          }else{
            doctor_list.value[item.doctor_name] =  item.patient_name
          }
        }

        if(item.advice_list.length>0){
          info_num.value+=1
        }

        if(str1&&str2){
          let {result1,result2} = markDifferences(str1,str2)
          str2 = result2
        }
        if(str3&&str1){
          let {result1,result2} = markDifferences(str3,str1)
          str3 = result2
        }
        item.ai_case_text = str1;
        item.doctor_case_text = str2;
        item.ai_case_edit_text = str3;
        return item;
      })
      localStorage.setItem('tableList',JSON.stringify(tableData.value))
    }).catch(error => {
      load.value = false
      console.error('发生错误:', error);
    });
  }
  getList()
  const markDifferences = (str1, str2)=>{
    const diffResult = diffWords(str1, str2)
    const result1 = diffResult.map(part => {
      if (part.added) {
        return `<span class="diff-added">${part.value}</span>`;
      } else if (part.removed) {
        return `<span class="diff-removed">${part.value}</span>`;
      } else {
        return part.value;
      }
    }).join('');

    const result2 = diffResult.map(part => {
      if (part.added) {
        return `<span class="diff-added">${part.value}</span>`;
      } else if (part.removed) {
        return `<span class="diff-removed">${part.value}</span>`;
      } else {
        return part.value;
      }
    }).join('');

    return { result1, result2 };
  }

</script>

<style>

.trbg{
  background: yellow;
}
.tr td,.tr th{
  border: 1px solid #000000;
  padding: 5px;
}

.success{
  color: #67c23a;
}
.error{
  color: red;
}
.filters{
  display: flex;
}
.text {
  white-space: pre-line; /* 或者使用 pre-wrap */
  border: 1px solid #eee;
  font-size: 14px;
  padding: 15px;
}
.content{
  height: 500px;
  overflow-y: auto;
}
  .date-box{
    padding: 15px;
  }
  .diff-added{
    color: green;
  }
  .diff-removed{
    color: red;
    text-decoration: line-through; /* 设置删除线 */
  }

</style>