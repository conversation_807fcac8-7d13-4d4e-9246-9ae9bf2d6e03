<template>
  <div class="medical-record-select">
    <!-- 左侧筛选区域 -->
    <div class="filter-section" v-loading="load">
      <div class="filter-item">
        <div class="filter-title">选择病人（{{user_list?.length}}人）</div>
        <div>
          <el-input style="display: none;"></el-input>
          <el-select
              v-model="selectedDepartment"
              @change="getUser"
              placeholder="筛选病区"
              class="ward-select"
              :disabled="isDisabled"
          >
              <el-option
                  :label="item.item_name"
                  :value="item.id"
                  v-for="item in select_list"
                  :key="item.id"
              />
          </el-select>
        </div>
      </div>

      <div class="filter-item">
        <div class="filter-title">选择病历内容</div>
          <el-select v-model="select_value" class="m-2" placeholder="请选择病例类型">
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        <!-- <el-cascader
            :disabled="isDisabled"
            ref="_refCascader"
            placeholder="选择病例内容"
            collapse-tags
            style="width: 300px"
            :options="options"
            :props="props"
            clearable
            @change="selectCascader"
        /> -->
      </div>
      <div class="filter-item">
         <div class="filter-title">选择病例生成时间</div>
          <el-date-picker
            v-model="last_time"
            type="datetime"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="选择时间"
        /> 
      </div>

      <div class="filter-item">
        <div class="filter-title">语音内容</div>
        <!-- <div class="voice-hint">
          <el-tag type="warning">根据病历类型所选语音需要显示</el-tag>
        </div> -->
        <el-input
            type="textarea"
            :rows="6"
            placeholder="请输入"
            v-model="voiceContent"
        />
      </div>
      <el-button type="primary" v-loading="isDisabled" class="generate-btn" @click="handleGenerate">
        开始生成({{count}}/{{user_list?.length}})
      </el-button>
    </div>
    <!-- 右侧内容展示区域 -->
<!--    <div class="isDisabled" v-loading="isDisabled">生成中({{count}}/{{user_list?.length}})</div>-->
    <div class="content-section" >
      <el-checkbox v-if="user_list?.length>0" :disabled="isDisabled" @change="selectAllArr" v-model="isSelectAll" label="全选" size="large" />
      <div v-loading="row.btn_load" class="item" v-for="row in user_list" :key="row.pid" >
        <h5 class="title title-1">
          <el-checkbox :disabled="isDisabled" @change="rowSelect" v-model="row.isSelect" :label="row.pat_name+'('+row.pvid+'次)'" size="large" />
          <!-- <span>入院记录</span> -->
          <el-tag style="cursor: pointer" @click="showDataList(row)">数据源</el-tag>
        </h5>
        <div v-if="(typeof row.value==='string')">
          {{ row.value }}
        </div>
        <div v-else>
          <div class="item-type-list" v-for="(item,key) in row.value">
          <h4 style="border-bottom: 1px solid grey;height: 30px;line-height: 30px">
            <span>{{key}}</span>
          </h4>
          <div class="content-title">
            <div class="item-content">
              {{ item }}
            </div>
          </div> 
        </div>
        </div>
      </div>
    </div>

  </div>

  <el-dialog
      v-model="dialogVisible"
      title="详情"
      width="80%"
  >
    <div style="height: 600px;overflow-y: scroll">
      <el-timeline>
        <el-timeline-item placement="top" v-for="(activity, index) in activities">
          <el-card>
            <h4>{{index}}提示词</h4>
            <div>
              <p class="text">
                {{activity}}
              </p>
            </div>
          </el-card>
          <div style="height: 40px;"></div>
        </el-timeline-item>
      </el-timeline>
    </div>
  </el-dialog>

  <el-dialog
      v-model="dialogVisibles"
      title="详情"
      width="80%"
  >
    <div style="height: 600px;overflow-y: scroll">
      <el-timeline>
        <el-timeline-item placement="top">
          <el-card>
            <h4>生成内容</h4><br>
            <span style="cursor: pointer;" class="delColor" :class="'select'+item.id" @click="selectText(item.id)" v-for="item in activities.prompt.tags_list['dem_arr']['text']">
              {{item.name}},
            </span>
          </el-card>
          <div style="height: 40px;"></div>
        </el-timeline-item>
        <el-timeline-item placement="top">
          <el-card>
            <h4>模版内容</h4><br>
            <span style="cursor: pointer;" class="delColor" :class="'select'+item.id" @click="selectText(item.id)" v-for="item in activities.prompt.tags_list['tem_arr']['text']">
              {{item.name}},
            </span>
          </el-card>
          <div style="height: 40px;"></div>
        </el-timeline-item>
        <el-timeline-item placement="top">
          <el-card>
            <h4>标签</h4><br>
            <div style="cursor: pointer;" class="delColor" :class="'select'+item.id" @click="selectText(item.id)" v-for="item in activities.prompt.tags_list['tag']['text']">
              {{item.name}}
            </div>
          </el-card>
          <div style="height: 40px;"></div>
        </el-timeline-item>
      </el-timeline>
    </div>
  </el-dialog>


</template>

<script setup>
import { ref } from 'vue'
import { typeOptions } from '@/utils/typeOptions'
import { confirm,message } from '@/utils/message'
import { post } from '@/utils/request.js'
import c from "@/utils/config";
import { baseUrl } from '../utils/baseUrl';
const api_url = c.api_url;
const load = ref(false)
const dialogVisibles = ref(false)
const selectedContent = ref('')
const voiceContent = ref(`.哎，你好，就是你是患者的家属，还是今天送过来的话啊，主要是因为什么原因呃，那个我我家里面的那块就是我女儿嗯啊不听招呼啊，真的不听啊，那经常就是打东西砸东西，然后到处跑，又不听话。Okay.呃，最早出现异常的时候是什么时候呃，起步太好清楚了，大概呃4年前的样子嘛，就是开始了，就有这个情况。当时的话有没有什么刺激啊，就是你们当时有没有觉得有什么原因引发他这个就是出现这些异常。Yeah.当时异常异常情况，我觉得可能是那去打工，那打工的过程中，那老板儿不给我发工资，结果那那就那就就就就有这个情况了。具体表现是什么样的，具体表现，那就说他老板儿想整了，想把他真实，然后然后就不正常了噻，就是这种，然后你们家有没有去核实一下，他说的这个情况是不是真实的，我们又认不带到老板儿，但是我们觉得人家看能能够。那么大一个厂子，那肯定不可能说有这种这种问题噻，所以说我们就没有过头，就是没有去核实，然后只是听他自己说是吧啊啊没有没有没有没有去核实过。然后他具体回来的时候有没有就是有没有伤害自己啊或者伤害家人或者是悔物啊这些行为。有啊，他经常就是把屋头的电视机呃在来就打那个电视机还打烂了两三台，呃，要不就是他就躺到躺呃躺到那个厕所里面，就开始砸了个花洒，把那个花勺弄弄弄烂。你们有没有问问他为什么要砸电视，或去要去砸花洒。嗯.。嗯，这块问了老不说问了老不说有没有他有没有跟你说过，他就是有听到什么声音或者看到什么人。但是你们实际去核实这个情况是没有的，有没有这种情况，有啊有啊，那进来就一直说那老板在后头跟他了追，然后就是那老板一直在监控呢，就是就是就相当于是我们几看有没得这个情况呢？监控具体是他有没有跟你说是什么方式，有啊，他说监控就是。那走到哪儿，那老板就跟到哪儿。呃，然后近期的话就是中途有没有去治疗过？中途有两年前待那个泸州精神病医院治疗过呃，用用过什么药，病情有没有好转，就是吃梨陪同嘛，还有吃那个熬打病嘛，有没有什么好转，或者是病情这些没得啥子效果的，然后出院之后就是治疗过程没有什么好转，出院之后有没有继续服药。Yeah.嗯，带吃，但说有时候不太好爱吃呢，我就鼓捣吃又吃不进去，真的耐耐火的很。然后就是发病了之后就没有就没有去上班，有没有上学这些是吧，都在家里面嘛。哎，对对对对，基本上都在屋里头，就是生活方面能够自理嘛，肯定不能自理噻。Yeah.然后现在的话是近期有加重吗？送过来的话，他近期不是说好加重跟一前差求不多，但是呢就是不停哦，就是不停，没人管了的。就是现在跟这种跟前差不多，然后觉得家属不好管，然后就送过来是吧？`)
const selectedDepartment = ref('')
const select_list = ref([])
const props = { multiple: true }
const options = ref(typeOptions.options)
const select_value = ref("")
const user_list = ref([])
const _refCascader = ref(null)
const dialogVisible = ref(false)
const nodeList = ref([])
const isDisabled = ref(false)
const is_audio = ['入院记录(合作)','入院记录(不合作)','日常病程记录(异常)','入院记录(复发)']
let is_data = ['日常病程记录','日常病程记录(异常)','上级医师查房记录','出院查房记录']
const activities = ref([])
const count = ref(0)
const res_count = ref(0)
const isSelectAll = ref(true)
const last_time = ref("")
const tag_list = ref()

const get_tag = ref([])

function getRandomColorRGB() {
  var r = Math.floor(Math.random() * 256);
  var g = Math.floor(Math.random() * 256);
  var b = Math.floor(Math.random() * 256);
  return 'rgb(' + r + ', ' + g + ', ' + b + ')';
}

const selectText = (id)=>{
  if(!id) return
  let span = document.querySelectorAll('.select'+id)
  let color = getRandomColorRGB()
  let delColor = document.querySelectorAll('.delColor')
  delColor.forEach((obj)=>{
    obj.style.color="";
  })

  span.forEach((obj)=>{
    obj.style.color=color;
  })
}

const getTagList = ()=>{
    post(`${api_url}/tag/getCategoryTree`, JSON.stringify({})).then(res => {
      get_tag.value = res.data;
    }).catch(error => {

    });
}

getTagList()


// last_time
const llm_test = ()=>{
  let url = `${baseUrl.url4}/center/llm`
  let data = {
    messages:[
      {
        role:"user",
        content:"1+1"
      }
    ]
  }

  post(url, JSON.stringify(data)).then(data => {
    console.log(data)
  }).catch(error => {

  });
}

// if(localStorage.getItem('user_list')){
//   user_list.value = JSON.parse(JSON.stringify(user_list.value))
// }

const rowSelect = ()=>{
  let arr = user_list.value.filter((row)=>row.isSelect)
  if(arr?.length>0&&arr?.length===user_list.value?.length){
    isSelectAll.value = true
  }else{
    isSelectAll.value = false
  }
}
const selectAllArr = ()=>{
  user_list.value = user_list.value.map((item)=>{
    item.isSelect = isSelectAll.value;
    return item
  })
}

const get_info_arr = (arr)=>{
  let info = {}
  get_tag.value.forEach((item)=>{
    item.children.forEach((items)=>{
      items.tags.forEach((itemsss)=>{
        if(arr.indexOf(itemsss.name)!==-1){
          if(info[item.id]&&info[item.id].indexOf(itemsss.name)==-1){
            info[item.id].push(itemsss.name)
          }else{
            info[item.id] = [itemsss.name]
          }
        }
      })
    })
  })
  return info;
}

const getNewIdArr = (tag_e,dem_arr)=>{
  let arrss = [];
  let info = []
  for(let key in tag_e){
    info.push({name:tag_e[key][0],id:key})
  }
  dem_arr.forEach((row)=>{
    let index = info.findIndex((item)=>row===item.name)
    if(index===-1){
      arrss.push({name:row,id:""})
    }else{
      arrss.push({name:row,id:info[index]['id']})
    }
  })
  return arrss;
}


const showDataList = (row)=>{

  if(row.prompt.tags){
    let arr = []
    for(let key in row.prompt.tags){
      arr.push(`${row.prompt.tags[key]['name']}`)
    }
    row.prompt.tags = arr

    let dem_arr = row.prompt.document.replace(/。/g, "，").split("，")
    let tem_arr = row.prompt.template.replace(/。/g, "，").split("，")
    dem_arr= dem_arr.filter((item)=>item)
    tem_arr= tem_arr.filter((item)=>item)

    let tag_e = get_info_arr(dem_arr)
    let tag_t = get_info_arr(tem_arr)
    let tag_s = get_info_arr(arr)


    dem_arr = getNewIdArr(tag_e,dem_arr)
    tem_arr = getNewIdArr(tag_t,tem_arr)

    arr = getNewIdArr(tag_s,arr)


    console.log(arr)

    row.prompt.tags_list = {
        "dem_arr":{
          'text':dem_arr,
          'tag_e':tag_e
        },
        "tem_arr":{
          'text':tem_arr,
          'tag_e':tag_t
        },
       "tag":{
         'text':arr,
         'tag_e':tag_s
       }
    }

  }
  if(select_value.value==9){
    dialogVisibles.value = true
  }else{
    dialogVisible.value = true
  }
  activities.value = row

}

const setRes = (res,postData)=>{
  if(count.value===res_count.value){
      message("生成完毕")
      isDisabled.value = false
      count.value = 0
  }
  let labels_json = [
    "入院记录(合作)",
    "入院记录(不合作)",
    "入院记录(内转)",
    "入院记录(复发)",
    "首次病程记录",
    "首次病程记录(内转)",
    "首次病程记录(复发)",
    "阶段小结",
    "出院记录"
  ]
  let list=user_list.value.map((item)=>{
      if(item.pid==postData.pid){
        item.btn_load = false
        item.prompt = res.data.prompt
        // 判断有匹配到类型，就做字符串转json 
        if(labels_json.includes(postData.document_type)){
          let info = JSON.parse(res.data.document)
          for(let key in info){
            if(key==='体格检查'||key==='病例特点'){
              let str = ""
              if(typeof info[key] === 'object') {
                for(let key2 in info[key]){
                  if(typeof info[key][key2] === 'object'){
                    for(let key3 in info[key][key2]){
                      str+=key3+":"+info[key][key2][key3]+"\n"
                    }
                  }else{
                    str+=key2+":"+info[key][key2]+"\n"
                  }
                }
              }else{
                str = info[key]
              }
              info[key] = str
            }else{
              info[key] = info[key]
            }
          }
          item.value =info
        }else{
          item.value = res.data.document
        }
      }
      console.log(item)
      return item
  })
  user_list.value = list
}


// 串行处理生成请求
const handleGenerate = async () => {
  let select_options = typeOptions.options.filter((row)=>row.value==select_value.value)
  if(isDisabled.value){
    message("结果生成中...")
    return;
  }
  if(select_options?.length===0){
    message("请选择病例类型...")
    return;
  }
  if(!last_time.value){
    message("请选择生成时间...")
    return;
  }
  let patient_list=user_list.value.filter((row)=>row.isSelect).map((item)=>{
    return {
      last_time:last_time.value,
      audio_text:voiceContent.value,
      pid:item.pid,
      pvid:item.pvid,
      document_type:select_options[0].label
    }
  })
  if(patient_list?.length===0){
    message("至少勾选一个用户...")
    return;
  }
  res_count.value = patient_list.length
  isDisabled.value=true
  count.value = 0

  // 重置用户状态
  user_list.value = user_list.value.map((item2)=>{
    let index = patient_list.findIndex((iRow)=>iRow.pid==item2.pid)
    if(index!==-1){
      item2.btn_load = true
      item2.value = ""
    }
    return item2
  })

  // 串行请求
  for (let i = 0; i < patient_list.length; i++) {
    await createDocumentByPatientSerial(patient_list[i])
  }
  // 全部完成后
  message("生成完毕")
  isDisabled.value = false
  count.value = 0
}

// 串行请求的实现
const createDocumentByPatientSerial = async (postData) => {
  try {
    const res = await post(`${api_url}/AiCase/createDocumentByPatient`, JSON.stringify(postData))
    count.value+=1
    setRes(res,postData)
  } catch (error) {
    count.value+=1
    // 失败也继续下一个
  }
}


const getLlm =async (row)=>{
  let url = `${baseUrl.url4}/center/llm`
  let data = {
    messages:[
      {
        role:"user",
        content:row.content
      }
    ]
  }
  try {
    const result = await post(url, JSON.stringify(data));
    return result;
  } catch (error) {
    throw new Error('网络响应失败');
  }
}

const editDement =async (row,item)=>{
  if(Array.isArray(item.prompt)){
    if(item.prompt?.length>1){
      if(row.document_type==='出院查房记录'){
        let info1 = await getLlm(item.prompt[0])
        let info2 = await getLlm(item.prompt[1])
        info1 = info1.replace('今日查房，','今日XX主任医师查房：')
        info1 = info1.split('。')
        info1 = info1.slice(0,info1?.length-2).join("。")+'查房后指示：患者病情好转，安排今日出院。出院医嘱：'
        item.value = info1+info2
      }else{
        if(!row.xbs_value){
          let info2 = item.prompt[1]
          let info1 = await getLlm(item.prompt[0])
          info2['content'] = info2['content'].replace("{now_ill}",info1)
          item.value =await getLlm(info2)
          row.xbs_value = item.value
        }else{
          item.value = row.xbs_value
        }
      }
    }else{
      item.value = await getLlm(item.prompt[0])
    }
  }else if(typeof item.prompt === 'object'){

    if(row.document_type==='入院记录(内转)'||row.document_type==='入院记录(复发)'){
      if(item.name==='体格检查'){
        // 精神检查
        let jsjc_text = await getLlm(item.prompt['精神检查']);
        item.value = jsjc_text+'\n'+item.prompt['辅助检查']
      }else{
        let cbzd = item.prompt
        if(item.name==='初步诊断'){
          if(!row.xbs_value){
            let xbs_prompt = row.prompt['现病史']
            let info1 = await getLlm(xbs_prompt[0])
            let info2 = xbs_prompt[1]
            info2['content'] = info2['content'].replace("{now_ill}",info1)
            row.xbs_value =await getLlm(info2)
          }
          cbzd['content'] = cbzd['content'].replace("{now_ill}",row.xbs_value)
        }
        item.value = await getLlm(cbzd);
      }
    }else{
      if(row.document_type==='入院记录(合作)'&&item.name==='体格检查'){
        item.value = item.prompt['精神检查']
        item.prompt = "暂无"
      }else{
        let text = await getLlm(item.prompt);
        if(row.document_type==='上级医师查房记录'){
          text = text.replace('今日查房，','今日XX主任医师查房：')
        }
        item.value = text
      }
    }
  }else{
    item.value = item.prompt
  }
  return item;
}

const getAi = async () => {
  // user_list.value = user_list.value.slice(0, 2)
  for (const row of user_list.value) {
      row.xbs_value = ""
      // row.list = await Promise.all(row.list.map(async (item,index) => {
      //
      // }));

      row.list.map(async (item,index) => {
        item = await editDement(row,item)
        return item
      })

      if(row.isSelect){
        count.value+=1
      }
  }
  isDisabled.value = false;
  console.log("执行完毕")
  console.log(user_list.value);
  // window.localStorage.setItem('user_list',JSON.stringify(user_list.value))
};


const getBatchPromptList = (postData)=>{
  //{patient_list,document_type,audio_text}
  load.value = true;
  isDisabled.value = true;
  let url = `${api_url}/AiCase/getBatchPromptList`
  post(url, JSON.stringify(postData)).then(res => {
    try {
      let arr = res.data;
      user_list.value = user_list.value.map((item)=>{
        let rindex = arr.findIndex((row)=>row.pid==item.pid);
        if(rindex!==-1){
          let prompt = res.data[rindex]['prompt']
          item.prompt = prompt;
          item.list =JSON.parse(JSON.stringify(item.list))
          item.list.forEach((pitem)=>{
            if(prompt[pitem.name]){
              pitem.prompt = prompt[pitem.name]
            }else{
              pitem.prompt = prompt
            }
          })
        }
        return item
      })
      getAi()
      load.value = false
    }catch (e) {
      isDisabled.value = false
      message("接口请求失败！")
      load.value = false
    }

  }).catch(error => {
    load.value = false
    console.error('发生错误:', error);
  });
}
// 选择生成类型
const selectCascader = (value)=>{
  nodeList.value=[]

  let obj = {}
  let nodes = _refCascader.value.getCheckedNodes()
  nodes.forEach((row)=>{
    if(row.parent){
      if(obj[row.pathLabels[0]]){
        obj[row.pathLabels[0]].push(row.pathLabels[1])
      }else{
        obj[row.pathLabels[0]]=[row.pathLabels[1]]
      }
    }else{
      obj[row.label] = []
    }
  })
  let arr = []
  for(let key in obj){
    let push_obj = {
      name:key,
      list:obj[key].map((item)=>{
      return {name:item,value:"",prompt:'暂无'}
    })
    }

    if(is_data.indexOf(key)!==-1){
      push_obj = {
        name:key,
        list:[{name:key,value:"",prompt:'暂无'}]
      }
    }
    arr.push(push_obj)
  }
  if(arr?.length===0){
    return
  }
  user_list.value = user_list.value.map((item)=>{
    item['list'] = arr[0]['list']
    item['document_type'] = arr[0]['name']
    return item
  })
  nodeList.value = arr
}
// 获取患者
const getUser = ()=>{
  let url = `${api_url}/AiCase/getPatientList`
  post(url, JSON.stringify({ward_id:selectedDepartment.value})).then(res => {
    user_list.value = res.data;
    user_list.value =  user_list.value.map((item,index)=>{
      item.isSelect = true;
      item.btn_load = false;
      item.value = ""
      item.list = JSON.parse(JSON.stringify(typeOptions.list)).map((obj)=>{
        obj.value = "";
        return obj;
      })
      return item
    })
    rowSelect()
    load.value = false
  }).catch(error => {
    load.value = false
    console.error('发生错误:', error);
  });
}
// 获取病区
const getWardList = ()=>{
      let url = `${api_url}/AiCase/getWardList`
    load.value = true
    post(url, JSON.stringify({})).then(res => {
    select_list.value = res.data
    load.value = false
  }).catch(error => {
    load.value = false
    console.error('发生错误:', error);
  });
}
getWardList()
</script>

<style scoped>
.isDisabled{
  position: absolute;
  left: 50%;
  bottom: 50px;
  width: 50px;
  height: 50px;
  color: red;
}
.text{
  white-space: pre-line; /* 或者使用 pre-wrap */
}
.title-1{
  margin-bottom: 15px;
}
.item-type-list{
  border: 1px solid #ffffff;
  background: #ffffff;
  border-radius: 5px;
  overflow: hidden;
  margin-bottom: 15px;
}
.content-title{
  padding: 5px;
}
.content-title .title{
  margin-bottom: 5px;
}
.item-content{
  font-size: 14px;
  height: auto;
  white-space: pre-line; /* 或者使用 pre-wrap */
}
.medical-record-select {
  display: flex;
  height: calc(100% - 40px);
  padding: 20px;
  gap: 20px;
}

.filter-section {
  width: 300px;
  flex-shrink: 0;
}

.content-section {
  background-color: #f5f7fa;
  padding: 20px;
  border-radius: 4px;
  display: flex;
  flex-wrap: wrap;
  height:calc(100% - 40px);
  overflow-y: scroll;
  -moz-column-count:3; /* Firefox */
  -webkit-column-count:3; /* Safari 和 Chrome */
  column-count:3;
  -moz-column-gap: 1em;
  -webkit-column-gap: 1em;
  column-gap: 1em;
  margin:0 auto;
  width: 100%;
}

.content-section .item{
  margin-bottom: 1em;
  -webkit-column-break-inside: avoid;
  break-inside: avoid; /*防止断点*/
  background: #ccc;
  width: 100%;
  border-radius: 8px;
  padding: 14px 14px 0px 14px;
}
.title{
  display: flex;
  justify-content: space-between;
  font-size: 15px;
}

.filter-item {
  margin-bottom: 20px;
}

.filter-title {
  font-weight: bold;
  margin-bottom: 10px;
}

.voice-hint {
  margin-bottom: 10px;
}

.generate-btn {
  width: 100%;
}
</style>