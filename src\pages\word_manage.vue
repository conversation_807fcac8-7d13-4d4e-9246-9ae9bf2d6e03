<template>
  <div class="word-manage-container">
    <!-- Tab navigation -->
    <el-tabs v-model="activeTab" class="word-tabs">
      <el-tab-pane label="使用中" name="active"></el-tab-pane>
      <el-tab-pane label="已弃用" name="deprecated"></el-tab-pane>
    </el-tabs>

    <div class="content-container">
      <!-- Left side: Categories -->
      <div class="category-container">
        <div class="category-header">
          <span v-if="activeTab === 'active'">
            <el-button type="primary" size="small" @click="addNewCategory"
              >新建分类</el-button
            >
          </span>
        </div>

        <div class="category-list">
          <el-menu
            :default-active="activeCategory"
            class="category-menu"
            @select="handleCategorySelect"
          >
            <el-menu-item index="all">
              <span>全部 (默认)</span>
            </el-menu-item>
            <el-menu-item
              v-for="category in categories"
              :key="category.id"
              :index="category.id.toString()"
            >
              <div class="category-item">
                <span>{{ category.name }}</span>
                <div v-if="activeTab === 'active'" class="category-actions">
                  <el-dropdown
                    trigger="click"
                    @command="handleCategoryCommand($event, category)"
                    @click.stop
                  >
                    <el-icon>
                      <MoreFilled />
                    </el-icon>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item command="rename" icon="Edit"
                          >重命名</el-dropdown-item
                        >
                        <el-dropdown-item command="delete" icon="Delete"
                          >删除</el-dropdown-item
                        >
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </div>
              </div>
            </el-menu-item>
          </el-menu>
        </div>
      </div>

      <!-- Right side: Prompt words -->
      <div class="prompt-container">
        <div class="prompt-header">
          <div class="search-container">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索提示词或编码"
              prefix-icon="el-icon-search"
              clearable
              @clear="handleSearchClear"
              @input="handleSearch"
            ></el-input>
          </div>
          <div v-if="activeTab === 'active'" class="add-prompt">
            <el-button type="primary" @click="addNewPrompt"
              >新建提示词</el-button
            >
          </div>
        </div>

        <div class="prompt-list">
          <div
            v-for="prompt in filteredPrompts"
            :key="prompt.id"
            class="prompt-card"
          >
            <div class="prompt-card-header">
              <div class="prompt-title">
                <span class="prompt-name">{{ prompt.title }}</span>
                <span class="prompt-code">{{ prompt.code }}</span>
              </div>
              <div class="prompt-actions">
                <el-tooltip
                  content="编辑"
                  placement="top"
                  v-if="activeTab === 'active'"
                >
                  <el-icon>
                    <EditPen @click="editPrompt(prompt)" />
                  </el-icon>
                  <!-- <i class="el-icon-edit" @click="editPrompt(prompt)"></i> -->
                </el-tooltip>
                <el-tooltip content="历史版本" placement="top">
                  <el-icon>
                    <Clock @click="viewHistory(prompt)" />
                  </el-icon>

                  <!-- <i class="el-icon-time" @click="viewHistory(prompt)"></i> -->
                </el-tooltip>
                <el-tooltip content="删除" placement="top">
                  <el-icon>
                    <Delete @click="deletePrompt(prompt)" />
                  </el-icon>
                  <!-- <i class="el-icon-delete" @click="deletePrompt(prompt)"></i>   -->
                </el-tooltip>
              </div>
            </div>
            <div class="prompt-content">
              <div class="prompt-section">
                <el-input
                  type="textarea"
                  style="background: '#f9fafb'"
                  v-model="prompt.content"
                  :rows="10"
                  readonly
                ></el-input>

                <!-- <div class="section-content" v-html="formatContent(section.content)"></div> -->
              </div>
            </div>
            <div class="prompt-content">
              <div class="prompt-section">
                <div class="section-title">备注</div>
                <div class="section-content">{{ prompt.mark }}</div>
              </div>
            </div>
            <div class="prompt-footer">
              <span>更新于 {{ prompt.update_time }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Dialogs -->
    <!-- Add/Edit Category Dialog -->
    <el-dialog
      :title="categoryForm.isEdit ? '重命名分类' : '新建分类'"
      v-model="categoryDialogVisible"
      width="30%"
    >
      <el-form :model="categoryForm" :rules="categoryRules">
        <el-form-item label="分类名称" prop="name" :rules="categoryRules.name">
          <el-input
            v-model="categoryForm.name"
            placeholder="请输入分类名称"
          ></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="categoryDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitCategoryForm">确定</el-button>
      </span>
    </el-dialog>

    <!-- Add/Edit Prompt Dialog -->
    <el-dialog
      :title="promptForm.isEdit ? '编辑提示词' : '新建提示词'"
      v-model="promptDialogVisible"
      width="60%"
    >
      <el-form :model="promptForm" :rules="promptRules" label-width="100px">
        <el-form-item label="提示词名称" prop="title">
          <el-input
            v-model="promptForm.title"
            placeholder="请输入提示词名称"
          ></el-input>
        </el-form-item>
        <el-form-item label="编码" prop="code">
          <el-input
            v-model="promptForm.code"
            placeholder="请输入编码"
          ></el-input>
        </el-form-item>
        <el-form-item label="所属分类" prop="categoryId">
          <el-select
            v-model="promptForm.categoryId"
            placeholder="请选择所属分类"
          >
            <el-option
              v-for="category in categories"
              :key="category.id"
              :label="category.name"
              :value="category.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="内容" prop="content">
          <el-input
            type="textarea"
            v-model="promptForm.content"
            :rows="5"
            placeholder="请输入内容"
          ></el-input>
        </el-form-item>
        <el-form-item label="备注" prop="mark">
          <el-input
            type="textarea"
            v-model="promptForm.mark"
            :rows="5"
            placeholder="请输入标记"
          ></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="promptDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitPromptForm">确定</el-button>
      </span>
    </el-dialog>

    <!-- History Dialog -->
    <el-dialog title="历史版本" v-model="historyDialogVisible" width="70%">
      <div v-if="selectedPrompt" class="history-container">
        <el-timeline>
          <el-timeline-item
            v-for="(history, index) in promptHistory"
            :key="index"
            :timestamp="history.updateTime"
            placement="top"
          >
            <el-card>
              <div
                class="history-card-header"
                style="
                  display: flex;
                  flex-direction: row;
                  justify-content: space-between;
                  margin: 10px 0;
                "
              >
                <h4>版本 {{ promptHistory.length - index }}</h4>
                <el-button
                  type="primary"
                  size="small"
                  @click="viewModificationReason(history)"
                  icon="InfoFilled"
                  >查看修改原因</el-button
                >
              </div>
              <div class="history-section">
                <el-input
                  type="textarea"
                  v-model="history.content"
                  :rows="3"
                  readonly
                ></el-input>
              </div>
              <div class="history-section">
                <div class="section-title">备注</div>
                <div>{{ history.mark || "无" }}</div>
              </div>
              <div class="history-footer">
                <span>更新于 {{ history.create_time }}</span>
              </div>
            </el-card>
          </el-timeline-item>
        </el-timeline>
      </div>
    </el-dialog>

    <!-- Modification Reason Dialog -->
    <el-dialog title="修改原因" v-model="reasonDialogVisible" width="50%">
      <div v-if="selectedHistoryItem" class="reason-container">
        <div class="history-section">
          <div class="section-title">修改原因</div>
          <el-input
            type="textarea"
            v-model="selectedHistoryItem.reason"
            :rows="3"
            placeholder="请输入修改原因"
          ></el-input>
        </div>
        <div class="dialog-footer">
          <el-button @click="reasonDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveModificationReason"
            >保存</el-button
          >
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from "vue";
import { post, get } from "@/utils/request.js";
import c from "@/utils/config";
import { confirm, message } from "@/utils/message";

const api_url = c.api_url;
const activeTab = ref("active");
const activeCategory = ref("all");
const searchKeyword = ref("");
const categories = ref([]);
const prompts = ref([]);
const categoryDialogVisible = ref(false);
const promptDialogVisible = ref(false);
const historyDialogVisible = ref(false);
const reasonDialogVisible = ref(false);
const selectedPrompt = ref(null);
const selectedHistoryItem = ref(null);
const promptHistory = ref([]);

// Form data
const categoryForm = reactive({
  id: null,
  name: "",
  isEdit: false,
});

const promptForm = reactive({
  id: null,
  title: "",
  type: "",
  code: "",
  mark: "",
  categoryId: null,
  content: [{ title: "", content: "" }],
  isEdit: false,
});

// Form rules
const categoryRules = {
  name: [
    { required: true, message: "请输入分类名称", trigger: "blur" },
    { min: 1, max: 20, message: "长度在 1 到 20 个字符", trigger: "blur" },
  ],
};

const promptRules = {
  title: [
    { required: true, message: "请输入提示词名称", trigger: "blur" },
    { min: 1, max: 50, message: "长度在 1 到 50 个字符", trigger: "blur" },
  ],
  code: [{ required: true, message: "请输入编码", trigger: "blur" }],
  categoryId: [
    { required: true, message: "请选择所属分类", trigger: "change" },
  ],
  content: [{ required: true, message: "请输入内容", trigger: "blur" }],
};

// Computed properties
const filteredPrompts = computed(() => {
  let result = prompts.value.filter((prompt) => {
    // Filter by tab

    if (activeTab.value === "active" && prompt.status !== 1) return false;
    if (activeTab.value === "deprecated" && prompt.status !== 0) return false;

    // Filter by category
    if (
      activeCategory.value !== "all" &&
      prompt.type_id !== parseInt(activeCategory.value)
    )
      return false;

    // Filter by search
    if (searchKeyword.value) {
      const keyword = searchKeyword.value.toLowerCase();
      return (
        prompt.title.toLowerCase().includes(keyword) ||
        prompt.code.toLowerCase().includes(keyword) ||
        JSON.stringify(prompt.content).toLowerCase().includes(keyword)
      );
    }

    return true;
  });

  return result;
});

// Methods
const fetchCategories = () => {
  post(`${api_url}/Prompt/getTypeList`)
    .then((data) => {
      categories.value = data.data;
    })
    .catch((error) => {
      console.error("Error fetching categories:", error);
      // For demo, add some sample data
      categories.value = [
        { id: 1, name: "入院记录" },
        { id: 2, name: "病历" },
        { id: 3, name: "三级医院" },
      ];
    });
};

const fetchPrompts = () => {
  post(`${api_url}/Prompt/getAllList`, JSON.stringify({})).then((data) => {
    console.log(123333, data);

    prompts.value = data.data || [];
  });
};

const handleCategorySelect = (index) => {
  activeCategory.value = index;
};

const handleCategoryCommand = (command, category) => {
  if (command === "rename") {
    renameCategory(category);
  } else if (command === "delete") {
    deleteCategory(category);
  }
};

const handleSearch = () => {
  // Search is handled by the computed property
};

const handleSearchClear = () => {
  searchKeyword.value = "";
};

const addNewCategory = () => {
  categoryForm.id = null;
  categoryForm.name = "";
  categoryForm.isEdit = false;
  categoryDialogVisible.value = true;
};

const renameCategory = (category) => {
  categoryForm.id = category.id;
  categoryForm.name = category.name;
  categoryForm.isEdit = true;
  categoryDialogVisible.value = true;
};

const deleteCategory = (category) => {
  // Check if category has prompts
  const hasPrompts = prompts.value.some(
    (p) => p.type_id === category.id && p.status === 1
  );

  // if (hasPrompts) {
  ElMessageBox.confirm(
    hasPrompts ? "该分组下存在使用中的提示词，无法删除" : "是否确认删除？",
    "警告",
    {
      confirmButtonText: hasPrompts ? "" : "确定",
      cancelButtonText: hasPrompts ? "好的" : "取消",
      showConfirmButton: !hasPrompts,
      type: "warning",
    }
  )
    .then(() => {
      // Call API to delete category
      post(
        `${api_url}/Prompt/deleteType`,
        JSON.stringify({
          id: category.id,
        })
      ).then(() => {
        ElMessage.success("删除成功");
        fetchCategories();
        fetchPrompts();
      });
    })
    .catch(() => {
      // User cancelled
    });
};

const submitCategoryForm = () => {
  if (categoryForm.isEdit) {
    // Update existing category
    post(
      `${api_url}/Prompt/updateType`,
      JSON.stringify({
        id: categoryForm.id,
        name: categoryForm.name,
      })
    )
      .then(() => {
        ElMessage.success("更新成功");
        fetchCategories();
        categoryDialogVisible.value = false;
      })
      .catch((error) => {
        console.error("Error updating category:", error);
        // For demo
        const index = categories.value.findIndex(
          (c) => c.id === categoryForm.id
        );
        if (index !== -1) {
          categories.value[index].name = categoryForm.name;
        }
        ElMessage.success("更新成功");
        categoryDialogVisible.value = false;
      });
  } else {
    // Create new category
    post(
      `${api_url}/Prompt/addType`,
      JSON.stringify({
        name: categoryForm.name,
      })
    )
      .then((data) => {
        ElMessage.success("创建成功");
        fetchCategories();
        categoryDialogVisible.value = false;
      })
      .catch((error) => {
        console.error("Error creating category:", error);
        // For demo
        const newId = Math.max(...categories.value.map((c) => c.id), 0) + 1;
        categories.value.push({
          id: newId,
          name: categoryForm.name,
        });
        ElMessage.success("创建成功");
        categoryDialogVisible.value = false;
      });
  }
};

const addNewPrompt = () => {
  promptForm.id = null;
  promptForm.title = "";
  promptForm.code = "";
  promptForm.categoryId = +activeCategory.value ? +activeCategory.value : null;
  promptForm.content = "";
  promptForm.mark = "";
  promptForm.isEdit = false;
  promptDialogVisible.value = true;
};

const editPrompt = (prompt) => {
  promptForm.id = prompt.id;
  promptForm.title = prompt.title;
  promptForm.code = prompt.code;
  promptForm.categoryId = prompt.type_id;
  promptForm.content = prompt.content;
  promptForm.mark = prompt.mark;
  promptForm.isEdit = true;
  promptDialogVisible.value = true;
};

const deletePrompt = (prompt) => {
  ElMessageBox.confirm(
    activeTab.value === "active"
      ? "确定要删除该提示词吗？"
      : "确定要彻底删除该提示词吗？",
    "警告",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    }
  )
    .then(() => {
      // Call API to delete prompt
      const URL =
        activeTab.value === "active"
          ? "/Prompt/deletePrompt"
          : "/Prompt/deletePromptTrue";
      post(
        `${api_url}${URL}`,
        JSON.stringify({
          id: prompt.id,
        })
      ).then(() => {
        ElMessage.success("删除成功");
        fetchPrompts();
      });
    })
    .catch(() => {
      // User cancelled
    });
};

const viewHistory = (prompt) => {
  selectedPrompt.value = prompt;

  // Fetch history
  post(
    `${api_url}/Prompt/getRecord`,
    JSON.stringify({
      prompt_id: prompt.id,
    })
  ).then((data) => {
    promptHistory.value = data.data || [];
    historyDialogVisible.value = true;
  });
};

const viewModificationReason = (history) => {
  selectedHistoryItem.value = { ...history };
  reasonDialogVisible.value = true;
};

const saveModificationReason = () => {
  // Call API to update the modification reason
  post(
    `${api_url}/Prompt/updateRecord`,
    JSON.stringify({
      id: selectedHistoryItem.value.id,
      reason: selectedHistoryItem.value.reason,
    })
  ).then((data) => {
    ElMessage.success("修改原因更新成功");
    // Update the reason in the promptHistory array
    const index = promptHistory.value.findIndex(
      (item) => item.id === selectedHistoryItem.value.id
    );
    if (index !== -1) {
      promptHistory.value[index].reason = selectedHistoryItem.value.reason;
    }
    reasonDialogVisible.value = false;
  });
};

const submitPromptForm = () => {
  if (promptForm.isEdit) {
    // Update existing prompt
    post(
      `${api_url}/Prompt/updatePromptAndRecord`,
      JSON.stringify({
        title: promptForm.title,
        code: promptForm.code,
        id: promptForm.id,
        content: promptForm.content,
        mark: promptForm.mark,
        type_id: promptForm.categoryId,
      })
    ).then(() => {
      ElMessage.success("更新成功");
      fetchPrompts();
      promptDialogVisible.value = false;
    });
  } else {
    // Create new prompt
    post(
      `${api_url}/Prompt/addPrompt`,
      JSON.stringify({
        title: promptForm.title,
        code: promptForm.code,
        type_id: promptForm.categoryId,
        content: promptForm.content,
        mark: promptForm.mark,
      })
    ).then(() => {
      ElMessage.success("创建成功");
      fetchPrompts();
      promptDialogVisible.value = false;
    });
  }
};

const formatContent = (content) => {
  if (!content) return "";

  // Replace newlines with <br> and preserve indentation
  return content.replace(/\n/g, "<br>").replace(/\s{2}/g, "&nbsp;&nbsp;");
};

onMounted(() => {
  fetchCategories();
  fetchPrompts();
});
</script>

<style scoped>
.word-manage-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.word-tabs {
  margin-bottom: 20px;
}

.content-container {
  display: flex;
  height: calc(100vh - 150px);
  gap: 20px;
}

.category-container {
  width: 250px;
  display: flex;
  flex-direction: column;
  border-right: 1px solid #e0e0e0;
}

.category-header {
  padding: 10px;
  border-bottom: 1px solid #e0e0e0;
}

.category-list {
  flex: 1;
  overflow-y: auto;
}

.category-menu {
  border-right: none;
}

.category-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}
.category-item span {
  display: inline-block;
  max-width: 160px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  vertical-align: middle;
}

.category-actions {
  display: flex;
  gap: 5px;
}

.category-actions i {
  cursor: pointer;
  font-size: 16px;
  color: #909399;
}

.category-actions i:hover {
  color: #409eff;
}

.prompt-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.prompt-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  margin-bottom: 20px;
}

.search-container {
  width: 300px;
}

.prompt-list {
  flex: 1;
  overflow-y: auto;
  padding-right: 10px;
}

.prompt-card {
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  margin-bottom: 20px;
  background-color: #fff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.prompt-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
}

.prompt-title {
  /* display: flex;
    flex-direction: column; */
}

.prompt-name {
  font-size: 16px;
  font-weight: bold;
}

.prompt-code {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
  margin-left: 45px;
}

.prompt-actions {
  display: flex;
  gap: 15px;
}

.prompt-actions i {
  cursor: pointer;
  font-size: 18px;
  color: #909399;
}

.prompt-actions i:hover {
  color: #409eff;
}

.prompt-content {
  padding: 15px;
}

.prompt-section {
  margin-bottom: 15px;
}

.section-title {
  font-weight: bold;
  margin-bottom: 5px;
}

.section-content {
  font-size: 14px;
  line-height: 1.6;
  white-space: pre-wrap;
}

.prompt-footer {
  padding: 10px 15px;
  /* border-top: 1px solid #e0e0e0; */
  color: #909399;
  font-size: 12px;
  /* text-align: right; */
}

.content-section {
  margin-bottom: 15px;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  gap: 10px;
}

.add-section {
  margin-top: 10px;
}

.history-container {
  max-height: 500px;
  overflow-y: auto;
}

.history-section {
  margin-bottom: 15px;
}
</style>
