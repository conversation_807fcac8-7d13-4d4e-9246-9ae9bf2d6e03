<template>

  <div class="tableData">
    <h3>{{ route.meta.title }}</h3>
    <!-- <div class="topMenu">
      <div class="menuBox">
      </div>
    </div> -->
    <div class="table">
      <el-collapse accordion v-model="activeNames">
        <el-collapse-item :title="key" :name="key" v-for="(item, key) in info">
          <el-timeline>
            <el-timeline-item :timestamp="row.title" placement="top" v-for="row in item">
              <el-card>
                <div style="margin-bottom: 15px">
                  <el-button type="primary" @click="saveWord(row)">保存测试</el-button>
                  <el-button type="primary" @click="saveWordZs(row)">保存正式</el-button>
                </div>
                <el-input
                  class="textarea"
                  v-model="row.content"
                  type="textarea"
                  placeholder="Please input"
                  :autosize="{ minRows: 2, maxRows: 12 }"
                />
              </el-card>
            </el-timeline-item>
          </el-timeline>
        </el-collapse-item>
      </el-collapse>
    </div>
  </div>

</template>

<script setup>
import { ref, nextTick } from 'vue';
import { message } from "@/utils/message"
import { post } from '@/utils/request.js'
const outerVisible = ref(false)
import c from "@/utils/config";
import { useRoute } from 'vue-router';
import { baseUrl } from '../utils/baseUrl';

const route = useRoute();
const api_url = c.api_url;
const info = ref({})
const activeNames = ref("")
const load = ref(false)

const saveWordZs = (row) => {
  load.value = true
  post(`${baseUrl.hos_zhixuee}/prompt/updatePrompt`, JSON.stringify({
    id: row.id,
    content: row.content
  })).then(res => {
    load.value = false
    message("保存成功!", 'success')
  }).catch(error => {
    load.value = false
  });
}

const saveWord = (row) => {
  load.value = true
  let url = `${baseUrl.h_test_zhixuee}/php/prompt/updatePrompt`
  post(url, JSON.stringify({
    id: row.id,
    content: row.content
  })).then(res => {
    load.value = false
    message("保存成功!", 'success')
  }).catch(error => {
    load.value = false
  });
}

const getPromptList = () => {
  load.value = true
  post(`${api_url}/prompt/getList`, JSON.stringify({})).then(res => {
    console.log(res.data)
    let obj = {}
    res.data.forEach((row) => {
      if (!row.document_type) {
        row.document_type = '未命名'
      }
      if (obj[row.document_type]) {
        obj[row.document_type].push(row)
      } else {
        obj[row.document_type] = [row]
      }

    })

    info.value = obj
    load.value = false
  }).catch(error => {
    load.value = false
    console.error('发生错误:', error);
  });
}
getPromptList()
</script>

<style scoped>
.textarea {
  width: 100%;
  padding: 2px;
  /* height: 250px; */
}

/* :deep(.el-textarea__inner) {
  height: 100%;
} */

.tableData {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  background-color: #ffffff;
  padding: 10px;
  box-sizing: border-box;
}

h3 {
  height: 40px;
}

.topMenu {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 10px;
  flex-wrap: wrap;
  gap: 5px;
}

.menuBox {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 10px;
  flex-wrap: wrap;
  gap: 5px;
}

.table {
  flex: 1;
  overflow-y: auto;
}
</style>