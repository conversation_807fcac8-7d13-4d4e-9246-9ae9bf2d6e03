<!-- filepath: d:\工作项目\医院项目\coolmed_admin\src\components\DealTags.vue -->
<template>
  <el-dialog v-model="visible" title="批量处理标签" width="60%">
    <div style="margin-bottom: 8px;">
      待处理标签数量：<b>{{ tags.length }}</b>
    </div>
    <div style="margin-bottom: 16px;">
      <span>进程数：</span>
      <el-slider :disabled="processRunning" v-model="processCount" :min="1" :max="10" show-input
        style="width: 300px;" />
    </div>
    <el-scrollbar height="400px">
      <el-table :data="processStatusList" border style="width: 100%; margin-bottom: 16px;">
        <el-table-column prop="index" label="进程编号" width="100" />
        <el-table-column prop="pending" label="待处理数" width="100" />
        <el-table-column prop="success" label="成功数" width="100" />
        <el-table-column prop="fail" label="失败数" width="100" />
        <el-table-column label="处理进度" width="200" :formatter="(_, row) => calcPercent(row)">
          <template #default="scope">
            <el-progress :percentage="calcPercent(scope.row, true)" :text-inside="false" :stroke-width="16"
              :status="calcPercent(scope.row) == '100%' ? 'success' : 'format'" style="width: 100%" />
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" />
      </el-table>
    </el-scrollbar>
    <div style="margin-top: 16px;">
      <b>汇总：</b>
      <span>总成功：{{ totalSuccess }}，总失败：{{ totalFail }}，总进程：{{ processCount }}</span>
    </div>
    <template #footer>
      <el-button @click="visible = false">关闭</el-button>
      <el-button type="primary" :loading="processRunning" @click="startProcess"
        :disabled="processRunning">开始处理</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { post } from '@/utils/request.js'
import { baseUrl } from '../utils/baseUrl'

const props = defineProps({
  tags: { type: Array, default: () => [] }, // 传入待处理标签数组
  apiUrl: { type: String, required: true }
})

const visible = ref(false)
const processCount = ref(0)
const processStatusList = reactive([])
const processRunning = ref(false)
const totalSuccess = computed(() => processStatusList.reduce((sum, p) => sum + p.success, 0))
const totalFail = computed(() => processStatusList.reduce((sum, p) => sum + p.fail, 0))

// 打开弹窗方法，父组件可调用
function open() {
  visible.value = true
  processCount.value = 10
  if (processStatusList.length === 0) {
    for (let i = 0; i < processCount.value; i++) {
      processStatusList.push({ index: i + 1, success: 0, fail: 0, pending: 0, status: '等待中' })
    }
  }
}
defineExpose({ open })

// 动态分配每个进程的待处理数
watch(processCount, (val) => {
  while (processStatusList.length < val) {
    processStatusList.push({ index: processStatusList.length + 1, success: 0, fail: 0, pending: 0, status: '等待中' })
  }
  while (processStatusList.length > val) {
    processStatusList.pop()
  }
  const tags = props.tags || []
  const chunkSize = Math.ceil(tags.length / processCount.value)
  for (let i = 0; i < processStatusList.length; i++) {
    const chunk = tags.slice(i * chunkSize, (i + 1) * chunkSize)
    processStatusList[i].pending = chunk.length
  }
})

function calcPercent(row, onlyNum = false) {
  const total = row.success + row.fail + row.pending
  if (total === 0) return onlyNum ? 0 : '0%'
  const done = row.success
  const percent = Math.round((done / total) * 100)

  return onlyNum ? percent : percent + '%'
}

// 处理标签接口
const haddleTags = async (label) => {
  try {
    const response = await fetch(props.apiUrl + `/v1/api/tag/classify`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': 'API655c18174462a06ede7ffbfa5aca4270c645b9b78ca30817e3a1bd496f34e04b',
        'authorization': 'Bearer 6787c226c3eae7.874922141Z3hNBMuGI'
      },
      body: JSON.stringify({ tag: label })
    })

    const result = await response.json()
    if (result.error_code === 0 && result.data) {
      return result.data
    } else {
      return null
    }
  } catch (error) {
    console.error(error)
    return null
  }
}

// AI prompt
const getWord = (tags) => {
  return `
        ;; 作者: [你的名字]
        ;; 版本: 1.0
        ;; 模型: (适用于 GPT-4, Claude 3 等)
        ;; 用途: 将精神病学的复合症状标签，精确拆解为原子化的子标签，并以严格的JSON格式输出。

        ;; 设定如下内容为你的 *System Prompt*
        (defun 精神症状分析师 ()
          "你是一个逻辑极其严谨的精神病学领域数据处理器"
          (身份 . "专业的精神病学领域助手")
          (专长 . ("症状分析" "概念拆分" "逻辑推理"))
          (原则 . ("忠于原文" "绝对精确" "无冗余"))
          (工作模式 . "确定性算法模式"))

        (defun 症状标签拆解 (原始标签)
          "定义了如何将一个标签拆分为子标签的核心算法"
          (let (标签类型定义 '((复合标签 "包含多个并列概念")
                              (原子标签 "单一且不可再分的概念")))

            ;; 核心拆分规则
            (拆分规则 '(
              (独立性原则 "每个子标签必须是独立的、完整的症状描述")
              (修饰语复制原则 "修-饰语(如: 否认, 持续性, 伴有)必须被精确复制到每个对应的子标签中")
              (原子处理原则 "原子标签本身即为其自身的唯一子标签")
              (语义守恒原则 "拆分后的所有子标签合集必须与原始标签的含义完全等价")))

            ;; 执行流水线: 对原始标签应用上述所有规则进行处理
            (执行 (应用 语义守恒原则 (应用 原子处理原则 (应用 修饰语复制原则 (应用 独立性原则 (识别类型 原始标签))))))
          ))

        (defun 输出为JSON (处理结果)
          "将处理结果封装成严格的、无换行的单行JSON数组"
          (setq format-rules '(
              (根类型 . "JSON数组")
              (对象结构 . '((compound_label "string") (sub_label "array of strings")))
              (换行符 . "禁止")))

          ;; 格式化示例 (此示例仅用于定义结构, 其内容本身不作为输出)
          (let (format-example "[{\"compound_label\":\"否认幻听、幻视\",\"sub_label\":[\"否认幻听\",\"否认幻视\"]},{\"compound_label\":\"妄想\",\"sub_label\":[\"妄想\"]}]")

          ;; 将 '处理结果' 严格按照 'format-rules' 和 'format-example' 的规范进行最终渲染
          (Render-JSON 处理结果)))

        (defun 自我校验与修正 (最终输出 原始输入源)
          "在输出前执行的最后一道质量控制关卡"
          (let (校验逻辑 "检查[最终输出]中每个对象的'compound_label'字段, 是否与[原始输入源]中的标签完全对应且顺序一致")
            (修正机制 "若[校验逻辑]失败, 则立即废弃[最终输出], 并严格基于[原始输入源]重新执行整个流程, 直至校验通过"))
          (return (执行修正机制 (执行校验逻辑))))


        ;; 运行规则
        ;; 1. 启动时, 必须以 (精神症状分析师) 的身份和设定来执行任务。
        ;; 2. 你的唯一输入是 '{tags}' 变量。首先，你需要将 '{tags}' 的字符串按中文逗号【，】分割成一个 [原始标签列表]。
        ;; 3. 遍历 [原始标签列表] 中的【每一个】标签，并独立调用 (症状标签拆解 标签) 函数处理它。
        ;; 4. 将所有处理结果的集合传递给 (输出为JSON) 函数进行最终格式化，生成 [最终输出]。
        ;; 5. 在向用户展示结果之前，【必须】调用 (自我校验与修正 [最终输出] [原始标签列表]) 函数，这是最后且强制的一步。
        ;; 6. 最终，只输出通过校验的、纯净的JSON字符串。不要包含任何额外的解释、注释或文字。

        ;; 启动指令
        (print "精神症状分析师已就绪。正在等待 '{tags}' 输入...")

        ---
        ### 本次任务输入
        {tags} = ${tags}

        / no think
    `
}

// AI接口
const getai = async (text) => {
  let url = `${baseUrl.ai_url}`
  let token_key = "sk-a11f833afe094bb9971932a5ea001834"
  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Cache-Control': 'no-cache',
      'Authorization': 'Bearer ' + token_key,
    },
    body: JSON.stringify({
      messages: [{ role: "user", content: text }],
      temperature: 0.0,
      model: 'qwen3-32b',
      enable_thinking: false,
      stream: false
    })
  });
  const result = await response.json();
  let str = result.choices[0]['message']['content']
  return str
}

// 启动多进程处理
async function startProcess() {
  processRunning.value = true
  const tags = props.tags || []
  const chunkSize = Math.ceil(tags.length / processCount.value)
  const processPromises = []

  for (let i = 0; i < processCount.value; i++) {
    const chunk = tags.slice(i * chunkSize, (i + 1) * chunkSize)
    processStatusList[i].success = 0
    processStatusList[i].fail = 0
    processStatusList[i].pending = chunk.length
    processStatusList[i].status = '处理中'
    processPromises.push(runProcess(i, chunk))
  }

  await Promise.all(processPromises)
  processRunning.value = false
  ElMessage.success('处理标签已完成')
}

// 单个进程处理函数
async function runProcess(idx, tagList) {
  for (const row of tagList) {
    try {
      let wrod = row?.name ? row?.name.replace(/ /g, '') : row.replace(/ /g, '')
      // if (row.name) {
      //   wrod = getWord()
      // } else {
      //   wrod = getWord()
      // }
      // let info = await getai(wrod)
      // let arr = info.replace(/<think>/g, '').replace(/<\/think>/g, '').replace(/ /g, '').replace(/\n/g, '')
      // arr = arr.replace(/```/g, '')
      // arr = arr.replace('json', '')
      let data = await haddleTags(wrod)
      if (data) {
        processStatusList[idx].success++
        processStatusList[idx].pending--
      } else {
        processStatusList[idx].fail++
        processStatusList[idx].pending--

      }
    } catch (e) {
      processStatusList[idx].fail++
      processStatusList[idx].pending--
    }
  }
  processStatusList[idx].status = '完成'
}
</script>