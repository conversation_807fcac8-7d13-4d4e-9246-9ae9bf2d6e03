<!-- 分页组件 -->
<template>
    <div class="pagination" ref="paginationRef" v-if="!isMobile">
        <el-pagination
        v-if="total > 0"
        :current-page="currentPage"
        :page-size="pageSize"
        :total="total"
        :pager-count="paperCount"
        :page-sizes="pageSizes"
        layout="total, sizes, prev, pager, next, jumper"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
        >
        </el-pagination>
    </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue'
const props = defineProps({
  currentPage: {
    type: Number,
    default: 1
  },
  pageSize: {
    type: Number,
    default: 10
  },
  total: {
    type: Number,
    default: 0
  },
  paperCount: {
    type: Number,
    default: 5
  },
  pageSizes: {
    type: Array,
    default: () => [10, 20, 30, 40, 50,100,200,500,1000]
  }
})

const emit = defineEmits(['current-change', 'size-change'])

const handleCurrentChange = (val) => {
  emit('current-change', val)
}

const handleSizeChange = (val) => {
  emit('size-change', val)
}


// 当宽度缩小时，组件隐藏分页
const isMobile = ref(false)
const updateIsMobile = () => {
  isMobile.value = window.innerWidth < 768
    
}
onMounted(() => {
  updateIsMobile()
  window.addEventListener('resize', updateIsMobile)
})
onBeforeUnmount(() => {
  window.removeEventListener('resize', updateIsMobile)
})
</script>
<style  scoped>
.pagination {
  margin-top: 20px;
  width: 100%;
  overflow: auto;
}
</style>