import config from "./config.js";
import md5 from "js-md5";
import aes from "./aes.js";

/**
 * 处理请求体，加密数据并添加验证信息
 * @param {*} data - 请求数据
 * @returns {string} - 处理后的请求体
 */
function enrichBody(data, url) {
  const request_data = {};
  if (config.mode !== "production") {
    console.log(`请求地址：${url}`, `请求参数: ${data}`);
  }

  // 如果有数据，进行加密
  if (data && data !== "{}") {
    request_data.data = aes.encrypt(data);
  }
  // 设置时间戳和访问令牌
  request_data.timestamp = Math.floor(Date.now() / 1000);
  request_data.AccessToken = md5(request_data.timestamp + config.secret_key);

  return JSON.stringify(request_data);
}

/**
 * 解码响应数据
 * @param {string} responseText - 响应文本
 * @returns {*} - 解码后的数据
 */
function decode(responseText) {
  const response = JSON.parse(responseText);
  let data = response.data;
  if (typeof data === "string" && !/[\u4e00-\u9fa5]/.test(data)) {
    data = aes.decryptBase64(aes.base64(response.data));
    // 判断数据是否为JSON格式，如果是则解析，否则不解析
    try {
      // 尝试解析JSON
      if (typeof data === "string") {
        // 清除特殊字符，如\x01等不可见控制字符
        let cleanData = data.replace(/[\x00-\x1F\x7F-\x9F]/g, "");
        let trimmedData = cleanData.trim();

        // 检查是否是对象或数组格式的JSON字符串
        if (trimmedData.startsWith("{") || trimmedData.startsWith("[")) {
          data = JSON.parse(trimmedData);
        } else {
          // 如果不是JSON格式，使用已清理的字符串
          data = cleanData;
        }
      }
    } catch (e) {
      // 解析失败，使用原始数据
      console.log("数据不是有效的JSON格式，返回原始数据");
    }
  }

  response.data = data;
  if (config.mode !== "production") {
    console.log("接口返回数据：", response);
  }

  return response;
}

/**
 * 通用安全请求函数
 * @param {string} url - 请求地址
 * @param {object} options - 请求选项
 * @returns {Promise} - 请求Promise
 */
export function secureFetch(url, options = {}) {
  const method = (options.method || "GET").toUpperCase();
  const headers = {
    "Content-Type": "application/json",
    ...(options.headers || {}),
  };

  const fetchOptions = {
    ...options,
    method,
    headers,
  };
  // 如果是需要 body 的请求，添加统一参数并加密
  if (["POST", "PUT", "PATCH"].includes(method)) {
    fetchOptions.body = enrichBody(options.body, url);
  }

  return fetch(url, fetchOptions)
    .then((res) => {
      if (!res.ok) {
        throw new Error(`HTTP ${res.status}: ${res.statusText}`);
      }
      return res.text();
    })
    .then(decode)
    .catch((err) => {
      console.error("secureFetch 请求出错：", err);
      throw err;
    });
}

/**
 * GET 请求
 * @param {string} url - 请求地址
 * @param {object} options - 请求选项
 * @returns {Promise} - 请求Promise
 */
export function get(url, options = {}) {
  return secureFetch(url, {
    ...options,
    method: "GET",
  });
}

/**
 * POST 请求
 * @param {string} url - 请求地址
 * @param {*} data - 请求数据
 * @param {object} options - 请求选项
 * @returns {Promise} - 请求Promise
 */
export function post(url, data, options = {}) {
  return secureFetch(url, {
    ...options,
    method: "POST",
    body: data,
  });
}

/**
 * PUT 请求
 * @param {string} url - 请求地址
 * @param {*} data - 请求数据
 * @param {object} options - 请求选项
 * @returns {Promise} - 请求Promise
 */
export function put(url, data, options = {}) {
  return secureFetch(url, {
    ...options,
    method: "PUT",
    body: data,
  });
}

/**
 * DELETE 请求
 * @param {string} url - 请求地址
 * @param {object} options - 请求选项
 * @returns {Promise} - 请求Promise
 */
export function del(url, options = {}) {
  return secureFetch(url, {
    ...options,
    method: "DELETE",
  });
}

/**
 * PATCH 请求
 * @param {string} url - 请求地址
 * @param {*} data - 请求数据
 * @param {object} options - 请求选项
 * @returns {Promise} - 请求Promise
 */
export function patch(url, data, options = {}) {
  return secureFetch(url, {
    ...options,
    method: "PATCH",
    body: data,
  });
}

// 默认导出
export default {
  secureFetch,
  get,
  post,
  put,
  delete: del,
  patch,
};
