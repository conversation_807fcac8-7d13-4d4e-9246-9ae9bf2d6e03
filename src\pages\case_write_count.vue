<template>
    <div class="date-box">
      <div class="filters">
        <div>
          <el-date-picker
              v-model="date_value"
              type="date"
              placeholder="书写日期"
              @change="selectDate"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              size="default"
          />
        </div>
        <div style="width: 200px;margin-left: 15px">
          <el-select
              v-model="sellect_type"
              placeholder="选择类型"
              @change="selectType"
          >
            <el-option
                v-for="item in options"
                :key="item"
                :label="item.label"
                :value="item.label"
            />
          </el-select>
        </div>
        <div>
          <el-button v-if="date_value===date_v" style="margin-left: 10px" @click="writeContent" type="primary">一键上传his</el-button>
          <el-button type="primary" @click="batch_case" v-if="date_value===date_v">批量生成病例</el-button>
          <el-button type="text" @click="see_new">更新记录</el-button>

          <el-button type="warning">未执行：{{no_num - info_num}}</el-button>
          <el-button type="success">执行完成：{{success_num}}</el-button>
          <el-button type="danger">执行失败：{{error_num}}</el-button>
          <el-button type="info" @click="refresh">刷新</el-button>

          <el-button type="info" @click="statisticPage">统计</el-button>
          <el-button type="danger" @click="seeDisease">查看病种</el-button>
        </div>
      </div>
    </div>

    <el-table v-loading="load" :data="tableData" height="800px" border style="width: 100%">
      <el-table-column type="index" width="50" />
      <el-table-column prop="patient_name" label="患者名称" width="120"
         :filters="[
            { text: '已修改', value:2 },
            { text: '未修改', value:1 },
          ]"
         :filter-method="filterModify"
         filter-placement="bottom-end"
      >
        <template #default="scope">
          <el-button type="primary" @click="see(scope.row)" text>{{scope.row.patient_name}}</el-button>
        </template>
      </el-table-column>
<!--      <el-table-column prop="text1" label="上次日常">-->
<!--        <template #default="scope">-->
<!--          <div v-html="scope.row.last_case"></div>-->
<!--        </template>-->
<!--      </el-table-column>-->
      <el-table-column resizable prop="text1" label="今日AI书写">
        <template #default="scope">
          <div v-html="scope.row.ai_case"></div>
        </template>
      </el-table-column>
      <el-table-column resizable prop="text1" label="内部修改">
        <template #default="scope">
          <div>
<!--            <div v-html="scope.row.ai_case_edit_text"></div>-->
            <div contenteditable="true" v-if="scope.row.ai_case_edit" :id="'edit_text_'+scope.row.or_id" class="edit-text">{{scope.row.ai_case_edit}}</div>
            <el-button size="default" v-if="scope.row.ai_case_edit" @click="saveEdit(scope.row,'case')">保存编辑</el-button>
          </div>

<!--          <el-button size="primary" v-if="scope.row.ai_case_edit_text" @click="saveEdit(scope.row,'case')">对比</el-button>-->

        </template>
      </el-table-column>
      <el-table-column resizable prop="text2" label="医院生(红色代表：删除，绿色：添加)">
        <template #default="scope">
          <div :id="`doctor_case${scope.row.or_id}`" v-html="scope.row.doctor_case_text"></div>
        </template>
      </el-table-column>

      <el-table-column label="错误原因">
        <template #default="scope">
          <el-input type="text" @keydown.enter="saveMark(scope.row)" v-model="scope.row.mark"></el-input>
        </template>
      </el-table-column>

<!--      <el-table-column label="确认状态" width="120px">-->
<!--        <template #default="scope">-->
<!--          {{scope.row.doctor_signature}}-->
<!--        </template>-->
<!--      </el-table-column>-->

      <el-table-column label="上传状态" width="100px"
           :filters="[
            { text: '未执行',value:0},
            { text: '完成',value:2},
            { text: '失败',value:3},
          ]"
           :filter-method="filterStatus"
           filter-placement="bottom-end"
      >
        <template #default="scope">
          <span v-if="scope.row.ai_case_status===0">未执行</span>
          <span class="success"  v-if="scope.row.ai_case_status===2">完成</span>
          <span class="error" v-if="scope.row.ai_case_status===3">失败</span>
        </template>
      </el-table-column>

      <el-table-column label="病例时间" width="100px">
        <template #default="scope">
          <span>{{scope.row.docment_datetime}}</span>
        </template>
      </el-table-column>

      <el-table-column label="书写时间" width="100px">
        <template #default="scope">
          <span>{{scope.row.edit_time}}</span>
        </template>
      </el-table-column>
      <el-table-column label="病种" width="100px">
        <template #default="scope">
            {{scope.row.disease}}
        </template>
      </el-table-column>

      <el-table-column
          label="医生"
          width="80px"
          prop="doctor_name"
          :filters="[
            { text: '罗叶', value: '罗叶' },
            { text: '张艳', value: '张艳' },
            { text: '何静', value: '何静' },
            { text: '任凤', value: '任凤' },
          ]"
         :filter-method="filterTag"
         filter-placement="bottom-end"
      >
        <template #default="scope">
          {{scope.row.doctor_name}}
        </template>
      </el-table-column>
      <el-table-column
            label="是否查阅"
            :filters="[
              {text:'未查阅',value:2},
              {text:'已查阅',value:1},
            ]"
           :filter-method="filterSign"
            filter-placement="bottom-end"
           width="100px"
        >
        <template #default="scope">
          <span v-if="scope.row.doctor_sign===1">已查阅</span>
          <span v-else>未查阅</span>
        </template>
      </el-table-column>
      <el-table-column label="病人状态" width="100px"
            prop="is_abnormal"
           :filters="[
            { text: '正常', value: false },
            { text: '异常', value: true },
          ]"
         :filter-method="filterAbnormal"
         filter-placement="bottom-end"
      >
        <template #default="scope">
          <el-button type="success" v-if="!scope.row.is_abnormal">正常</el-button>
          <el-button type="danger" @click="abnormal(scope.row.orders)" v-if="scope.row.is_abnormal">异常</el-button>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200px">
        <template #default="scope">
          <el-button type="primary" v-if="date_value===date_v" size="small" @click="write(scope.row)">生成病例</el-button>
          <el-button type="warning" size="small" @click="reverse_write(scope.row)">反向对比</el-button>
         <br><br>
          <el-button type="danger" size="small" @click="see_tag(scope.row)">查看标签</el-button>
          <el-button type="success" size="small" @click="generate_tag(scope.row)">生成标签</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog
        v-model="dialogVisible"
        title="病例记录"
        width="80%"
        draggable
    >
      <template #default>
        <div class="content" v-loading="listings_load">
          <div class="list-item" v-for="(item,index) in listings" :key="index">
            <h4>{{item.document_type}}</h4>
            <div class="text">{{item.document_content}}</div>
          </div>
        </div>
        <el-table :data="advice_list" v-loading="advice_load" style="width: 100%;height: 200px">
          <el-table-column type="index" width="50" />
          <el-table-column prop="order_content" label="内容" />
          <el-table-column prop="order_exe_time_start" label="开始时间"/>
          <el-table-column prop="order_exe_time_end" label="结束时间" />
        </el-table>
        <div>
          <el-pagination background :current-page="page" layout="prev, pager, next" @current-change="tabClick" :page-size="1" :total="parseInt(select_row.pvid)" />
        </div>
      </template>
    </el-dialog>

    <el-dialog
        v-model="show_see"
        title="医嘱内容"
        width="80%"
        draggable
    >
        <div>
          {{orders}}
        </div>
<!--      <el-table :data_list="see_list" style="width: 100%">-->
<!--        <el-table-column prop="order_content" label="内容" />-->
<!--        <el-table-column prop="order_exe_time_start" label="开始时间"/>-->
<!--        <el-table-column prop="order_exe_time_end" label="结束时间" />-->
<!--      </el-table>-->
    </el-dialog>

    <el-dialog
        v-model="show_count_see"
        :title="`统计日期${date_value}`"
        width="80%"
        draggable
    >
      <div>
        <table style="width: 100%;border: 1px solid #000000;">
          <tr class="tr" style="border: 1px solid #000000">
            <th>医生名称</th>
            <th>书写患者人数</th>
            <th>不修改人数</th>
            <th>修改人数</th>
            <th>10字以内</th>
            <th>10-50字</th>
            <th>50-100字</th>
            <th>100字以上</th>
            <th>添加字数</th>
            <th>删除字数</th>
            <th>移动字数</th>
            <th>总字数</th>
            <th>修改率</th>
          </tr>
          <tr class="tr" style="text-align: center;margin-bottom: 15px" v-for="(item,index) in count_list" :key="index">
            <td>{{index}}</td>
            <td>{{item.num}}人</td>
            <td>{{item.no_modify}}人</td>
            <td>{{item.yes_modify}}人</td>
            <td>{{item.modify_10}}人</td>
            <td>{{item.modify_10_50}}人</td>
            <td>{{item.modify_50_100}}人</td>
            <td>{{item.modify_100}}人</td>
            <td style="color: #67c23a;font-weight: bold;">{{item.count_add_num}}</td>
            <td style="color: red;font-weight: bold;">{{item.count_del_num}}</td>
            <td style="color: red;font-weight: bold;">{{item.move_num}}</td>
            <td style="color: #409eff;font-weight: bold;">{{item.count_text_mum}}</td>

            <td style="color: #409eff;font-weight: bold;">{{(((item.count_del_num+item.count_add_num)/item.str_ai_doctor_num)*100).toFixed(2)}}%</td>
          </tr>
          <tr class="tr trbg" style="text-align: center;font-weight: bold;border-top: 2px solid green">
            <td>统计</td>
            <td>{{count_num.user_num}}人</td>
            <td>{{count_num.no_modify}}人</td>
            <td>{{count_num.no_modify>0?count_num.user_num - count_num.no_modify:0}}人</td>

            <td>{{count_num.modify_10}}人</td>
            <td>{{count_num.modify_10_50}}人</td>
            <td>{{count_num.modify_50_100}}人</td>
            <td>{{count_num.modify_100}}人</td>

            <td>{{count_num.count_add_num}}</td>
            <td>{{count_num.count_del_num}}</td>
            <td>{{count_num.count_move_num}}</td>
            <td>{{count_num.count_text_mum}}</td>
            <td>{{count_num.rate}}%</td>
          </tr>
        </table>
      </div>
    </el-dialog>

    <el-dialog
        v-model="show_tag"
        title="标签内容"
        width="80%"
        draggable
    >
      <el-button type="primary" @click="addTag">添加标签</el-button>
      <el-table border v-loading="load" :data="tag_list" style="width: 100%;height: 500px;">
        <el-table-column prop="type" width="200px" label="类型" />
        <el-table-column label="标签" #default="scope">
            <div class="tag-list" v-for="item in scope.row.list">
              <el-input style="width: 200px;" type="text" v-model="item.label" ></el-input>：
              <el-input style="width: calc(100% - 300px)" type="text" v-model="item.content" ></el-input>
<!--              <strong>{{item.label}}:</strong>{{item.content}}-->
              <el-button link type="primary" @click="saveEditTag(item,'edit')">保存</el-button>
              <el-button link type="danger" @click="saveEditTag(item,'del')">删除</el-button>
            </div>
        </el-table-column>
      </el-table>
      <div v-for="(item,index) in tag_list" :key="index">
        <div v-for="r in item.list">
          <span>{{r.label}}:</span>
          <span>{{r.content}}</span>
        </div>
      </div>
    </el-dialog>

  <el-dialog
      v-model="show_disease"
      title="病种"
      width="75%"
      draggable
  >
    <div style="width: 100%;height:400px;overflow-y: scroll">
      <div v-for="(item,index) in disease" :key="index">
        {{item.name}} <strong>({{item.num}}人)</strong>
      </div>
    </div>
  </el-dialog>

  <el-dialog
      v-model="create_show"
      title="添加标签"
      width="75%"
      draggable
  >
    <div>
      <el-form-item label="选择类型">
        <el-select
            v-model="form.document_type"
            placeholder="选择类型"
        >
          <el-option
              v-for="item in type_list"
              :key="item"
              :label="item"
              :value="item"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="标签名称">
        <el-input v-model="form.name"></el-input>
      </el-form-item>
      <el-form-item label="标签内容">
        <el-input type="textarea" v-model="form.content"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="submitTagForm" v-loading="load">立即创建</el-button>
      </el-form-item>
    </div>
  </el-dialog>


  <el-dialog
      v-model="generate_tag_show"
      :title="`生成标签【${select_row.patient_name}】`"
      width="90%"
      draggable
  >
    <div v-loading="generate_tag_show_load" style="height: 600px;overflow-y: scroll;padding-left: 5px;">
      <div style="margin-bottom: 15px;">
        <el-button :loading="is_sc" type="primary" size="small" @click="generateCase">生成标签</el-button>
        <span style="margin-right: 15px;"></span>
<!--        <el-button :loading="is_he" type="primary" size="small" @click="mergeTag">合并标签</el-button>-->
        <el-button :loading="save_tag_load" type="primary" size="small" @click="saveMergeTag">保存标签</el-button>
      </div>
      <el-timeline>
        <el-timeline-item
            :timestamp="item.name+item.document_time"
            placement="top"
            v-for="(item,index) in generate_tag_list"
            :key="index"
            :color="item.color"
        >
          <el-card>
            <div>
              {{item.text}}
            </div>
          </el-card>
        </el-timeline-item>
      </el-timeline>
    </div>
  </el-dialog>



</template>

<script setup>
  import {ref} from 'vue';
  import {diffWords } from 'diff';
  import {message,confirm} from "@/utils/message";
  import {post} from '@/utils/request.js'

  import {typeOptions} from "@/utils/typeOptions";

  let today = new Date()
  const year = today.getFullYear();
  const month = today.getMonth() + 1; // 月份从 0 开始，需要加 1
  const day = today.getDate();
  const show_see = ref(false)
  const see_list = ref([])
  const show_tag = ref(false)
  const tag_list = ref([])
  const orders = ref("")
  const create_show = ref(false)
  const generate_tag_show_load = ref(false)
  const form = ref({
    name:"",
    content:"",
    document_type:""
  })

  const options = ref(typeOptions.options)

  const date_v = `${year}-${month<10?'0'+month:month}-${day<10?'0'+day:day}`
  const date_value = ref(date_v)

  import c from "@/utils/config";
import { baseUrl } from '../utils/baseUrl';
  const api_url = c.api_url;


  const load = ref(false)
  const dialogVisible = ref(false)
  const listings = ref([])
  const listings_load = ref(false)
  const page = ref(1)
  const select_row = ref({})
  const tableData = ref([])
  const select_value = ref("")
  const success_num = ref(0)
  const error_num = ref(0)
  const no_num = ref(0)
  const info_num = ref(0)
  const generate_num = ref(0)

  const doctor_list = ref({});
  const count_list = ref([])
  const show_count_see = ref(false);

  const count_num = ref({})
  const sellect_type = ref("日常病程记录")
  const info_list_arr = ref([])
  const show_advice = ref(false)
  const advice_list = ref([])
  const advice_load = ref(false)
  const generate_tag_show = ref(false)

  const generate_tag_list = ref([])
  const hebing = ref("")
  const index_s = ref(0)

  const is_sc = ref(false)
  const is_he = ref(false)
  const save_tag_load = ref(false)



  // 保存标签
  const saveMergeTag = ()=>{
    let list = generate_tag_list.value;
    let row = list[list.length-1]
    if(row.name==='合并'){
      try {
        load.value = true;
        let info = JSON.parse(row.text)
        let arr = [];
        for(let key in info){
          arr.push({
            "pid":select_row.value.or_id,
            "pvid": select_row.value.pvid,
            "patient_name":select_row.value.patient_name,
            "label":key,
            "document_type":"日常病程记录",
            "content":info[key].join(","),
            "is_effective": 0
          })
        }
        post(`${baseUrl.aip_python_url}/templates/patientLabels/batch_create_labels/`, JSON.stringify({labels_list:arr})).then(res => {
          load.value = false;
          message("保存成功！",'success')
        }).catch(error => {
          load.value = false;
          console.error('发生错误:', error);
        });
      }catch (e) {

      }
      //,JSON.parse(row)
    }
  }
  const hetag = ()=>{
      let str  = ``
      if(index_s.value===0){
        let row1 = generate_tag_list.value[index_s.value]
        let row2 = generate_tag_list.value[index_s.value+1]
        str+=`${row1['document_time']}\n\r${row1['text']}`
        str+=`${row2['document_time']}\n\r${row2['text']}`
      }

      if(index_s.value>0){
        str+=hebing.value
        let row2 = generate_tag_list.value[index_s.value+1]
        str+=`${row2['document_time']}\n\r${row2['text']}`
      }

      generate_tag_list.value.push({
        color:"",
        name:"合并",
        text:"",
        document_time:"",
        content:str
      })

      // str = tag_hebing.replace("{tag_list}",str)
      // llm(generate_tag_list.value.length-1,str,true)
  }

  const mergeTag = ()=>{
      index_s.value=0;
      hebing.value = ""
      is_he.value = true
      hetag()
  }

  const generateCase = ()=>{
    is_sc.value = true;
    generate_tag_list.value.forEach((item,index)=>{
      // let str = tag_text.replace("{入院记录精神检查}",item.content)
      // llm(index,str)
    })
  }

  const llm = (index,content,off)=>{
    let url = `${baseUrl.url4}/center/llm`
    let data_list = {
      messages:[
        {
          role:"user",
          content:content
        }
      ]
    }

    post(url, JSON.stringify(data)).then(res => {
      let data_list = res.data;
      generate_tag_list.value[index]['text'] = data
      generate_tag_list.value[index]['color'] = "#67C23A"
      if(off){
        hebing.value = data
        index_s.value+=1
        if(index_s.value<=2){
          hetag()
        }else{
          is_he.value = false
          index_s.value = 0;
        }
      }else{
       let success = generate_tag_list.value.filter((item)=>!item.text)
        if(success.length===0){
          is_sc.value = false;
          mergeTag()
        }
      }
    }).catch(error => {
      is_sc.value = false;
      console.error('发生错误:', error);
    });
  }
  // 生成标签
  const generate_tag = (row)=>{
    generate_tag_list.value=[]
    generate_tag_show.value = true;
    generate_tag_show_load.value = true;
    select_row.value = row
    let pid = row.or_id;
    let pvid = row.pvid
    let url = `${api_url}/AiCase/getPatientCase`
    post(url, JSON.stringify({pid,pvid})).then(res => {
      let list = res.data
      generate_tag_show_load.value = false;
      let arr = []
      list.forEach((item)=>{
        let content = item.document_content
        let sin_name = content.split("医师签名")[1].replace(/：/g, '').split("，")[0]
        if(item.document_type==='入院记录(合作)'){
          let str = content.split('【精神检查】')
          content = str[1].split('辅助检查')[0]
        }
        if(item.document_type==='首次病程记录'){
          let str = content.split('精神检查：')
          content = str[1].split('辅助检查')[0]
        }
        if(sin_name){
          arr.push({
            color:"",
            name:item.document_type,
            text:"",
            document_time:item.document_time,
            content:content
          })
        }
      })
      console.log(arr)
      arr.sort((a, b) => new Date(a.document_time) - new Date(b.document_time));
      generate_tag_list.value = [...[arr[0]],...arr.slice(-3)]

    }).catch(error => {
      generate_tag_show_load.value = false;
      console.error('发生错误:', error);
    });

  }

  const see_advice = (pid,pvid)=>{
    advice_list.value=[]
    post('${api_url}/patient/getYz', JSON.stringify({pid,pvid})).then(res => {
      advice_list.value = res.map((item)=>{
        let {
          order_content,
          order_drask,
          order_once_qunt,
          order_total_qunt,
          order_once_qunt_unit,
          order_exe_time_start,
          order_exe_time_end,
        } = item
        return {
          order_content,
          order_drask,
          order_once_qunt,
          order_total_qunt,
          order_once_qunt_unit,
          order_exe_time_start,
          order_exe_time_end
        }
      })
      advice_load.value = false;
    }).catch(error => {
      advice_load.value = false
      console.error('发生错误:', error);
    });

  }

  const isBtn = ref(false);
  if(localStorage.getItem("batch_case")){
    isBtn.value = true;
  }

  // 保存修改
  const saveEditTag = (row,type)=>{
    let {id,pvid,pid,content,label } = row
    if(type==='edit'){
        let postData = {
          "pid": pid,
          "pvid": pvid,
          "label": label,
          "content": content,
        }
        load.value = true
        post(`${baseUrl.aip_python_url}/templates/patientLabels/${id}/`, JSON.stringify(postData)).then(res => {
          if (!res.ok) {
            throw new Error('网络响应错误: ' + res.statusText);
          }
          return res.json();
        }).then(data_list => {
          create_show.value = false
          load.value = false
          message("保存成功！",'success')
          load.value = false
        }).catch(error => {
          load.value = false
          console.error('请求失败:', error);
        });
    }

    if(type==='del'){
      confirm("确认要删除吗？","删除").then((index)=>{
          load.value = true
          post(`${baseUrl.aip_python_url}/templates/patientLabels/${id}/`, {}).then(res => {
            if (!res.ok) {
              throw new Error('网络响应错误: ' + res.statusText);
            }
            return res.json();
          }).then(data_list => {
            create_show.value = false
            load.value = false
            see_tag(select_row.value)
            message("删除成功！",'success')
            load.value = false
          }).catch(error => {
            load.value = false
            console.error('请求失败:', error);
          });
      })
    }
  }

  const abnormal= (des)=>{
    orders.value = des
    show_see.value = true;
  }

  const addTag = ()=>{
    form.value = {
      name:"",
      content:"",
      document_type:""
    }
    create_show.value = true;
  }

  const submitTagForm = ()=>{
    if(form.value.document_type===""){
      message("选择标签类型！")
      return
    }
    if(form.value.content===""){
      message("内容不能为空！")
      return
    }
    if(form.value.name===""){
      message("标签名称不能为空！")
      return
    }
    let {
      pvid,
      or_id,
      patient_name,
    } = select_row.value
    let postData = {
      "pid": or_id,
      "pvid": pvid,
      "patient_name":patient_name,
      "label": form.value.name,
      "document_type": form.value.document_type,
      "content": form.value.content,
      "is_effective": 0
    }
    load.value = true
    post(`${baseUrl.aip_python_url}/templates/patientLabels/`, JSON.stringify(postData)).then(res => {
      if (!res.ok) {
        throw new Error('网络响应错误: ' + res.statusText);
      }
      return res.json();
    }).then(data_list => {
      create_show.value = false
      load.value = false
      see_tag(select_row.value)
      message("保存成功！",'success')
      load.value = false
    }).catch(error => {
      load.value = false
      console.error('请求失败:', error);
    });
  }


  // 选择类型
  const selectType = ()=>{
    select_list()
  }
  const show_disease = ref(false)
  const disease = ref([])
  //查看病种
  const seeDisease = ()=>{
    let list = []
    // 精神分裂症
    tableData.value.forEach((row)=>{
      let text = removeLineBreaks(row.disease)
      text = text.replace(/\s+/g, '')
      let index = list.findIndex((item)=>item.name===text)
      if(index===-1){
        list.push({name:text.replace(/\s+/g, ''), num:1 })
      }else{
        let item = list[index]
        item.num+=1
        list[index] = item
      }
    })
    show_disease.value = true
    disease.value = new Set(list)
  }
  // 删除字符串中的r
  const removeLineBreaks = (str)=>{
    return str.replace(/[\r\n]+/g, ''); // 替换所有 \r\n 为 ''
  }
  //查询标签
  const see_tag = (row)=>{
    select_row.value = row;
    show_tag.value =  true;
    // row.or_id = 123
    let url =`${baseUrl.aip_python_url}/templates/patientLabels/?pid=${row.or_id}`
    load.value = true
    post(url).then(res => {
      let row = res.data[0]
      let list = [];
      for(let key in row){
        if(key!=='patient_name'){
          list.push({
            type:key,
            list:row[key]
          })
        }
      }
      tag_list.value = list;
      load.value = false
    }).catch(error => {
      load.value = false
      console.error('发生错误:', error);
    });
  }

  const filterModify = (value, row)=>{
    return row.is_yes_modify === value
  }

  const filterSign = (value, row)=>{
    return row.doctor_sign === value
  }
  const filterTag = (value, row) => {
    return row.doctor_name === value
  }

  const filterStatus = (value, row)=>{
    return row.ai_case_status === value
  }

  const filterAbnormal = (value, row)=>{
    return row.is_abnormal === value
  }

  const refresh = ()=>{
      getList()
  }

  const type_list=ref([
    // "入院记录(合作)",
    // "入院记录(不合作)",
    // "首次病程记录",
    // "上级医师查房记录",
    "日常病程记录",
    // "阶段小结",
    // "交班记录",
    // "接班记录",
    // "出院记录",
  ])

  //统计数据
  const extractTimeFromChineseDate = (str)=>{
    let regex = /(\d{4})年(\d{2})月(\d{2})日(\d{2})时(\d{2})分/; // 匹配 YYYY年MM月DD日HH时MM分 格式
    let match = str.match(regex);
    if (match) {
      let year = match[1];
      let month = match[2];
      let day = match[3];
      let hour = match[4];
      let minute = match[5];
      return `${year}年${month}月${day}日 ${hour}时${minute}`;
    } else {
      return null;
    }
  }

  const removeDateTimeFromString = (str)=>{
    // 正则表达式匹配日期时间格式，例如 "YYYY-MM-DD HH:MM:SS"
    return str.replace(/\d{4}年\d{1,2}月\d{1,2}日\d{1,2}时\d{1,2}分/g, '').trim();
  }
  const statisticPage = ()=>{

    tableData.value = tableData.value.map((item)=>{
      item.del_num = 0;
      item.add_num = 0;
      item.text_num = 0;
      item.no_modify = 0;
      item.yes_modify = 0;
      item.move_num = 0;
      item.str_ai_doctor_num = 0;
      item.modify_10 = 0;
      item.modify_10_50 = 0;
      item.modify_50_100 = 0;
      item.modify_100 = 0;
      return item;
    })

    let count_del_num = 0;
    let count_add_num = 0;
    let count_text_mum = 0;
    let user_num = 0;
    let no_modify = 0;
    let rate = 0;
    let count_move_num = 0;

    let modify_10=0;
    let modify_10_50=0;
    let modify_50_100 = 0;
    let modify_100 = 0;

    let arr = {}
    tableData.value.filter((row)=>row.doctor_case_text).map((item)=>{
       let doc = document.getElementById(`doctor_case${item.or_id}`)
       let set2 = item.doctor_case
      if(doc){
         let add = doc.querySelectorAll(".diff-added")
         let del = doc.querySelectorAll(".diff-removed")

         let add_str_arr = []
         let del_str_arr = []
         let add_str = []
         let del_str = []

         item.text_num = item.ai_case_edit.length;

         del.forEach((obj)=>{
           // item.del_num+=obj.innerText.length
           del_str_arr.push(obj.innerText)
         })

         add.forEach((obj,i)=>{
           if(i>0&&i<(add.length-1)){
             // item.add_num+=obj.innerText.length
             add_str_arr.push(obj.innerText)
           }
         })

         add_str = add_str_arr.filter((row)=>{
           return del_str_arr.indexOf(row)===-1
         })

         del_str = del_str_arr.filter((row)=>{
            return add_str_arr.indexOf(row)===-1
         })

         add_str = add_str.join("").replace(/\s/g, '').replace(/[，。！？、\s]/g, '')
         del_str = del_str.join("").replace(/\s/g, '').replace(/[，。！？、\s]/g, '')

         item.add_num+=add_str.length
         item.del_num+=del_str.length

         let move_str= add_str_arr.filter(value => del_str_arr.includes(value));
         item.move_num = move_str.join("").replace(/\s/g, '').length
         set2 = set2.replace(/\s/g, '')
         if(item.ai_case_edit.replace(/\s/g, '')===set2){
           if(set2){
             item.no_modify+=1
             no_modify+=1
           }
         }else{
           if(set2){
             let len = item.add_num+item.del_num
             item.yes_modify+=1
             if(len<10){
               item.modify_10+=1
               modify_10+=1;
             }else if(len>=10&&len<50){
               item.modify_10_50+=1
               modify_10_50+=1;
             }else if(len>=50&&len<100){
               item.modify_50_100+=1
               modify_50_100+=1;
             }else if(len>=100){
               item.modify_100+=1;
               modify_100+=1;
             }
           }
         }

         item.str_ai_doctor_num = item.ai_case_edit.length+set2[0].replace(/\s/g, '').length

         count_del_num+=item.del_num;
         count_add_num+=item.add_num;
         count_text_mum+=item.text_num
         user_num+=1
         count_move_num+=item.move_num

         if(arr[item.doctor_name]){
           let r = arr[item.doctor_name]
           arr[item.doctor_name] = {
             num:r.num+=1,
             count_del_num:r.count_del_num+item.del_num,
             count_add_num:r.count_add_num+item.add_num,
             count_text_mum:r.count_text_mum+item.text_num,
             no_modify:r.no_modify+item.no_modify,
             yes_modify:r.yes_modify+item.yes_modify,
             str_ai_doctor_num:r.str_ai_doctor_num+item.str_ai_doctor_num,
             move_num:r.move_num+item.move_num,
             modify_10:item.modify_10+r.modify_10,
             modify_10_50:item.modify_10_50+r.modify_10_50,
             modify_50_100:item.modify_50_100+r.modify_50_100,
             modify_100:item.modify_100+r.modify_100,
           }
         }else{
           arr[item.doctor_name] = {
             num:1,
             count_del_num:item.del_num,
             count_add_num:item.add_num,
             count_text_mum:item.text_num,
             no_modify:item.no_modify,
             yes_modify:item.yes_modify,
             str_ai_doctor_num:item.str_ai_doctor_num,
             move_num:item.move_num,
             modify_10:item.modify_10,
             modify_10_50:item.modify_10_50,
             modify_50_100:item.modify_50_100,
             modify_100:item.modify_100,
           }
         }
       }
      return item
    })
    console.log(arr)
    count_list.value = arr;
    for (let key in arr){
      let item = arr[key]
      rate+=(item.num/user_num)*((item.count_del_num+item.count_add_num)/item.str_ai_doctor_num)
    }

    count_num.value = {
      count_del_num,
      count_add_num,
      count_text_mum,
      user_num,
      no_modify,
      count_move_num,
      rate:(rate*100).toFixed(2),
      modify_10,
      modify_10_50,
      modify_50_100,
      modify_100
    }

    show_count_see.value = true
  }

  // 删除上传病例
  const clearAdvice = (row)=>{
    let url = `${baseUrl.hos_zhixuee}/patient/deleteAiCase`
    load.value = true
    post(url, JSON.stringify({
      ai_case_id:row.ai_case_id
    })).then(res => {
      load.value = false
      getList()
    }).catch(error => {
      load.value = false
      console.error('发生错误:', error);
    });
  }
  // 批量生成病例
  const batch_case = ()=>{
    localStorage.setItem("batch_case",true)
    let url = `${baseUrl.aip_python_url}/batchCreateMedical`
    let list = tableData.value.filter((item)=>item.ai_case==="").map((item)=>{
      return {
        id:item.or_id,
        is_abnormal:item.is_abnormal,
        last_time:item.edit_time,
        orders:item.orders
      }
    })
    // console.log(list)
    // return
    load.value = true
    post(url, JSON.stringify({
      list
    })).then(res => {
      load.value = false
    }).catch(error => {
      load.value = false
      console.error('发生错误:', error);
    });
  }
  // 保存备注
  const saveMark = (row)=>{
    let url = `${baseUrl.hos_zhixuee}/patient/updateAiCase`
    load.value = true
    post(url, JSON.stringify({
      ai_case_id:row.ai_case_id,
      mark:row.mark
    })).then(res => {
      load.value = false
    }).catch(error => {
      load.value = false
      console.error('发生错误:', error);
    });

  }
  // 保存编辑
  const saveEdit = (row,type)=>{
     load.value = true;
     let text = document.getElementById('edit_text_'+row.or_id)
    let url = "${baseUrl.hos_zhixuee}/patient/updateAiCase"
    load.value = true
    post(url, JSON.stringify({
      ai_case_id:row.ai_case_id,
      document:text.innerText
    })).then(res => {
      load.value = false
    }).catch(error => {
      load.value = false
      console.error('发生错误:', error);
    });
  }

  const seeAdvice = (list)=>{
    show_see.value = true
    see_list.value = list
  }
  // 更新病例
  const see_new = ()=>{
    let url=`${baseUrl.aip_python_url}/updateHospitalData`
    load.value = true
    post(url, JSON.stringify({})).then(res => {
      load.value = false
      // getList()
    }).catch(error => {
      load.value = false
      console.error('发生错误:', error);
    });
  }
  //反向对比
  const reverse_write = (row)=>{
    row.isReverse=!row.isReverse
    let str1 = row.ai_case_edit;
    let str2 = row.doctor_case;
    if(str2){
      let arr_s = row.doctor_case.split("【主要健康问题】主要健康问题")
      str2 = arr_s[1].split("医师签名")[0]
    }
    if(str1&&str2){
      let {result1,result2} = markDifferences(str1,str2)
      if(row.isReverse){
        row.ai_case_text =result1;
        row.doctor_case_text = str2
      }else{
        row.ai_case_text =str1;
        row.doctor_case_text = result2
      }
    }
  }
  // 分页查询
  const tabClick = (index)=>{
    page.value = index
    getPatientCase(select_row.value.or_id,index)
  }
  // 查看病例
  const see = (row)=>{
    select_row.value = row;
    getPatientCase(row.or_id,row.pvid)
  }
  // 获取病例内容
  const getPatientCase = (pid,pvid)=>{
    page.value = parseInt(pvid);
    listings_load.value = true
    advice_load.value = true;
    dialogVisible.value = true
    let url = `${api_url}/AiCase/getPatientCase`
    post(url, JSON.stringify({pid,pvid})).then(res => {
      listings.value = res.data;
      listings_load.value = false
      see_advice(pid,pvid)
    }).catch(error => {
      listings_load.value = false
      console.error('发生错误:', error);
    });
  }
  // 一键书写
  const writeContent = ()=>{
    load.value = true
    let url = `${api_url}/patient/startExe`
    post(url, JSON.stringify({})).then(res => {
      load.value = false
    }).catch(error => {
      load.value = false
      console.error('发生错误:', error);
    });
  }
  // 书写
  const write = (row)=>{
    let post_data = {
      id:row.or_id,
      last_time:row.edit_time,
      is_abnormal:row.is_abnormal,
      orders:row.orders
    }
    load.value = true
    post(`${baseUrl.aip_python_url}/createMedical`, JSON.stringify(post_data)).then(res => {
        load.value = false
        let info = res.data
        row.ai_case =  info.ai_case
        row.ai_case_text =  info.ai_case
        row.ai_case_edit = info.ai_case
        row.ai_case_id = info.ai_case_id
    }).catch(error => {
      load.value = false
      console.error('发生错误:', error);
    });
  }
  const selectDate = (value)=>{
    getList()
  }
  const select_list = ()=>{
    let info_list = info_list_arr.value[sellect_type.value]
    load.value = false
    no_num.value= 0;
    success_num.value=0;
    error_num.value=0;
    info_num.value=0;
    doctor_list.value = {}
    generate_num.value = 0;
    if(!info_list){
      tableData.value=[]
      return
    }
    let success = []
    let error = []
    tableData.value = info_list.map((item)=>{
      item.doctor_sign = 2
      let datetime="",set2=""
      if(item.doctor_case){
        datetime = extractTimeFromChineseDate(item.doctor_case)
        set2 = removeDateTimeFromString(item.doctor_case).split("医师签名")
        if(set2[1].replace(/：/g, '').split("，")[0]){
          item.doctor_sign =1
        }
      }

      item.docment_datetime = datetime
      item.doctor_case = set2[0];

      let str1 = item.ai_case_edit;
      let str2 = item.doctor_case;
      let str3 = item.ai_case;

      item.del_num = 0;
      item.add_num = 0;
      item.text_num = 0;
      item.no_modify = 0;
      item.yes_modify = 0;
      item.move_num = 0;
      item.str_ai_doctor_num = 0;
      item.modify_10 = 0;
      item.modify_10_50 = 0;
      item.modify_50_100 = 0;
      item.modify_100 = 0;
      item.is_yes_modify = 1;

      if(str2&&(str1!==str2.replace(/\s+/g, '')&&str2)){
        item.is_yes_modify = 2
      }

      if(str3){
        generate_num.value+=1
      }
      if(item.ai_case_status===0){
        no_num.value+=1
      }
      if(item.ai_case_status===2){
        success_num.value+=1
      }
      if(item.ai_case_status===3){
        error_num.value+=1
      }

      if(doctor_list.value[item.doctor_name]){
        doctor_list.value[item.doctor_name] =  doctor_list.value[item.doctor_name]+","+item.patient_name
      }else{
        doctor_list.value[item.doctor_name] =  item.patient_name
      }

      if(str1&&str2){
        let {result1,result2} = markDifferences(str1,str2)
        str2 = result2
      }
      if(str3&&str1){
        let {result1,result2} = markDifferences(str3,str1)
        str3 = result2
      }

      item.ai_case_text = str1;
      item.doctor_case_text = str2;
      item.ai_case_edit_text = str3;
      if(!item.is_abnormal){
        success.push(item)
      }else{
        error.push(item)
      }
      return item;
    })
    console.log(doctor_list.value)
    console.log(success.map((it)=>it.or_id))
  }
  // 获取列表
  const getList = ()=>{
    load.value = true
    let url = `${baseUrl.hos_zhixuee}/patient/patientList`
    // console.log(JSON.parse(localStorage.getItem("item")))
    post(url, JSON.stringify({
      date:`${date_value.value} 23:59:59`,
      type:sellect_type.value
    })).then(res => {
      info_list_arr.value = res.data
      // localStorage.setItem("item",JSON.stringify(res.data_list))
      select_list()
    }).catch(error => {
      load.value = false
      console.error('发生错误:', error);
    });
  }
  getList()
  const markDifferences = (str1, str2)=>{
    const diffResult = diffWords(str1, str2)
    const result1 = diffResult.map(part => {
      if (part.added) {
        return `<span class="diff-added">${part.value}</span>`;
      } else if (part.removed) {
        return `<span class="diff-removed">${part.value}</span>`;
      } else {
        return part.value;
      }
    }).join('');

    const result2 = diffResult.map(part => {
      if (part.added) {
        return `<span class="diff-added">${part.value}</span>`;
      } else if (part.removed) {
        return `<span class="diff-removed">${part.value}</span>`;
      } else {
        return part.value;
      }
    }).join('');

    return { result1, result2 };
  }

</script>

<style>
.tag-list{
    margin-bottom: 12px;
  padding-top: 12px;
}
.trbg{
  background: yellow;
}
.tr td,.tr th{
  border: 1px solid #000000;
  padding: 5px;
}

.success{
  color: #67c23a;
}
.error{
  color: red;
}
.filters{
  display: flex;
}
.text {
  white-space: pre-line; /* 或者使用 pre-wrap */
  border: 1px solid #eee;
  font-size: 14px;
  padding: 15px;
}
.content{
  height: 300px;
  overflow-y: auto;
}
  .date-box{
    padding: 15px;
  }
  .diff-added{
    color: green;
  }
  .diff-removed{
    color: red;
    text-decoration: line-through; /* 设置删除线 */
  }

</style>