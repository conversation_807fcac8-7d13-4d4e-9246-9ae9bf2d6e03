<template>
  <div class="date-box">
    <div class="filters">
      <div>
        <el-date-picker
            v-model="date_value"
            type="date"
            placeholder="书写日期"
            @change="selectDate"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            size="default"
        />
      </div>
    </div>
  </div>
  <div>
    <el-table v-loading="load" :data="tableData" height="800px" border style="width: 100%">
      <el-table-column type="index" width="50" />
      <el-table-column prop="patient_name" label="患者名称" width="120">
        <template #default="scope">
          <el-button type="primary" @click="see(scope.row)" text>{{scope.row.patient_name}}</el-button>
        </template>
      </el-table-column>
      <el-table-column label="病种">
        <template #default="scope">
          {{scope.row.disease}}
        </template>
      </el-table-column>
      <el-table-column
          label="医生"
          prop="doctor_name">
        <template #default="scope">
          {{scope.row.doctor_name}}
        </template>
      </el-table-column>
      <el-table-column label="病人状态">
        <template #default="scope">
          <el-button type="success" v-if="!scope.row.is_abnormal">正常</el-button>
          <el-button type="danger" @click="abnormal(scope.row.orders)" v-if="scope.row.is_abnormal">异常</el-button>
        </template>
      </el-table-column>
      <el-table-column label="操作">
        <template #default="scope">
          <el-button type="danger" size="small" @click="createCase(scope.row)">查看生成病例</el-button>
          <div style="height: 10px;"></div>
          <el-button type="primary" size="small" @click="mentalCase(scope.row)">提示词调试</el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>

  <el-dialog
      v-model="dialogVisible"
      title="病例记录"
      width="80%"
      draggable
  >
    <template #default>
      <div class="content" v-loading="listings_load">
        <div class="list-item" v-for="(item,index) in listings" :key="index">
          <h4>{{item.document_type}}</h4>
          <div class="text">{{item.document_content}}</div>
        </div>
      </div>
      <el-table :data="advice_list" v-loading="advice_load" style="width: 100%;height: 200px">
        <el-table-column type="index" width="50" />
        <el-table-column prop="order_content" label="内容" />
        <el-table-column prop="order_exe_time_start" label="开始时间"/>
        <el-table-column prop="order_exe_time_end" label="结束时间" />
      </el-table>
      <div>
        <el-pagination background :current-page="page" layout="prev, pager, next" @current-change="tabClick" :page-size="1" :total="parseInt(select_row.pvid)" />
      </div>
    </template>
  </el-dialog>

  <el-dialog
      v-model="createShow"
      :title="`生成病例【${select_value.patient_name}】`"
      width="90%"
      draggable
      @closed="closed"
  >
    <div style="height: 600px;overflow-y: scroll;padding-left: 5px;">

      <el-tabs v-model="activeName" class="demo-tabs">
        <el-tab-pane :label="item.name" :name="item.name" v-for="item in type_lists">
            <div style="margin-bottom: 15px;"><el-button type="primary" size="small" @click="generateWord(item)">生成病例</el-button></div>
            <div style="margin-bottom: 15px;" v-if="isTyep.indexOf(item.name)!==-1">
              <el-input type="textarea" placeholder="请输入语音内容" v-model="item.audio_text"></el-input>
            </div>
           <div style="padding-left: 10px">
              <el-timeline>
                <el-timeline-item
                    :timestamp="items.name"
                    placement="top"
                    v-for="items in item.list"
                    :color="items.text?'#67C23A':''"
                >
                  <el-card>
                    <div class="text">
                      {{items.text}}
                    </div>
                  </el-card>
                </el-timeline-item>
              </el-timeline>
              <img v-if="item.load_ai" src="../assets/svg/load2.svg" />
            </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </el-dialog>

  <el-dialog
      v-model="mentalShow"
      :title="`生成病例提示词调试【${select_value.patient_name}】`"
      width="90%"
      draggable
  >
    <div style="height: 700px;overflow-y: scroll;padding-left: 5px;">
      <div style="margin-bottom: 20px;"><el-button type="primary" size="small" @click="seeCase">生成病例</el-button></div>
      <el-timeline>
        <el-timeline-item
            timestamp="模版"
            placement="top"
        >
          <el-card>
            <el-input style="height: 100px;" v-model="temp_value" type="textarea"></el-input>
          </el-card>
        </el-timeline-item>

        <el-timeline-item
            timestamp="提示词1"
            placement="top"
        >
          <el-card>
            <el-input style="height: 300px;" type="textarea" v-model="word1"></el-input>
          </el-card>
        </el-timeline-item>

        <el-timeline-item
            timestamp="提示词2"
            placement="top"
        >
          <el-card>
            <el-input style="height: 300px;" type="textarea" v-model="word2"></el-input>
          </el-card>
        </el-timeline-item>
        <el-timeline-item
            timestamp="提示词3"
            placement="top"
        >
          <el-card>
            <el-input style="height: 200px;" type="textarea" v-model="word3"></el-input>
          </el-card>
        </el-timeline-item>

        <el-timeline-item
            timestamp="标签"
            placement="top"
        >
          <el-card>
            <div style="margin-bottom: 15px;">
             精神标签：{{jingsheng_tag}}
            </div>
            <div>
              先天：{{xt_tag}}
            </div>
          </el-card>
        </el-timeline-item>


        <el-timeline-item
            timestamp="第一次质控精神"
            placement="top"
            :color="step_1.color"
        >
          <el-card>
            <div class="text">
                {{step_1.word}}
            </div>
            <div v-loading="step_1.load">
                {{step_1.text}}
            </div>
          </el-card>
        </el-timeline-item>

        <el-timeline-item
            timestamp="第二次质控"
            placement="top"
            :color="step_2.color"
        >
          <el-card>
            <div class="text">
              {{step_2.word}}
            </div>
            <div v-loading="step_2.load">
              {{step_2.text}}
            </div>
          </el-card>
        </el-timeline-item>

        <el-timeline-item
            timestamp="第三次质控"
            placement="top"
            :color="step_3.color"
        >
          <el-card>
            <div class="text">
              {{step_3.word}}
            </div>
            <div v-loading="step_3.load">
              {{step_3.text}}
            </div>
          </el-card>
        </el-timeline-item>
      </el-timeline>
    </div>
  </el-dialog>

</template>

<script setup>
import {ref} from 'vue';
import {message,confirm} from "@/utils/message";
import { post, get } from '@/utils/request.js'
import c from "@/utils/config";
import { baseUrl } from '../utils/baseUrl';

const activeName = ref("入院记录(内转)")
let today = new Date()
const year = today.getFullYear();
const month = today.getMonth() + 1; // 月份从 0 开始，需要加 1
const day = today.getDate();
const show_see = ref(false)
const orders = ref("")
const mentalShow = ref(false)

const form = ref({
  name:"",
  content:"",
  document_type:""
})

const date_v = `${year}-${month<10?'0'+month:month}-${day<10?'0'+day:day}`
const date_value = ref(date_v)
const api_url = c.api_url;

const load = ref(false)
const dialogVisible = ref(false)
const listings = ref([])
const listings_load = ref(false)
const page = ref(1)
const select_row = ref({})
const tableData = ref([])
const select_value = ref("")

const isTyep = ref(['入院记录(合作)','入院记录(不合作)','日常病程记录(异常)','入院记录(复发)'])

const sellect_type = ref("日常病程记录")
const info_list_arr = ref([])
const show_advice = ref(false)
const advice_list = ref([])
const advice_load = ref(false)

const createShow = ref(false)
const temp_value = ref("")
const step_1 = ref({text:"",load:false,color:"",word:""})
const step_2 = ref({text:"",load:false,color:"",word:""})
const step_3 = ref({text:"",load:false,color:"",word:""})

const step_index = ref(0)
const jingsheng_tag = ref("")
const xt_tag = ref("")

const word1 = ref("")
const word2 = ref("")
const word3 = ref("")

const type_lists=ref([
  {name:"入院记录(内转)",text:"生成结果",list:[],load_list:[],load_index:0,load_time:null,load_ai:false},
  {name:"入院记录(复发)",text:"生成结果",list:[],load_list:[],load_index:0,load_time:null,load_ai:false,audio_text:""},
  {name:"首次病程记录(内转)",text:"生成结果",list:[],load_list:[],load_index:0,load_time:null,load_ai:false},
  {name:"三级医师查房(主治)",text:"生成结果",color:"",list:[],load_list:[],load_index:0,load_time:null,load_ai:false},
  {name:"三级医师查房(主任)",text:"生成结果",color:"",list:[],load_list:[],load_index:0,load_time:null,load_ai:false},
  {name:"上级医师查房记录",text:"生成结果",color:"",list:[],load_list:[],load_index:0,load_time:null,load_ai:false},
  {name:"出院查房记录",text:"生成结果",color:"",list:[],load_list:[],load_index:0,load_time:null,load_ai:false},
  {name:"阶段小结",text:"生成结果",color:"",list:[],load_list:[],load_index:0,load_time:null,load_ai:false},
  {name:"出院记录",text:"生成结果",color:"",list:[],load_list:[],load_index:0,load_time:null,load_ai:false},

  {name:"日常病程记录",text:"生成结果",color:"",list:[],load_list:[],load_index:0,load_time:null,load_ai:false},
  {name:"日常病程记录(异常)",text:"生成结果",color:"",list:[],load_list:[],load_index:0,load_time:null,load_ai:false},

  {name:"入院记录(合作)",text:"生成结果",color:"",list:[],load_list:[],load_index:0,load_time:null,load_ai:false,audio_text:""},
  {name:"入院记录(不合作)",text:"生成结果",color:"",list:[],load_list:[],load_index:0,load_time:null,load_ai:false,audio_text:""},
  {name:"首次病程记录",text:"生成结果",list:[],load_list:[],load_index:0,load_time:null,load_ai:false},
])

const closed = ()=>{
  type_lists.value = type_lists.value.map((item)=>{
    if(item.load_time){
      clearTimeout(item.load_time)
    }
    item.load_time = false;
    item.list = [];
    item.load_index = 0;
    item.load_ai = false;
    return item;
  })
}
// 其他流程输出
const loadJlTasks = (row,list)=>{
  let index = type_lists.value.findIndex((item)=>item.name===row.name)
  type_lists.value[index]['load_list'] = list
  row.load_time = setTimeout(async ()=>{
    let load_row = type_lists.value[index]
    let item = load_row['load_list'][load_row['load_index']]
    let push_item = null
    if(!item){
      load_row.load_ai = false
      load_row.load_list = [];
      load_row.load_time = null;
      load_row.load_index=0;
      return
    }
    if(item.text!==""){
      push_item = item
    }else{
      item.text =await loadJlTasksAi(item)
      push_item = item
    }
    if(push_item){
      type_lists.value[index]['list'].push(push_item)
      type_lists.value[index].load_index+=1
      loadJlTasks(row,list)
    }

  },1000)
}
const loadJlTasksAi = async(row)=>{
  if(row['list']&&row['list']["content"]){
    return await llms(row['list']["content"])
  }else{
    return row['list']?row['list']:"";
  }
}
// 入院记录
const loadRyjlTasks =(row,list)=>{
  let index = type_lists.value.findIndex((item)=>item.name===row.name)
  type_lists.value[index]['load_list'] = list
  row.load_time = setTimeout(async ()=>{
    let load_row = type_lists.value[index]
    let item = load_row['load_list'][load_row['load_index']]
    let push_item = null
    if(!item){
      load_row.load_ai = false
      load_row.load_list = [];
      load_row.load_time = null;
      load_row.load_index=0;
      return
    }
    if(item.text!==""){
      push_item = item
    }else{
      if(item.name==='现病史'){
        let text = await xbsAI(item);
        item.text = text.data
        push_item = item
      }

      if(item.name==='既往史'){
        let text = await jwsAI(item);
        item.text = text.data
        push_item = item
      }

      if(item.name==='体格检查'){
        item.text = await tgjcAI(item);
        push_item = item
      }

      if(item.name==='补充诊断'){
        item.text = "暂无"
        push_item = item;
      }
    }

    if(push_item){
      type_lists.value[index]['list'].push(push_item)
      type_lists.value[index].load_index+=1
      loadRyjlTasks(row,list)
    }

  },1000)
}
//体格检查
const tgjcAI = async (row)=>{
  let tgjc = row['list']
  let tgjc_text = await llms(tgjc["精神检查"]['content'])
  return tgjc_text+tgjc["辅助检查"]
}
// 既往史
const jwsAI = async (row)=>{
  let jws = row['list']
  let jws_text_1 =await llms(jws[0]['content'])
  let jws_text_2 =await llms(jws[1]['content'])
  return await haddleJws({
    str1:jws_text_1,
    str2:jws_text_2,
    jws:jws[0]['jws'],
    join_date:jws[0]['入院日期'],
    out_date:jws[0]['出院日期'],
    step:"1"
  })
}
// 现病史
const xbsAI =async (row)=>{
  let xbs = row['list']
  // 现病史
  let xbs_text =await llms(xbs[0]['content'])
  const step_1 =  await haddleXbs({
    str:xbs_text,
    xbs:xbs[0]['xbs'],
    join_date:xbs[0]['入院日期'],
    out_date:xbs[0]['出院日期'],
    step:"1"
  })
  xbs_text =await llms(xbs[1]['content'].replace("{xbs}",step_1.data))
  let step_2 =await llms(xbs[2]['content'])
  return  await haddleXbs({
    str:step_2,
    xbs:xbs_text,
    join_date:xbs[0]['入院日期'],
    out_date:xbs[0]['出院日期'],
    step:"2"
  })
}

const generateWord = (row)=>{
  let url =`${api_url}/prompt/getRecordPrompt`
  let data_list = {
    pid:select_value.value.or_id,
    document_type:row.name
  }

  if(isTyep.value.indexOf(row.name)!==-1){
    if(row.audio_text===""){
      message("请输出语音内容！")
      return;
    }
    data_list.audio_text = row.audio_text
  }

  if(row.load_ai) return
  row.list=[]
  row.load_index=0;
  row.load_time=null;
  row.load_ai=true;
  row.load_list = []

  post(url, JSON.stringify(data_list)).then(async res => {
    let row_text = ['出院查房记录','日常病程记录','上级医师查房记录','日常病程记录(异常)']
    if(row_text.indexOf(row.name)!==-1){
      row.load_ai = false;
      if(row.name==='日常病程记录(异常)'){
        let step_2 =await llms(res.data.content)
        row['list'].push({
          name:"结果",
          text:step_2,
          color:"#67C23A"
        })
      }else{
        row['list'].push({
          name:"结果",
          text:res.data,
          color:"#67C23A"
        })
      }
      return
    }

    let list = []
    for(let key in res.data){
      if(Array.isArray(res.data[key])){
        list.push({name:key,text:"",color:"",list:res.data[key],step:2})
      }else if(typeof res.data[key] === 'object'){
        list.push({name:key,text:"",color:"",list:res.data[key],step:1})
      }else{
        list.push({name:key,text:res.data[key],color:"#67C23A"})
      }
    }
    if(row.name==='入院记录(内转)'||row.name==='入院记录(复发)'){
      loadRyjlTasks(row,list)
    }else{
      loadJlTasks(row,list)
    }
  }).catch(error => {
    console.error('发生错误:', error);
  });
}

const haddleJws = async (data_list)=>{
  let url = `${api_url}/prompt/haddleJws`
  const result = await post(url, JSON.stringify(data))
  return result
}

const haddleXbs = async (data_list)=>{
  let url = `${api_url}/prompt/haddleXbs`
  const result = await post(url, JSON.stringify(data))
  return result
}

const llms = async (content)=>{
  let url = `${api_url}/center/llm`
  let data_list = {
    messages:[
      {
        role:"user",
        content:content
      }
    ]
  }
  const result = await post(url, JSON.stringify(data_list))
  return result
}


const stepCase1 = (content)=>{
  step_index.value = 2;
  step_3.value.load = true
  let word = word3.value.replace("{content}",step_2.value.text)
  step_3.value.word = word
  llm(word)
}

const stepCase = ()=>{
  step_index.value = 1;
  step_2.value.load = true
  let word = word2.value.replace("{tag_content}",xt_tag.value)
  word = word.replace("{患者病历记录}",step_1.value.text)
  step_2.value.word = word
  llm(word)
}
const seeCase = ()=>{
  step_index.value = 0;
  let temp_text = word1.value.replace("{temp_content}",temp_value.value)
  let url =`${baseUrl.aip_python_url}/templates/patientLabels/?pid=${select_value.value.or_id}`
  get(url).then(res => {
    let row = res.data[0]
    let list = [];
    for(let key in row){
      if(key!=='patient_name'){
        list.push({
          type:key,
          list:row[key]
        })
      }
    }
    let tag = '一般情况,认知活动,情感活动,意志行为,检查结果,治疗方案,注意事项'
    let arr = tag.split(",")
    let arr1 = {} // 精神检查标签
    let arr2 = {}
    list[0]['list'].forEach((item)=>{
     if(arr.indexOf(item.label)!==-1){
       arr1[item.label] = item.content.split(",")
     }else{
       arr2[item.label] = item.content.split(",")
     }
    })
    jingsheng_tag.value = JSON.stringify(arr1)
    xt_tag.value = JSON.stringify(arr2)
    temp_text = temp_text.replace("{tag_content}",jingsheng_tag.value)
    step_1.value['load'] = true
    step_1.value.word = temp_text
    llm(temp_text)
  }).catch(error => {
    console.error('发生错误:', error);
  });
}

const llm = (content)=>{
  let url = `${baseUrl.url4}/center/llm`
  let data_list = {
    messages:[
      {
        role:"user",
        content:content
      }
    ]
  }
  post(url, JSON.stringify(data_list)).then(text => {

    if(step_index.value===0){
      step_1.value['text'] = text;
      step_1.value['load'] = false
      step_1.value['color'] ="#67C23A"
      setTimeout(()=>{
        stepCase()
      },1000)
    }

    if(step_index.value===1){
      step_2.value['text'] = text;
      step_2.value['load'] = false
      step_2.value['color'] ="#67C23A"
      setTimeout(()=>{
        stepCase1()
      },1000)
    }

    if(step_index.value===2){
      step_3.value['text'] = text;
      step_3.value['load'] = false
      step_3.value['color'] ="#67C23A"
    }

  }).catch(error => {
    console.error('发生错误:', error);
  });
}


const mentalCase = (row)=>{
  select_value.value = row;
  mentalShow.value = true;
}

// 生成病例
const createCase =(row)=>{
  createShow.value = true;
  select_value.value = row;
}

const see_advice = (pid,pvid)=>{
  advice_list.value=[]
  post(`${api_url}/patient/getYz`, JSON.stringify({pid,pvid})).then(data_list => {
    advice_list.value = data_list.map((item)=>{
      let {
        order_content,
        order_drask,
        order_once_qunt,
        order_total_qunt,
        order_once_qunt_unit,
        order_exe_time_start,
        order_exe_time_end,
      } = item
      return {
        order_content,
        order_drask,
        order_once_qunt,
        order_total_qunt,
        order_once_qunt_unit,
        order_exe_time_start,
        order_exe_time_end
      }
    })
    advice_load.value = false;
  }).catch(error => {
    advice_load.value = false
    console.error('发生错误:', error);
  });

}

const isBtn = ref(false);
if(localStorage.getItem("batch_case")){
  isBtn.value = true;
}

const abnormal= (des)=>{
  orders.value = des
  show_see.value = true;
}


const disease = ref([])

// 分页查询
const tabClick = (index)=>{
  page.value = index
  getPatientCase(select_row.value.or_id,index)
}
// 查看病例
const see = (row)=>{
  select_row.value = row;
  getPatientCase(row.or_id,row.pvid)
}
// 获取病例内容
const getPatientCase = (pid,pvid)=>{
  page.value = parseInt(pvid);
  listings_load.value = true
  advice_load.value = true;
  dialogVisible.value = true
  let url = `${api_url}/AiCase/getPatientCase`
  post(url, JSON.stringify({pid,pvid})).then(res => {
    listings.value = res.data;
    listings_load.value = false
    see_advice(pid,pvid)
  }).catch(error => {
    listings_load.value = false
    console.error('发生错误:', error);
  });
}

const selectDate = (value)=>{
  getList()
}
const select_list = ()=>{
  let info_list = info_list_arr.value[sellect_type.value]
  load.value = false
  tableData.value = info_list.map((item)=>{
    return item;
  })
  localStorage.setItem("tableData",JSON.stringify(tableData.value))
}
// 获取列表
const getList = ()=>{
  // tableData.value = arr_list
  return;
  let item = localStorage.getItem("tableData")
  if(item){
    tableData.value  = JSON.parse(item)
    console.log(tableData.value)
    return
  }

  load.value = true
  let url = `${api_url}/patient/getListByWrite`
  url = `${baseUrl.aip_python_url}/patientList`
  // console.log(JSON.parse(localStorage.getItem("item")))
  post(url, JSON.stringify({
    date:`${date_value.value} 23:59:59`,
    type:sellect_type.value
  })).then(res => {
    info_list_arr.value = res.data
    select_list()
  }).catch(error => {
    load.value = false
    console.error('发生错误:', error);
  });
}
getList()

</script>

<style>
.items{
  position: relative;
}
.el-textarea__inner{
  height: 100%;
}
.text {
  white-space: pre-line; /* 或者使用 pre-wrap */
  margin-bottom: 15px;
}
.tag-list{
  margin-bottom: 12px;
  padding-top: 12px;
}
.trbg{
  background: yellow;
}
.tr td,.tr th{
  border: 1px solid #000000;
  padding: 5px;
}

.success{
  color: #67c23a;
}
.error{
  color: red;
}
.filters{
  display: flex;
}
.text {
  white-space: pre-line; /* 或者使用 pre-wrap */
  border: 1px solid #eee;
  font-size: 14px;
  padding: 15px;
}
.content{
  height: 300px;
  overflow-y: auto;
}
.date-box{
  padding: 15px;
}
.diff-added{
  color: green;
}
.diff-removed{
  color: red;
  text-decoration: line-through; /* 设置删除线 */
}

</style>