import { createRouter, createWebHashHistory } from 'vue-router';

const Index = () => import('@/pages/index.vue')
const CaseList = () => import('@/pages/case_list.vue')
const CaseAbnormalUser = () => import('@/pages/case_abnormal_user.vue')
const CueWord = () => import('@/pages/cue_word.vue')
const CaseWriteCount = () => import('@/pages/case_write_count.vue')
const CaseTemplate = () => import('@/pages/case_template.vue')
const CaseTest = () => import('@/pages/case_test.vue')
const WordTest = () => import('@/pages/word_test.vue')
const WordList = () => import('@/pages/word_list.vue')
const WordManage = () => import('@/pages/word_manage.vue')

const TagList = () => import('@/pages/tag_list.vue')
const TaskList = () => import('@/pages/task_list.vue')
const GenerateCases = () => import('@/pages/generate_cases.vue')
const NewGenerateCases = () => import('@/pages/new_generate_cases.vue')
const TaskUpload = () => import('@/pages/task_upload.vue')
const DiseaseList = () => import('@/pages/diseaseList.vue')
const HandCaseUpload = () => import('@/pages/handCaseUpload.vue')
const TestHis = () => import('@/pages/test_his.vue')
const Open = () => import('@/pages/open_ai.vue')
const Statistics = () => import('@/pages/statistics.vue')
const AiTest = () => import('@/pages/aiTest.vue')
const HisTask = () => import('@/pages/his_task.vue')
const DoctorList = () => import('@/pages/doctorList.vue')
const TagLibrary = () => import('@/pages/tagLibrary.vue')
const TagLibraryFuhe = () => import('@/pages/tagLibraryFuHeBq.vue')
const tagLibraryList = () => import('@/pages/tagLibraryList.vue')
const GenerateCaseList = () => import('@/pages/generate_case_list.vue')
const TagOperationHistory = () => import('@/pages/tag_operation_history.vue')
const AIButler = () => import('@/pages/ai_butler.vue')
const Tag = () => import('@/pages/tag.vue')
const writingChart = () => import('@/pages/writing_chart.vue')
const DrugManagement = () => import('@/pages/drug_management.vue')
const LogQuery = () => import('@/pages/log_query.vue')




const VITE_APP_ENV = import.meta.env.VITE_APP_ENV

let routes = [
    { path: '/', component: Index, name: 'Index', meta: { title: '首页', isLogin: true } },
    { path: '/HisTask', component: HisTask, name: 'HisTask', meta: { title: '测试', isLogin: true } },
    { path: '/CaseAbnormalUser', component: CaseAbnormalUser, name: 'CaseAbnormalUser', meta: { title: '异常病例', isLogin: true } },
    { path: '/CaseList', component: CaseList, name: 'CaseList', meta: { title: '病例列表', isLogin: true } },
    { path: '/CueWord', component: CueWord, name: 'CueWord', meta: { title: '提示调整', isLogin: true } },
    { path: '/CaseWriteCount', component: CaseWriteCount, name: 'CaseWriteCount', meta: { title: '统计页面', isLogin: true } },
    { path: '/CaseTemplate', component: CaseTemplate, name: 'CaseTemplate', meta: { title: '模版统计', isLogin: true } },
    { path: '/CaseTest', component: CaseTest, name: 'CaseTest', meta: { title: '演示内容', isLogin: true } },
    { path: '/WordTest', component: WordTest, name: 'WordTest', meta: { title: '提示词测试', isLogin: true } },
    { path: '/WordList', component: WordList, name: 'WordList', meta: { title: '提示词列表', isLogin: true } },
    { path: '/WordManage', component: WordManage, name: 'WordManage', meta: { title: '提示词管理', isLogin: true } },
    { path: '/TagList', component: TagList, name: 'TagList', meta: { title: '标签管理', isLogin: true } },
    { path: '/TaskList', component: TaskList, name: 'TaskList', meta: { title: '任务管理', isLogin: true } },
    { path: '/GenerateCases', component: GenerateCases, name: 'GenerateCases', meta: { title: '任务管理', isLogin: true } },
    { path: '/NewGenerateCases', component: NewGenerateCases, name: 'NewGenerateCases', meta: { title: '新日常病例生成', isLogin: true } },
    { path: '/TaskUpload', component: TaskUpload, name: 'TaskUpload', meta: { title: '任务上传', isLogin: true } },
    { path: '/DiseaseList', component: DiseaseList, name: 'DiseaseList', meta: { title: '病种添加页面', isLogin: true } },
    { path: '/HandCaseUpload', component: HandCaseUpload, name: 'HandCaseUpload', meta: { title: '手动上传病例', isLogin: true } },
    { path: '/TestHis', component: TestHis, name: 'TestHis', meta: { title: '测试his病例系统', isLogin: true } },
    { path: '/Open', component: Open, name: 'Open', meta: { title: '手动上传', isLogin: true } },
    { path: '/Statistics', component: Statistics, name: 'Statistics', meta: { title: '统计', isLogin: true } },
    { path: '/AiTest', component: AiTest, name: 'AiTest', meta: { title: '测试', isLogin: true } },
    { path: '/DoctorList', component: DoctorList, name: 'DoctorList', meta: { title: '医生列表', isLogin: true } },
    { path: '/TagOperationHistory', component: TagOperationHistory, name: 'TagOperationHistory', meta: { title: '标签操作历史记录', isLogin: true } },
    { path: '/TagLibrary', component: TagLibrary, name: 'TagLibrary', meta: { title: '标签库', isLogin: true } },
    { path: '/TagLibraryFuhe', component: TagLibraryFuhe, name: 'TagLibraryFuhe', meta: { title: '复合标签', isLogin: true } },
    { path: '/tagLibraryList', component: tagLibraryList, name: 'tagLibraryList', meta: { title: '标签库', isLogin: true } },
    { path: '/GenerateCaseList', component: GenerateCaseList, name: 'GenerateCaseList', meta: { title: '生成病例列表', isLogin: true } },
    { path: '/AIButler', component: AIButler, name: 'AIButler', meta: { title: 'mcpSql对话', isLogin: true } },
    { path: '/Tag', component: Tag, name: 'Tag', meta: { title: '标签管理', isLogin: true } },
    { path: '/writingChart', component: writingChart, name: 'writingChart', meta: { title: '病历书写统计', isLogin: true } },
    { path: '/DrugManagement', component: DrugManagement, name: 'DrugManagement', meta: { title: '药品管理', isLogin: true } },
    { path: '/LogQuery', component: LogQuery, name: 'LogQuery', meta: { title: '日志查询', isLogin: true } },
];

// if (VITE_APP_ENV === 'release') {
//     routes = [{ path: '/', component: HisTask, name: 'HisTask', meta: { title: '测试', isLogin: true } }]
// }

export default createRouter({
    history: createWebHashHistory(),
    routes: routes,
});
