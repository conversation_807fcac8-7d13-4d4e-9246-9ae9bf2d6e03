<template>
  <el-container class="medical-record">
    <!-- 左侧病人信息栏 -->
    <el-aside width="240px" class="patient-sidebar">
      <div class="sidebar-header">
        <el-input style="display: none;"></el-input>
        <el-select v-model="selectedDepartment" @change="getUser" placeholder="筛选病区" class="ward-select">
          <el-option :label="item.item_name" :value="item.item_no" v-for="item in select_list" :key="item.id" />
        </el-select>
        <div style="padding-top: 15px; display: flex; gap: 5px;">
          <el-input placeholder="输入患者姓名" @input="searchUser" v-model="user_name" type="text"></el-input>
          <el-button type="primary" @click="addPatient">新增</el-button>
        </div>
      </div>
      <el-menu class="patient-list" v-loading="patientLoad">
        <el-menu-item v-for="patient in user_list" @click="selectUser(patient)" :key="patient.pid" :index="patient.pid"
          v-show="patient.show">
          <span>{{ patient.pat_name }}（主治医生：{{ patient.doctor_name }}）</span>
        </el-menu-item>
      </el-menu>
    </el-aside>
    <!-- 主要内容区 -->
    <el-main>
      <div class="content-header">
        <h3 style="display: flex;">
          <div style="width: 300px;">本次入院最新病历</div>
          <el-select v-model="selectedPvid" @change="getTables" placeholder="选择入院次数" class="ward-select">
            <el-option :value="row" :key="row" v-for="row in parseInt(select_row.pvid || 0)">
              {{ '第' + row + '入院' }}
            </el-option>
          </el-select>
        </h3>
      </div>
      <div>
        <el-button type="primary" @click="dialogBingli" v-if="activeName === 'bingli'">增加病例</el-button>
        <el-button type="primary" @click="dialogYizhu" v-if="activeName === 'yizhu'">增加医嘱</el-button>
        <el-button type="primary" @click="dialogJiancha" v-if="activeName === 'jiancha'">增加检查</el-button>
        <el-button type="primary" @click="dialogJianyan" v-if="activeName === 'jianyan'">增加检验</el-button>
        <!-- <el-button type="success" style="margin-right:50px;" @click="updatePatientData('emr')"
          v-if="activeName === 'bingli'">更新病例</el-button> -->
        <el-button type="success" style="margin-right:50px;" @click="updatePatientData('yz')"
          v-if="activeName === 'yizhu'">更新医嘱</el-button>
        <el-button type="success" style="margin-right:50px;" @click="updatePatientData('jc')"
          v-if="activeName === 'jiancha'">更新检查</el-button>
        <el-button type="success" style="margin-right:50px;" @click="updatePatientData('jy')"
          v-if="activeName === 'jianyan'">更新检验</el-button>

        <el-button type="warning" @click="refreshTaskDialog">更新任务</el-button>
        <el-button type="warning" @click="mergeTaskDialog" style="margin-left:10px;">任务合并</el-button>
      </div>
      <div class="dialog">
        <!-- 新增病历 -->
        <el-dialog v-model="binglinVisible" :title="formDataBingli.id ? '编辑病历' : '新增病历'" width="1000px">
          <el-form :model="formDataBingli" label-width="80px">
            <el-form-item label="病历类型">
              <el-select v-model="formDataBingli.title" placeholder="请选择病历类型">
                <el-option v-for="item in caseTypes" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="病例内容">
              <el-input v-model="formDataBingli.content" type="textarea" :rows="7" placeholder="请输入病例内容" />
            </el-form-item>
            <el-form-item label="病例XML">
              <el-input v-model="formDataBingli.xml" type="textarea" :rows="7" placeholder="请输入病例XML" />
            </el-form-item>
            <el-form-item label="病历时间">
              <el-date-picker v-model="formDataBingli.document_time" type="datetime" placeholder="选择日期时间"
                value-format="YYYY-MM-DD HH:mm:ss" style="width: 100%" />
            </el-form-item>
          </el-form>
          <template #footer>
            <el-button @click="binglinVisible = false">取消</el-button>
            <el-button type="primary" @click="addBingli">确认</el-button>
          </template>
        </el-dialog>
        <!-- 新增医嘱 -->
        <el-dialog v-model="yizhuVisible" title="新增医嘱" width="50%">
          <el-form :model="formDatayizhu" label-width="100px">
            <el-form-item label="医嘱ID">
              <el-input v-model="formDatayizhu.YZ_ID" type="number" />
            </el-form-item>
            <el-form-item label="开嘱时间">
              <el-date-picker v-model="formDatayizhu.YZKJSJ" type="datetime" placeholder="选择时间"
                value-format="YYYY-MM-DD HH:mm:ss" style="width: 100%" />
            </el-form-item>
            <el-form-item label="医嘱执行时间">
              <el-date-picker v-model="formDatayizhu.YZZXSJ" type="datetime" placeholder="选择时间"
                value-format="YYYY-MM-DD HH:mm:ss" style="width: 100%" />
            </el-form-item>
            <el-form-item label="停嘱时间">
              <el-date-picker v-model="formDatayizhu.YZTZSJ" type="datetime" placeholder="选择时间"
                value-format="YYYY-MM-DD HH:mm:ss" style="width: 100%" />
            </el-form-item>
            <el-form-item label="单次用量">
              <el-input v-model="formDatayizhu.YWSY_DCJL" />
            </el-form-item>
            <el-form-item label="总量">
              <el-input v-model="formDatayizhu.SL" type="number" />
            </el-form-item>
            <el-form-item label="单位">
              <el-input v-model="formDatayizhu.JLDW" />
            </el-form-item>
            <el-form-item label="频次">
              <el-select v-model="formDatayizhu.YWSY_PL" placeholder="请选择">
                <el-option v-for="item in frequencyOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="医嘱类别">
              <el-select v-model="formDatayizhu.YZLB" placeholder="请选择">
                <el-option v-for="item in adviceTypeOptions" :key="item.value" :label="item.label"
                  :value="item.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="项目类别">
              <el-select v-model="formDatayizhu.XMLB" placeholder="请选择">
                <el-option v-for="item in projectType" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="医嘱内容">
              <el-input v-model="formDatayizhu.YZNR" type="textarea" :rows="3" placeholder="请输入医嘱内容" />
            </el-form-item>
          </el-form>
          <template #footer>
            <el-button @click="yizhuVisible = false">取消</el-button>
            <el-button type="primary" @click="addYizhu">确认</el-button>
          </template>
        </el-dialog>
        <!-- 新增检查 -->
        <el-dialog v-model="jianchaVisible" title="新增检查" width="50%">
          <el-form :model="formDatajiancha" label-width="80px">
            <el-form-item label="检查项目">
              <el-select v-model="formDatajiancha.ORDER_CONTENT" placeholder="请选择">
                <el-option v-for="item in checkProjectOptions" :key="item.value" :label="item.label"
                  :value="item.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="申请时间">
              <el-date-picker v-model="formDatajiancha.APPLY_TIME" type="datetime" placeholder="选择时间"
                value-format="YYYY-MM-DD HH:mm:ss" style="width: 100%" />
            </el-form-item>
            <el-form-item label="报告时间">
              <el-date-picker v-model="formDatajiancha.REPORT_TIME" type="datetime" placeholder="选择时间"
                value-format="YYYY-MM-DD HH:mm:ss" style="width: 100%" />
            </el-form-item>
            <el-form-item label="诊断建议">
              <el-input v-model="formDatajiancha.LOITEM_CNAME" />
            </el-form-item>
            <el-form-item label="检查结果">
              <el-input v-model="formDatajiancha.ORDER_RPT_RESULT" type="textarea" :rows="4" placeholder="请输入检查结果" />
            </el-form-item>
          </el-form>
          <template #footer>
            <el-button @click="jianchaVisible = false">取消</el-button>
            <el-button type="primary" @click="addJiancha">确认</el-button>
          </template>
        </el-dialog>
        <!-- 更新任务确认 -->
        <el-dialog v-model="refreshTaskVisible" title="更新任务确认" width="50%">
          <p>确定要更新任务吗？</p>
          <template #footer>
            <el-button @click="refreshTaskVisible = false">取消</el-button>
            <el-button type="primary" @click="confirmRefreshTask">确认</el-button>
          </template>
        </el-dialog>

        <!-- 任务合并确认 -->
        <el-dialog v-model="mergeTaskVisible" title="任务合并确认" width="50%">
          <p>确定要合并任务吗？</p>
          <template #footer>
            <el-button @click="mergeTaskVisible = false">取消</el-button>
            <el-button type="primary" @click="confirmMergeTask">确认</el-button>
          </template>
        </el-dialog>
        <!-- 新增检验 -->
        <el-dialog v-model="jianyanVisible" title="新增检验" width="50%">
          <el-form :model="formDataJianyan" label-width="100px">
            <el-form-item label="检验项目">
              <el-select v-model="formDataJianyan.XMMC" placeholder="请选择">
                <el-option v-for="item in testProjectOptions" :key="item.value" :label="item.label"
                  :value="item.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="标本类型">
              <el-input v-model="formDataJianyan.BZLX" />
            </el-form-item>
            <el-form-item label="申请时间">
              <el-date-picker v-model="formDataJianyan.APPLY_TIME" type="datetime" placeholder="选择时间"
                value-format="YYYY-MM-DD HH:mm:ss" style="width: 100%" />
            </el-form-item>
            <el-form-item label="检验结果">
              <el-input v-model="formDataJianyan.JYJG" />
            </el-form-item>
            <el-form-item label="参考值">
              <el-input v-model="formDataJianyan.JGCK" />
            </el-form-item>
            <el-form-item label="结果标志">
              <el-input v-model="formDataJianyan.JGBZ" />
            </el-form-item>
            <el-form-item label="单位">
              <el-input v-model="formDataJianyan.JGDW" />
            </el-form-item>
            <el-form-item label="审核时间">
              <el-date-picker v-model="formDataJianyan.SHSJ" type="datetime" placeholder="选择时间"
                value-format="YYYY-MM-DD HH:mm:ss" style="width: 100%" />
            </el-form-item>
            <el-form-item label="报告时间">
              <el-date-picker v-model="formDataJianyan.BGSJ" type="datetime" placeholder="选择时间"
                value-format="YYYY-MM-DD HH:mm:ss" style="width: 100%" />
            </el-form-item>


            <el-form-item label="结果时间">
              <el-date-picker v-model="formDataJianyan.JGSJ" type="datetime" placeholder="选择时间"
                value-format="YYYY-MM-DD HH:mm:ss" style="width: 100%" />
            </el-form-item>
            <el-form-item label="签收时间">
              <el-date-picker v-model="formDataJianyan.QSSJ" type="datetime" placeholder="选择时间"
                value-format="YYYY-MM-DD HH:mm:ss" style="width: 100%" />
            </el-form-item>
            <el-form-item label="医嘱内容">
              <el-input v-model="formDataJianyan.YZNR" type="textarea" :rows="3" placeholder="请输入医嘱内容" />
            </el-form-item>
          </el-form>
          <template #footer>
            <el-button @click="jianyanVisible = false">取消</el-button>
            <el-button type="primary" @click="addJianyan">确认</el-button>
          </template>
        </el-dialog>

        <el-dialog v-model="stopYizhuVisible" title="选择停嘱时间" width="50%">
          <el-form :model="formDataStop" label-width="80px">
            <el-form-item label="停嘱时间">
              <el-date-picker v-model="formDataStop.YZTZSJ" type="datetime" placeholder="选择停嘱时间"
                value-format="YYYY-MM-DD HH:mm:ss" style="width: 100%" />
            </el-form-item>
          </el-form>
          <template #footer>
            <el-button @click="stopYizhuVisible = false">取消</el-button>
            <el-button type="primary" @click="confirmStopYizhu">确认停嘱</el-button>
          </template>
        </el-dialog>
        <!-- 查看病历内容弹框 -->
        <el-dialog v-model="viewBingliVisible" title="病例内容" width="1000px">
          <div class="view-content">
            <div class="view-item">
              <span class="label">病历类型：</span>
              <span>{{ viewBingliData.title }}</span>
            </div>
            <div class="view-item">
              <span class="label">病历时间：</span>
              <span>{{ viewBingliData.document_time }}</span>
            </div>
            <div class="view-item">
              <span class="label">病例内容：</span>
              <div class="content-box">{{ viewBingliData.content }}</div>
            </div>
            <div class="view-item">
              <span class="label">创建时间：</span>
              <span>{{ viewBingliData.create_time }}</span>
            </div>
            <div class="view-item">
              <span class="label">更新时间：</span>
              <span>{{ viewBingliData.update_time }}</span>
            </div>
          </div>
          <template #footer>
            <el-button @click="viewBingliVisible = false">关闭</el-button>
          </template>
        </el-dialog>
      </div>
      <!-- 最新病历表格 -->
      <el-tabs v-model="activeName" class="demo-tabs">
        <el-tab-pane label="病例列表" name="bingli">
          <el-table :data="his_list" border style="margin-bottom: 20px;height: 700px">
            <el-table-column prop="type" label="病历类型" width="180">
              <template #default="scope">
                {{ scope.row.title }}
              </template>
            </el-table-column>
            <el-table-column prop="type" label="病例内容" width="500">
              <template #default="scope">
                <div style="height: 50px;overflow-y: scroll;">{{ scope.row.content }}</div>
              </template>
            </el-table-column>
            <el-table-column prop="time" label="病历时间" width="180">
              <template #default="scope">
                {{ scope.row.document_time }}
              </template>
            </el-table-column>
            <el-table-column prop="time" label="创建时间" width="180">
              <template #default="scope">
                {{ scope.row.create_time }}
              </template>
            </el-table-column>
            <el-table-column prop="time" label="更新时间" width="180">
              <template #default="scope">
                {{ scope.row.update_time }}
              </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" min-width="120">
              <template #default="scope">
                <el-button link type="primary" size="small" @click="deleteBingli(scope.row)">
                  删除
                </el-button>
                <el-button link type="primary" size="small" @click="editBingli(scope.row)">
                  修改
                </el-button>
                <el-button link type="primary" size="small" @click="viewBingli(scope.row)">
                  查看
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="医嘱列表" name="yizhu">
          <el-table :data="yizhu_list" border style="margin-bottom: 20px;height: 500px">
            <el-table-column prop="YZ_ID" label="医嘱ID" width="80" />
            <el-table-column prop="YZKJSJ" label="开嘱时间" width="160" :formatter="(row) => formatDateTime(row.YZKJSJ)" />
            <el-table-column prop="YZZXSJ" label="医嘱执行时间" width="160"
              :formatter="(row) => formatDateTime(row.YZZXSJ)" />
            <el-table-column prop="YZTZSJ" label="停嘱时间" width="160" :formatter="(row) => formatDateTime(row.YZTZSJ)" />
            <el-table-column prop="YWSY_DCJL" label="单次用量" width="100" />
            <el-table-column prop="SL" label="总量" width="80" />
            <el-table-column prop="JLDW" label="单位" width="80" />
            <el-table-column prop="YWSY_PL" label="频次" width="80" />
            <el-table-column prop="YZLB" label="医嘱类别" width="100"
              :formatter="(row) => row.YZLB === '1' ? '临嘱' : '长嘱'" />
            <el-table-column prop="XMLB" label="项目类别" width="100" />
            <el-table-column prop="YZNR" label="医嘱内容" min-width="200" show-overflow-tooltip />
            <el-table-column fixed="right" label="操作" min-width="120">
              <template #default="scope">
                <el-button link type="primary" size="small" @click="deleteYizhu(scope.row)">
                  删除
                </el-button>
                <el-button link type="primary" size="small" @click="editYizhu(scope.row)">
                  修改
                </el-button>
                <el-button link type="primary" size="small" @click="endYizhu(scope.row)">
                  停嘱
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>

        <el-tab-pane label="检查列表" name="jiancha">
          <el-table :data="jiancha" border style="margin-bottom: 20px;height: 500px">
            <el-table-column prop="ORDER_CONTENT" label="检查项目" width="150" show-overflow-tooltip />
            <el-table-column prop="APPLY_TIME" label="申请时间" width="160"
              :formatter="(row) => formatDateTime(row.APPLY_TIME)" />
            <el-table-column prop="REPORT_TIME" label="报告时间" width="160"
              :formatter="(row) => formatDateTime(row.REPORT_TIME)" />
            <el-table-column prop="LOITEM_CNAME" label="诊断建议" width="120" />
            <el-table-column prop="ORDER_RPT_RESULT" label="检查结果" min-width="300" show-overflow-tooltip>
              <template #default="{ row }">
                <div style="white-space: pre-line">{{ row.ORDER_RPT_RESULT }}</div>
              </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" min-width="120">
              <template #default="scope">
                <el-button link type="primary" size="small" @click="deleteJiancha(scope.row)">
                  删除
                </el-button>
                <el-button link type="primary" size="small" @click="editJiancha(scope.row)">
                  修改
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="检验列表" name="jianyan">
          <el-table :data="jianyan" border style="margin-bottom: 20px;height: 500px">
            <el-table-column prop="XMMC" label="检验项目" width="120" />
            <el-table-column prop="BZLX" label="标本类型" width="100" />
            <el-table-column prop="APPLY_TIME" label="申请时间" width="160"
              :formatter="(row) => formatDateTime(row.APPLY_TIME)" />
            <el-table-column prop="SHSJ" label="审核时间" width="100" />
            <el-table-column prop="BGSJ" label="报告时间" width="100" />
            <el-table-column prop="JYJG" label="检验结果" width="100" />
            <el-table-column prop="JGCK" label="参考值" width="120" />
            <el-table-column prop="JGBZ" label="结果标志" width="100" />
            <el-table-column prop="JGDW" label="单位" width="100" />
            <el-table-column prop="JGSJ" label="结果时间" width="160" :formatter="(row) => formatDateTime(row.JGSJ)" />
            <el-table-column prop="QSSJ" label="签收时间" width="160" :formatter="(row) => formatDateTime(row.QSSJ)" />
            <el-table-column prop="YZNR" label="医嘱内容" min-width="200" show-overflow-tooltip />
            <el-table-column fixed="right" label="操作" min-width="120">
              <template #default="scope">
                <el-button link type="primary" size="small" @click="deleteJianyan(scope.row)">
                  删除
                </el-button>
                <el-button link type="primary" size="small" @click="editJianyan(scope.row)">
                  修改
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="任务列表" name="taskList">
          <el-table :data="taskList" border style="margin-bottom: 20px;height: 700px">
            <el-table-column prop="state" label="病历类型" width="120" />
            <el-table-column prop="document" label="病例内容" width="150" />
            <el-table-column prop="content" label="任务执行消息" width="150" />
            <el-table-column prop="status" label="任务状态" width="100">
              <template #default="scope">
                <span v-if="scope.row.status === 0">待执行</span>
                <span v-else-if="scope.row.status === 9">未执行</span>
                <span v-else-if="scope.row.status === 2">已上传</span>
                <span v-else-if="scope.row.status === -1">已删除</span>
                <span v-else>{{ scope.row.status }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="upload_status" label="上传状态" width="100" />
            <el-table-column prop="create_time" label="创建时间" width="180" />
            <el-table-column prop="edit_time" label="修改时间" width="180" />
            <el-table-column prop="is_sys" label="系统创建" width="100">
              <template #default="scope">
                <span>{{ scope.row.is_sys === 1 ? '是' : '否' }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="is_auto_write" label="自动书写" width="100">
              <template #default="scope">
                <span>{{ scope.row.is_auto_write === 1 ? '是' : '否' }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="is_auto_upload" label="自动上传" width="100">
              <template #default="scope">
                <span>{{ scope.row.is_auto_upload === 1 ? '是' : '否' }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="create_reason" label="创建原因" width="150">
              <template #default="scope">
                <div style="height: 100px;overflow-y: scroll;">{{ scope.row.create_reason }}</div>
              </template>
            </el-table-column>
            <el-table-column prop="yz_content" label="医嘱内容" width="250">
              <template #default="scope">
                <div style="height: 100px;overflow-y: scroll;">{{ scope.row.yz_content }}</div>
              </template>
            </el-table-column>
            <el-table-column prop="jy_content" label="检验内容" width="150" />
            <el-table-column prop="jc_content" label="检查内容" width="150" />
          </el-table>
        </el-tab-pane>
      </el-tabs>

      <!-- 新增患者弹窗 -->
      <el-dialog v-model="addPatientVisible" title="新增患者" width="50%" :close-on-click-modal="false">
        <el-scrollbar>
          <el-form :model="addPatientData" :rules="formRules" ref="patientForm" label-width="auto">
            <el-row style="display: flex; justify-content: space-around">
              <!-- Left Column -->
              <el-col :span="11">
                <el-form-item label="医生" prop="pat_sex">
                  <el-select v-model="addPatientData.doctor_id" placeholder="请选择医生">
                    <el-option v-for="item in doctorSelect" :key="item.id" :label="item.username" :value="item.id" />
                  </el-select>
                </el-form-item>
                <el-form-item label="PID" prop="pid">
                  <el-input v-model.number="addPatientData.pid" placeholder="请填写PID" :maxlength="7" />
                </el-form-item>
                <el-form-item label="PVID" prop="pvid">
                  <el-input v-model.number="addPatientData.pvid" placeholder="请填写PVID" :maxlength="2" />
                </el-form-item>
                <el-form-item label="住院号" prop="inpno">
                  <el-input v-model="addPatientData.inpno" placeholder="请填写住院号" :maxlength="8" />
                </el-form-item>
                <el-form-item label="床号" prop="inp_bed_no">
                  <el-input v-model="addPatientData.inp_bed_no" placeholder="请填写床号" :maxlength="4" />
                </el-form-item>
                <el-form-item label="患者姓名" prop="pat_name">
                  <el-input v-model="addPatientData.pat_name" placeholder="请填写患者姓名" :maxlength="10" />
                </el-form-item>
                <el-form-item label="是否中转病人" prop="is_zzbr">
                  <el-switch v-model="addPatientData.is_zzbr" />
                </el-form-item>
              </el-col>

              <!-- Right Column -->
              <el-col :span="11">
                <el-form-item label="身份证号" prop="idno">
                  <el-input v-model="addPatientData.idno" placeholder="请填写身份证号" :maxlength="18" show-word-limit
                    @blur="parseIDCard" />
                </el-form-item>
                <el-form-item label="性别" prop="pat_sex">
                  <el-select v-model="addPatientData.pat_sex" placeholder="请选择性别" style="width: 100%">
                    <el-option label="男" value="男" />
                    <el-option label="女" value="女" />
                  </el-select>
                </el-form-item>
                <el-form-item label="年龄">
                  <el-input v-model="addPatientData.pat_age" placeholder="请填写年龄" type="string" />
                </el-form-item>
                <el-form-item label="出生日期">
                  <el-date-picker v-model="addPatientData.pat_brsdate" type="datetime" placeholder="选择出生日期"
                    value-format="YYYY-MM-DD HH:mm:ss" style="width: 100%" />
                </el-form-item>
                <el-form-item label="入科时间">
                  <el-date-picker v-model="addPatientData.indept_time" type="datetime" placeholder="选择入科时间"
                    value-format="YYYY-MM-DD HH:mm:ss" style="width: 100%" />
                </el-form-item>
                <el-form-item label="出院时间" prop="out_time">
                  <el-date-picker v-model="addPatientData.out_time" type="datetime" placeholder="选择出院时间"
                    value-format="YYYY-MM-DD HH:mm:ss" style="width: 100%" :disabled="!addPatientData.indept_time" />
                </el-form-item>
                
                <el-form-item label="病种名称" prop="disease">
                  <el-input v-model="addPatientData.disease" placeholder="请填写病种名称" :maxlength="100" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-scrollbar>
        <template #footer>
          <el-button @click="addPatientVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmAddPatient(patientForm)">确认</el-button>
        </template>
      </el-dialog>

    </el-main>
  </el-container>
</template>

<script setup>
import { ref, watch, onMounted, reactive, toRaw } from 'vue'
import { post, get } from '@/utils/request.js'
import c from "@/utils/config";
import { message, confirm } from '@/utils/message'
import { baseUrl } from '../utils/baseUrl';
const api = `${baseUrl.h_test_zhixuee}`

const api_url = c.api_url;
const api_url1 = baseUrl.url9;
const selectedDepartment = ref('')
const taskFilter = ref('all')
const load = ref(false)
const patientLoad = ref(false) //左侧患者列表加载
const select_list = ref([])
const user_list = ref([])
const dialogVisible = ref(false)
const select_row = ref({})
const his_list = ref([])
const yizhu = ref([])
const page = ref(1)
const listings = ref([])
const user_name = ref("")

const showAdvice = ref(false)
const adviceList = ref([]) //  医嘱列表
const advicePage = ref(1)
const taskList = ref([]) // 任务列表
const selectedPvid = ref("")
const select_name = ref("")

const activeName = ref("bingli")
const jianyan = ref()
const jiancha = ref()
const yizhu_list = ref([])



// 新增患者
const addPatientVisible = ref(false)
const addPatientData = reactive({
  pid: '',
  pvid: '',
  real_pvid: '',
  inpno: '',
  inp_bed_no: '',
  pat_name: '',
  idno: '',
  dept_id: '',
  // dept_name: '',
  doctor_id: '',
  // doctor_name: '',
  ward_id: '',
  pat_sex: '',
  pat_age: '',
  pat_brsdate: '',
  indept_time: '',
  out_time: '',
  is_zzbr: false,
  disease: '',
})
const patientForm = ref(null)
const doctorSelect = ref([]) // 医生列表
const getDoctorList = () => {
  let params = {
    department_id: selectedDepartment.value
  }
  const filteredParams = Object.entries(params).filter(([key, value]) => value !== '' && value !== undefined && value !== null);
  const queryString = new URLSearchParams(filteredParams).toString();
  fetch(api_url1 + `/v1/api/rpa_task_list/doctor_list?${queryString}`, {
    method: 'GET',
    headers: {
      'x-api-key': 'API655c18174462a06ede7ffbfa5aca4270c645b9b78ca30817e3a1bd496f34e04b',
    },
  })
    .then(response => response.json())
    .then(res => {
      doctorSelect.value = res.data.records
    })
    .catch(error => {
    });
}
const validateID = (rule, value, callback) => {
  if (value && !/^[1-9]\d{5}(18|19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dXx]$/.test(value)) {
    callback(new Error('请输入有效的身份证号'));
  } else {
    callback();
  }
};
const parseIDCard = () => {
  const id = addPatientData.idno;
  if (!id || id.length !== 18) return;

  try {
    // Extract birth date parts
    const year = id.substring(6, 10);
    const month = id.substring(10, 12);
    const day = id.substring(12, 14);

    // Format as YYYY-MM-DD 00:00:00
    const birthDate = `${year}-${month}-${day} 00:00:00`;

    // Calculate age (simple year difference)
    const currentYear = new Date().getFullYear();
    const age = currentYear - parseInt(year);

    // Determine gender
    const genderCode = parseInt(id.charAt(16));
    const gender = genderCode % 2 === 1 ? '男' : '女';

    // Update form data
    addPatientData.pat_sex = gender;
    addPatientData.pat_age = String(age);
    addPatientData.pat_brsdate = birthDate;
  } catch (e) {
    console.error('ID card parsing failed:', e);
  }
}
const outTimePickerOptions = {
  disabledDate: (time) => {
    return this.addPatientData.indept_time &&
      time.getTime() < new Date(this.addPatientData.indept_time).getTime();
  }
}
const formRules = {
  pid: [{ required: true, message: 'PID不能为空', trigger: 'blur' }],
  pvid: [{ required: true, message: 'PVID不能为空', trigger: 'blur' }],
  inpno: [{ required: true, message: '住院号不能为空', trigger: 'blur' }],
  pat_name: [{ required: true, message: '患者姓名不能为空', trigger: 'blur' }],
  idno: [{ validator: validateID, trigger: 'blur' }],
  pat_age: [
    { type: 'number', message: '年龄必须为数字', trigger: 'blur' },
    { min: 0, max: 150, message: '年龄范围 0-150', trigger: 'blur' }
  ],
  pat_brsdate: [{ required: true, message: '请选择出生日期', trigger: 'change' }],
  indept_time: [{ required: true, message: '请选择入科时间', trigger: 'change' }],
  out_time: [{
    validator: (rule, value, callback) => {
      if (value && new Date(value) < new Date(addPatientData.indept_time)) {
        callback(new Error('出院时间不能早于入科时间'));
      } else {
        callback();
      }
    },
    trigger: 'change'
  }]
}

const addPatient = () => {
  if (!selectedDepartment.value) {
    message("请选择科室", "error")
    return
  }
  getDoctorList()
  let data = select_list.value.find(item => item.item_no === selectedDepartment.value)
  addPatientVisible.value = true
  addPatientData.ward_id = data.id
  addPatientData.dept_id = data.item_no
  // addPatientData.dept_name = data.item_name
}
const resetForm = () => {
  if (!patientForm.value) return
  addPatientVisible.value = false
  patientForm.value.resetFields()
}
const confirmAddPatient = (formEl) => {
  
  if (!formEl) return
  formEl.validate((valid) => {
    if (valid) {
      addPatientData.real_pvid = addPatientData.pvid
      addPatientData.is_zzbr = addPatientData.is_zzbr ? '是' : '否'
      // 转为普通对象
      console.log(addPatientData);
      const filteredParams = Object.entries(addPatientData).filter(([key, value]) => value !== '' && value !== undefined && value !== null);
      const queryString = new URLSearchParams(filteredParams).toString();
      fetch(baseUrl.url9 + `/v1/api/patient_in/create`, {
        method: 'POST',
        headers: {
          "content-type": "application/json",
          'x-api-key': 'API655c18174462a06ede7ffbfa5aca4270c645b9b78ca30817e3a1bd496f34e04b',
        },
        body: JSON.stringify(addPatientData)
      })
        .then(response => response.json())
        .then(res => {
          console.log(res);
          if (res.code === 400) {
            message(res.message, "error")
          }else{
            addPatientVisible.value = false
            message("添加成功", "success")
            getUser()
            resetForm()
          }
        })
        .catch(error => {
          message("添加失败", "error")
        });
    }
  })
}


//搜索患者
const searchUser = (value) => {
  user_list.value = user_list.value.map((row) => {
    if (!user_name.value || row.pat_name.toLowerCase()?.includes(user_name.value.toLowerCase())) {
      row.show = true;
    } else {
      row.show = false;
    }
    return row;
  })

  console.log(value)
}

// 格式化日期时间
const formatDateTime = (datetime) => {
  if (!datetime) return '';
  return new Date(datetime).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  });
}
// 格式化日期
const formatDate = (date) => {
  if (!date) return '';
  return date.split(' ')[0];
}

// 格式化金额
const formatMoney = (money) => {
  if (!money) return '0.00';
  return `¥${Number(money).toFixed(2)}`;
}
// 获取医嘱信息
const getDoctorAdvice = (pid, pvid) => {
  adviceList.value = []
  load.value = true
  post(`${api_url}/patient/getYz`, JSON.stringify({ pid, pvid })).then(res => {
    console.log(res)
    adviceList.value = res.data.map((item) => {
      let {
        order_content,
        order_drask,
        order_once_qunt,
        order_total_qunt,
        order_once_qunt_unit,
        order_exe_time_start,
        order_exe_time_end,
        order_freq_name,
        conterm_time,
        order_expidate_type,
        citem_type,
        order_status,
        apply_time,
        orderstp_time,
      } = item
      return {
        order_freq_name,
        conterm_time,
        orderstp_time,
        order_content,
        order_drask,
        order_once_qunt,
        order_total_qunt,
        order_once_qunt_unit,
        order_exe_time_start,
        order_exe_time_end,
        order_expidate_type,
        citem_type,
        order_status,
        apply_time
      }
    })
    load.value = false
  }).catch(error => {
    load.value = false
    console.error('发生错误:', error);
  });
}

// 选择患者
const selectUser = (row) => {
  select_row.value = row
  selectedPvid.value = row.pvid;
  page.value = parseInt(row.pvid)
  advicePage.value = parseInt(row.pvid)
  getTables()
}

watch(() => activeName.value, () => {
  getTables()
})
const getTables = () => {
  const pid = select_row.value.pid
  const pvid = selectedPvid.value
  if (pid === '' || pvid === '') {
    return false
  }
  switch (activeName.value) {
    case 'bingli':
      getTask(pid, pvid)
      break
    case 'yizhu':
      getYizhu(pid, pvid)
      break
    case 'jiancha':
      getJiancha(pid, pvid)
      break
    case 'jianyan':
      getJianyan(pid, pvid)
      break
    case 'taskList':
      getTaskList(pid, pvid)
  }
}

// 获取病例列表
const getTask = (pid, pvid) => {
  load.value = true
  let url = `${api_url}/recordmanage/medical_list`
  post(url, JSON.stringify({ pid, pvid })).then(res => {
    // taskList.value = res.data_list.ai_list
    if (res.data.length > 0) {
      his_list.value = res.data
    } else {
      his_list.value = {
        WBMC: "",
        document_time: ""
      }
    }
    load.value = false
  }).catch(error => {
    load.value = false
    console.error('发生错误:', error);
  });
}

// 获取医嘱列表
const getYizhu = (pid, pvid) => {
  load.value = true
  let url = `${api_url}/recordmanage/doctor_orders_list`
  post(url, JSON.stringify({
    pid,
    pvid,
  })).then(res => {
    // taskList.value = res.data_list.ai_list
    if (res.data.length > 0) {
      yizhu_list.value = res.data
    } else {
      yizhu_list.value = {
      }
    }
    load.value = false
  }).catch(error => {
    load.value = false
    console.error('发生错误:', error);
  });
}
// 获取检验列表
const getJianyan = (pid, pvid) => {
  load.value = true
  const params = {
    pid,
    pvid,
  }
  const queryString = new URLSearchParams(params).toString();
  let url = `${api_url}/recordmanage/jy_result_list`
  post(url,
    JSON.stringify(params)
  ).then(res => {
    // taskList.value = res.data_list.ai_list
    if (res.data.length > 0) {
      jianyan.value = res.data
    } else {
      jianyan.value = {
        WBMC: "",
        document_time: ""
      }
    }
    load.value = false
  }).catch(error => {
    load.value = false
    console.error('发生错误:', error);
  });
}
// 获取检验列表
const getJiancha = (pid, pvid) => {
  load.value = true
  load.value = true
  const params = {
    pid,
    pvid,
  }
  const queryString = new URLSearchParams(params).toString();
  let url = `${api_url}/recordmanage/jc_result_list`
  post(url,
    JSON.stringify(params)
  ).then(res => {
    // taskList.value = res.data_list.ai_list
    if (res.data.length > 0) {
      jiancha.value = res.data
    } else {
      jiancha.value = {
        WBMC: "",
        document_time: ""
      }
    }
    load.value = false
  }).catch(error => {
    load.value = false
    console.error('发生错误:', error);
  });
}

// 获取任务列表
const getTaskList = (pid, pvid) => {
  let params = {
    pid,
    pvid
  }
  post(`${api_url}/task/getTaskList`, JSON.stringify(params)).then(res => {
    if (res.error_code !== 0) {
      return message(res.msg || '获取任务列表失败', 'error')
    }
    taskList.value = res.data || []
  }).catch(error => {
    console.error('请求任务列表出错:', error)
    return message('网络错误，请稍后重试', 'error')
  })
}

// 获取患者
const getUser = () => {

  let params = {
    dept_id: selectedDepartment.value,
    page_size: 200,
  }
  const filteredParams = Object.entries(params).filter(([key, value]) => value !== '' && value !== undefined && value !== null);
  const queryString = new URLSearchParams(filteredParams).toString();
  let s_index = select_list.value.findIndex((row) => row.item_no == selectedDepartment.value);
  select_name.value = select_list.value[s_index]['item_name']

  load.value = true
  patientLoad.value = true
  let url = `${api_url1}/v1/api/patient_in/list?${queryString}`
  get(url,
    // JSON.stringify({pid,pvid})
  )
    .then(res => {
      user_list.value = res.data.records.map((row) => {
        row.show = true
        return row
      })
      load.value = false
      patientLoad.value = false
    }).catch(error => {
      load.value = false
      patientLoad.value = false
      console.error('发生错误:', error);
    });
}

// 获取病区
const getWardList = () => {
  let params = {
    page_size: 1000,
  }
  const filteredParams = Object.entries(params).filter(([key, value]) => value !== '' && value !== undefined && value !== null);
  const queryString = new URLSearchParams(filteredParams).toString();
  let url = `${api_url1}/v1/api/department/list?${queryString}`
  load.value = true
  get(url, JSON.stringify({})).then(res => {
    console.log(123, res)
    select_list.value = res.data.records.filter((row) => [221, 222, 223, 224].includes(row.item_no))
    load.value = false
  }).catch(error => {
    load.value = false
    console.error('发生错误:', error);
  });
}
getWardList()


// // 模拟病人列表数据
// const patients = ref([
//   { id: '1', name: '患者姓名' },
//   { id: '2', name: '患者姓名' },
//   { id: '3', name: '患者姓名' },
//   { id: '4', name: '患者姓名' },
// ])

// 最新病历数据
// const latestRecord = ref([{
//   type: '三级查房-主治医师',
//   time: '2024-12-12 09:01',
//   admissionCount: 10,
//   admissionTime: '2024-12-10 09:30'
// }])


onMounted(() => {
  getOptions()
})
const projectType = ref([])
// 获取病例列表
const getOptions = () => {
  load.value = true
  let url = `${api_url}/recordmanage/get_option`
  post(url, JSON.stringify({})).then(res => {
    console.log(res, 12313);

    // taskList.value = res.data_list.ai_list
    if (Object.keys(res.data).length > 0) {

      res.data.task_type.push("补充诊断")

      caseTypes.value = res.data.task_type.map(data => {
        return {
          label: data,
          value: data
        }
      })
      projectType.value = res.data.project_type.map(data => {
        return {
          label: data,
          value: data
        }
      })
    }
    load.value = false
  }).catch(error => {
    load.value = false
    console.error('发生错误:', error);
  });
}
// 新增病历
const binglinVisible = ref(false)
const caseTypes = ref([
  { value: '1', label: '入院记录' },
  { value: '2', label: '病程记录' },
  { value: '3', label: '出院记录' }
])

const formDataBingli = reactive({
  title: '',
  content: '',
  document_time: '',
  xml: ''
})

const dialogBingli = () => {
  if (Object.keys(select_row.value).length === 0) {
    ElMessage.error('请先选择患者')
    return false
  }
  binglinVisible.value = true
  // 重置表单数据
  Object.assign(formDataBingli, {
    title: '',
    content: '',
    document_time: '',
    id: '',
    xml: '',
  })
}

const addBingli = async (id) => {
  console.log(formDataBingli);
  try {
    const apiUrl = formDataBingli.id
      ? `${api_url}/recordmanage/medical_update`
      : `${api_url}/recordmanage/medical_add`

    const data_list = await post(apiUrl, JSON.stringify({
      ...formDataBingli,
      pid: select_row.value.pid,
      pvid: selectedPvid.value
    }))

    if (data_list.error_code !== 0) {
      ElMessage.error(data_list.msg)
    } else {
      ElMessage.success(`病历${formDataBingli.id ? '修改' : '添加'}成功`)
      binglinVisible.value = false
      getTables()

      // 重置表单
      Object.assign(formDataBingli, {
        id: '',
        type: '',
        content: '',
        document_time: '',
        xml: ''
      })
    }
  } catch (error) {
    console.error('添加病历失败:', error)
    ElMessage.error('病历添加失败')
  }
}

// 病例 删
const deleteBingli = async (row) => {
  load.value = true
  await ElMessageBox.confirm('确定要删除该病例记录吗？', '警告', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  })
  let url = `${api_url}/recordmanage/medical_delete`
  post(url, JSON.stringify({
    id: row.id
  })).then(res => {

    ElMessage.success(`删除成功`)
    getTables()
    load.value = false
  }).catch(error => {
    load.value = false
    console.error('发生错误:', error);
  });
}
// 病例 改
const editBingli = (row) => {

  // 填充表单数据
  formDataBingli.title = row.title
  formDataBingli.content = row.content
  formDataBingli.document_time = formatDateTime(row.document_time)
  formDataBingli.id = row.id
  formDataBingli.xml = row.xml


  // 打开对话框
  binglinVisible.value = true
}

// 新增医嘱
const yizhuVisible = ref(false)
const frequencyOptions = ref([
  { value: 'qd', label: '每日一次' },
  { value: 'bid', label: '每日两次' },
  { value: 'tid', label: '每日三次' },
  { value: 'qid', label: '每日四次' }
])

const adviceTypeOptions = ref([
  { value: '1', label: '临嘱' },
  { value: '2', label: '长嘱' }
])

const formDatayizhu = reactive({
  YZ_ID: '',
  YZKJSJ: '',
  YZZXSJ: '',
  YZTZSJ: '',
  YWSY_DCJL: '',
  SL: '',
  JLDW: '',
  YWSY_PL: '',
  YZLB: '',
  XMLB: '',
  YZNR: ''
})

const dialogYizhu = () => {
  if (Object.keys(select_row.value).length === 0) {
    ElMessage.error('请先选择患者')
    return
  }
  yizhuVisible.value = true
  // 重置表单数据
  Object.keys(formDatayizhu).forEach(key => {
    formDatayizhu[key] = ''
  })
}

const addYizhu = async () => {
  try {
    const apiUrl = formDatayizhu.id
      ? `${api_url}/recordmanage/doctor_orders_update`
      : `${api_url}/recordmanage/doctor_orders_add`

    const data_list = await post(apiUrl, JSON.stringify({
      ...formDatayizhu,
      PID: select_row.value.pid,
      pvid: selectedPvid.value
    }))
    if (data_list.error_code !== 0) {
      ElMessage.error(data_list.msg)
    } else {
      ElMessage.success(`病历${formDatayizhu.id ? '修改' : '添加'}成功`)
      yizhuVisible.value = false
      getTables()
      Object.keys(formDatayizhu).forEach(key => {
        formDatayizhu[key] = ''
      })
    }
  } catch (error) {
    console.error('添加医嘱失败:', error)
    ElMessage.error('医嘱添加失败')
  }
}
// 医嘱 删
const deleteYizhu = async (row) => {
  load.value = true
  await ElMessageBox.confirm('确定要删除该医嘱记录吗？', '警告', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  })
  let url = `${api_url}/recordmanage/doctor_orders_delete`
  post(url, JSON.stringify({
    id: row.id
  })).then(res => {

    ElMessage.success(`删除成功`)
    getTables()
    load.value = false
  }).catch(error => {
    load.value = false
    console.error('发生错误:', error);
  });
}

// 医嘱 改
const editYizhu = (row) => {
  // 填充表单数据
  formDatayizhu.YZ_ID = row.YZ_ID
  formDatayizhu.YZKJSJ = formatDateTime(row.YZKJSJ)
  formDatayizhu.YZZXSJ = formatDateTime(row.YZZXSJ)
  formDatayizhu.YZTZSJ = formatDateTime(row.YZTZSJ)
  formDatayizhu.YWSY_DCJL = row.YWSY_DCJL
  formDatayizhu.SL = row.SL
  formDatayizhu.JLDW = row.JLDW
  formDatayizhu.YWSY_PL = row.YWSY_PL
  formDatayizhu.YZLB = row.YZLB
  formDatayizhu.XMLB = row.XMLB
  formDatayizhu.YZNR = row.YZNR
  formDatayizhu.id = row.id

  yizhuVisible.value = true
}
const stopYizhuVisible = ref(false)
const currentYizhuRow = ref(null)
const formDataStop = reactive({
  YZTZSJ: ''
})

// 医嘱 停嘱
const endYizhu = (row) => {
  currentYizhuRow.value = row
  formDataStop.YZTZSJ = formatDateTime(new Date()) // 默认当前时间
  stopYizhuVisible.value = true
}

const confirmStopYizhu = async () => {
  if (!formDataStop.YZTZSJ) {
    ElMessage.error('请选择停嘱时间')
    return
  }

  load.value = true
  try {
    const data_list = await post(`${api_url}/recordmanage/doctor_orders_stop`, JSON.stringify({
      id: currentYizhuRow.value.id,
      YZTZSJ: formDataStop.YZTZSJ
    }))
    if (data_list.error_code === 0) {
      ElMessage.success('停嘱成功')
      getTables()
      stopYizhuVisible.value = false
    } else {
      ElMessage.error(data_list.msg)
    }
  } catch (error) {
    console.error('停嘱失败:', error)
    ElMessage.error('停嘱操作失败')
  } finally {
    load.value = false
  }
}

// 新增检查
const jianchaVisible = ref(false)
const checkProjectOptions = ref([
  { value: '头颅核磁共振检查', label: '头颅核磁共振' },
  { value: '心电图检查', label: '心电图' },
  { value: '胸部CT', label: '胸部CT' }
])

const formDatajiancha = reactive({
  ORDER_CONTENT: '',
  APPLY_TIME: '',
  REPORT_TIME: '',
  LOITEM_CNAME: '',
  ORDER_RPT_RESULT: ''
})

const dialogJiancha = () => {
  if (Object.keys(select_row.value).length === 0) {
    ElMessage.error('请先选择患者')
    return
  }
  jianchaVisible.value = true
  Object.keys(formDatajiancha).forEach(key => {
    formDatajiancha[key] = ''
  })
}


const addJiancha = async () => {
  try {
    const apiUrl = formDatajiancha.id
      ? `${api_url}/recordmanage/jc_result_update`
      : `${api_url}/recordmanage/jc_result_add`

    const data_list = await post(apiUrl, JSON.stringify({
      ...formDatajiancha,
      PID: select_row.value.pid,
      pvid: selectedPvid.value
    }))
    if (data_list.error_code !== 0) {
      ElMessage.error(data_list.msg)
    } else {
      ElMessage.success(`检查${formDatajiancha.id ? '修改' : '添加'}成功`)
      jianchaVisible.value = false
      getTables()
    }

  } catch (error) {
    console.error('添加检查失败:', error)
    ElMessage.error('检查添加失败')
  }
}
// 检查 删
const deleteJiancha = async (row) => {
  load.value = true
  await ElMessageBox.confirm('确定要删除该检查记录吗？', '警告', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  })
  let url = `${api_url}/recordmanage/jc_result_delete`
  post(url, JSON.stringify({
    id: row.id
  })).then(res => {

    ElMessage.success(`删除成功`)
    getTables()
    load.value = false
  }).catch(error => {
    load.value = false
    console.error('发生错误:', error);
  });
}
// 检查 改
const editJiancha = (row) => {
  // 填充表单数据
  formDatajiancha.ORDER_CONTENT = row.ORDER_CONTENT
  formDatajiancha.APPLY_TIME = formatDateTime(row.APPLY_TIME)
  formDatajiancha.REPORT_TIME = formatDateTime(row.REPORT_TIME)
  formDatajiancha.LOITEM_CNAME = row.LOITEM_CNAME
  formDatajiancha.ORDER_RPT_RESULT = row.ORDER_RPT_RESULT
  formDatajiancha.id = row.id

  jianchaVisible.value = true
}

// 更新任务 & 任务合并
const refreshTaskVisible = ref(false)

// 打开更新任务确认对话框
const refreshTaskDialog = () => {
  refreshTaskVisible.value = true
}

// 确认更新任务
const confirmRefreshTask = () => {
  // 检查是否有选中的患者
  if (!select_row.value.pid || !selectedPvid.value) {
    ElMessage.warning('请先选择患者和入院次数')
    refreshTaskVisible.value = false
    return
  }

  // 关闭对话框
  refreshTaskVisible.value = false

  // 准备参数
  const params = {
    pid: select_row.value.pid,
    pvid: selectedPvid.value
  }
  // 使用post调用接口
  post(`${api_url}/task/refreshTask`, JSON.stringify(params))
    .then(data => {
      // 根据error_code判断是否成功
      if (data.error_code === 0) {
        // 处理成功响应
        ElMessage.success('任务更新成功')
        console.log('任务更新成功:', data)
      } else {
        // 返回了非0的error_code
        throw new Error(data.msg || '任务更新失败')
      }
    })
    .catch(error => {
      ElMessage.error('错误：' + error)
      console.error('任务更新失败:', error)
    })
}

// 打开任务合并确认对话框
const mergeTaskVisible = ref(false)

const mergeTaskDialog = () => {
  mergeTaskVisible.value = true
}

// 确认任务合并
const confirmMergeTask = () => {
  // 检查是否有选中的患者
  if (!select_row.value.pid || !selectedPvid.value) {
    ElMessage.warning('请先选择患者和入院次数')
    mergeTaskVisible.value = false
    return
  }

  // 关闭对话框
  mergeTaskVisible.value = false

  // 准备参数
  const params = {
    pid: select_row.value.pid,
    pvid: selectedPvid.value
  }

  // 使用post调用接口
  post(`${api_url}/task/mergeTask`, JSON.stringify(params))
    .then(data => {
      // 根据error_code判断是否成功
      if (data.error_code === 0) {
        // 处理成功响应
        ElMessage.success('任务合并成功')
        console.log('任务合并成功:', data)
      } else {
        // 返回了非0的error_code
        throw new Error(data.msg || '任务合并失败')
      }
    })
    .catch(error => {
      ElMessage.error('错误：' + error)
      console.error('任务合并失败:', error)
    })
}

// 新增检验
const jianyanVisible = ref(false)
const testProjectOptions = ref([
  { value: '血常规', label: '血常规' },
  { value: '尿常规', label: '尿常规' },
  { value: '肝功能', label: '肝功能' }
])

const formDataJianyan = reactive({
  XMMC: '',
  BZLX: '',
  APPLY_TIME: '',
  JYJG: '',
  JGCK: '',
  JGBZ: '',
  JGDW: '',
  JGSJ: '',
  QSSJ: '',
  YZNR: '',
  SHSJ: '',
  BGSJ: ''
})

const dialogJianyan = () => {
  if (Object.keys(select_row.value).length === 0) {
    ElMessage.error('请先选择患者')
    return
  }
  jianyanVisible.value = true
  Object.keys(formDataJianyan).forEach(key => {
    formDataJianyan[key] = ''
  })
}

const addJianyan = async () => {
  try {
    const apiUrl = formDataJianyan.id
      ? `${api_url}/recordmanage/jy_result_update`
      : `${api_url}/recordmanage/jy_result_add`

    const data_list = await post(apiUrl, JSON.stringify({
      ...formDataJianyan,
      PID: select_row.value.pid,
      pvid: selectedPvid.value
    }))

    if (data_list.error_code !== 0) {
      ElMessage.error(data_list.msg)
    } else {
      ElMessage.success(`检验${formDataJianyan.id ? '修改' : '添加'}成功`)
      jianyanVisible.value = false
      getTables()
    }

  } catch (error) {
    console.error('添加检验失败:', error)
    ElMessage.error('检验添加失败')
  }
}

// 检验 删
const deleteJianyan = async (row) => {
  load.value = true
  await ElMessageBox.confirm('确定要删除该检验记录吗？', '警告', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  })

  let url = `${api_url}/recordmanage/jy_result_delete`
  post(url, JSON.stringify({
    id: row.id
  })).then(res => {

    ElMessage.success(`删除成功`)
    getTables()
    load.value = false
  }).catch(error => {
    load.value = false
    console.error('发生错误:', error);
  });
}
// 检验 改
const editJianyan = (row) => {
  // 填充表单数据
  formDataJianyan.XMMC = row.XMMC
  formDataJianyan.BZLX = row.BZLX
  formDataJianyan.APPLY_TIME = formatDateTime(row.APPLY_TIME)
  formDataJianyan.JYJG = row.JYJG
  formDataJianyan.JGCK = row.JGCK
  formDataJianyan.JGBZ = row.JGBZ
  formDataJianyan.JGDW = row.JGDW
  formDataJianyan.JGSJ = formatDateTime(row.JGSJ)
  formDataJianyan.QSSJ = formatDateTime(row.QSSJ)
  formDataJianyan.BGSJ = formatDateTime(row.BGSJ)
  formDataJianyan.SHSJ = formatDateTime(row.SHSJ)
  formDataJianyan.YZNR = row.YZNR
  formDataJianyan.id = row.id

  jianyanVisible.value = true
}

const viewBingliVisible = ref(false)
const viewBingliData = reactive({
  title: '',
  content: '',
  document_time: '',
  create_time: '',
  update_time: ''
})

// 查看病历
const viewBingli = (row) => {
  viewBingliData.title = row.title
  viewBingliData.content = row.content
  viewBingliData.document_time = row.document_time
  viewBingliData.create_time = row.create_time
  viewBingliData.update_time = row.update_time
  viewBingliVisible.value = true
}

/**
 * 更新患者数据
 * @param {string} type - 更新类型：emr(病例)、yz(医嘱)、jc(检查)、jy(检验)
 */
const updatePatientData = (type) => {
  if (!select_row.value.pid || !selectedPvid.value) {
    ElMessage.error('请先选择患者和入院次数')
    return
  }

  // 根据传入的类型确定要传递的参数值
  const params = {
    pid: select_row.value.pid,
    pvid: selectedPvid.value,
    emr: type === 'emr' ? 1 : 0,
    yz: type === 'yz' ? 1 : 0,
    jc: type === 'jc' ? 1 : 0,
    jy: type === 'jy' ? 1 : 0
  }
  // debugger
  const queryString = new URLSearchParams(params).toString();
  const url = `${api_url}/MedicalRecords/updatePatientData?${queryString}`;
  // 调用更新患者数据的API
  get(url,
    // JSON.stringify(params)
  ).then(res => {
    if (res.error_code === 0) {
      ElMessage.success('更新成功')
      // 更新后刷新当前数据
      getTables()
    } else {
      ElMessage.error(res.msg || '更新失败')
    }
  }).catch(error => {
    console.error('更新患者数据失败:', error)
    ElMessage.error('更新失败，请稍后重试')
  })
}

</script>

<style scoped>
.content {
  height: 60vh;
}

.medical-record {
  height: 100%;
}

.patient-sidebar {
  border-right: 1px solid #e6e6e6;
  background-color: #f5f7fa;
  display: flex;
  flex-direction: column;
}

.sidebar-header {
  padding: 20px;
  border-bottom: 1px solid #e6e6e6;
}

.ward-select {
  width: 100%;
}

.patient-list {
  flex: 1;
  border-right: none;
  overflow-y: scroll;
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.task-section {
  margin-top: 20px;
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.el-main {
  padding: 20px;
  background-color: #fff;
}

.view-content {
  padding: 20px;
}

.view-item {
  margin-bottom: 20px;
}

.view-item .label {
  font-weight: bold;
  margin-right: 10px;
  color: #606266;
}

.content-box {
  margin-top: 10px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
  min-height: 100px;
  white-space: pre-wrap;
  line-height: 1.6;
}
</style>