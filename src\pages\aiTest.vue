<template>
  <div>
    <div>
      <h4>复合词语拆多词语</h4>
      <div style="width: 1200px;height: 500px;">
        <el-input type="textarea" style="width: 1200px;height: 500px;" :rows="23" v-model="word1"></el-input>
      </div>
      <p>{{ streamContent1 }}</p>
      <el-button :disabled="loads.load1" v-loading="loads.load1" @click="completion1">生成</el-button>
      <!-- <el-button v-if="streamContent1" type="primary" @click="look(streamContent1)">查看</el-button> -->
      
    </div>

    <div>
      <h4>RAG单标签</h4>
      <p>{{ streamContent2 }}</p>
      <div><el-input type="textarea" style="width: 400px;" v-model="value2"></el-input></div>
      <el-button :disabled="loads.load2" v-loading="loads.load2" @click="completion2">生成</el-button>
      <!-- <el-button v-if="streamContent2" type="primary" @click="look(streamContent2)">查看</el-button> -->

    </div>

    <div>
      <h4>RAG多标签</h4>
      <p>{{ streamContent3 }}</p>
      <div><el-input type="textarea" style="width: 400px;" v-model="value3"></el-input></div>
      <el-button :disabled="loads.load3" v-loading="loads.load3" @click="completion3">生成</el-button>
      <!-- <el-button v-if="streamContent3" type="primary" @click="look(streamContent3)">查看</el-button> -->


    </div>

    <div>
      <h4>RAG最新单标签接口</h4>
      <p>{{ streamContent4 }}</p>
      <div><el-input type="textarea" style="width: 400px;" v-model="value4"></el-input></div>
      <el-button :disabled="loads.load4" v-loading="loads.load4" @click="completion4">生成</el-button>
      <!-- <el-button v-if="streamContent4" type="primary" @click="look(streamContent4)">查看</el-button> -->
    </div>
    
  </div>
  <el-dialog v-model="showDialog" title="完整内容" width="60%">
  <el-scrollbar height="600px">
    <div style="max-height:60vh;overflow:auto;word-break:break-all;">
      <pre style="white-space: pre-wrap;">{{ curData }}</pre>
    </div>
  </el-scrollbar>
 </el-dialog>
</template>

<script setup>
import { onMounted, ref } from 'vue'
import { post } from '@/utils/request.js'
import { json } from "d3";
import { ElMessage } from 'element-plus';
import { baseUrl } from '../utils/baseUrl';

const showDialog = ref(false)
const curData = ref()
const look = (data) => {
  showDialog.value = true
  curData.value = data
}
const streamContent1 = ref('')
const streamContent2 = ref("")
const streamContent3 = ref("")
const streamContent4 = ref("")
const value1 = ref("进食服药主动,睡眠进食可,食欲,未查及幻觉妄想等症状")
const value2 = ref("")
const value3 = ref("")
const value4 = ref("")
const loads = ref({
  load1: false,
  load2: false,
  load3: false,
  load4: false,
})
const session_id = ref()
const load = ref(false)

const word1 = ref(`;; 作者: [你的名字]
;; 版本: 1.0
;; 模型: (适用于 GPT-4, Claude 3 等)
;; 用途: 将精神病学的复合症状标签，精确拆解为原子化的子标签，并以严格的JSON格式输出。

;; 设定如下内容为你的 *System Prompt*
(defun 精神症状分析师 ()
  "你是一个逻辑极其严谨的精神病学领域数据处理器"
  (身份 . "专业的精神病学领域助手")
  (专长 . ("症状分析" "概念拆分" "逻辑推理"))
  (原则 . ("忠于原文" "绝对精确" "无冗余"))
  (工作模式 . "确定性算法模式"))

(defun 症状标签拆解 (原始标签)
  "定义了如何将一个标签拆分为子标签的核心算法"
  (let (标签类型定义 '((复合标签 "包含多个并列概念")
                       (原子标签 "单一且不可再分的概念")))

    ;; 核心拆分规则
    (拆分规则 '(
      (独立性原则 "每个子标签必须是独立的、完整的症状描述")
      (修饰语复制原则 "修-饰语(如: 否认, 持续性, 伴有)必须被精确复制到每个对应的子标签中")
      (原子处理原则 "原子标签本身即为其自身的唯一子标签")
      (语义守恒原则 "拆分后的所有子标签合集必须与原始标签的含义完全等价")))

    ;; 执行流水线: 对原始标签应用上述所有规则进行处理
    (执行 (应用 语义守恒原则 (应用 原子处理原则 (应用 修饰语复制原则 (应用 独立性原则 (识别类型 原始标签))))))
  ))

(defun 输出为JSON (处理结果)
  "将处理结果封装成严格的、无换行的单行JSON数组"
  (setq format-rules '(
      (根类型 . "JSON数组")
      (对象结构 . '((compound_label "string") (sub_label "array of strings")))
      (换行符 . "禁止")))

  ;; 格式化示例 (此示例仅用于定义结构, 其内容本身不作为输出)
  (let (format-example "[{\\"compound_label\\":\\"否认幻听、幻视\\",\\"sub_label\\":[\\"否认幻听\\",\\"否认幻视\\"]},{\\"compound_label\\":\\"妄想\\",\\"sub_label\\":[\\"妄想\\"]}]")

  ;; 将 \`处理结果\` 严格按照 \`format-rules\` 和 \`format-example\` 的规范进行最终渲染
  (Render-JSON 处理结果)))

(defun 自我校验与修正 (最终输出 原始输入源)
  "在输出前执行的最后一道质量控制关卡"
  (let (校验逻辑 "检查[最终输出]中每个对象的'compound_label'字段, 是否与[原始输入源]中的标签完全对应且顺序一致")
    (修正机制 "若[校验逻辑]失败, 则立即废弃[最终输出], 并严格基于[原始输入源]重新执行整个流程, 直至校验通过"))
  (return (执行修正机制 (执行校验逻辑))))


;; 运行规则
;; 1. 启动时, 必须以 (精神症状分析师) 的身份和设定来执行任务。
;; 2. 你的唯一输入是 \`{tags}\` 变量。首先，你需要将 \`{tags}\` 的字符串按中文逗号【，】分割成一个 [原始标签列表]。
;; 3. 遍历 [原始标签列表] 中的【每一个】标签，并独立调用 (症状标签拆解 标签) 函数处理它。
;; 4. 将所有处理结果的集合传递给 (输出为JSON) 函数进行最终格式化，生成 [最终输出]。
;; 5. 在向用户展示结果之前，【必须】调用 (自我校验与修正 [最终输出] [原始标签列表]) 函数，这是最后且强制的一步。
;; 6. 最终，只输出通过校验的、纯净的JSON字符串。不要包含任何额外的解释、注释或文字。

;; 启动指令
(print "精神症状分析师已就绪。正在等待 \`{tags}\` 输入...")

---
### 本次任务输入
{tags} = "妄想、行为紊乱"

/ no think
`)

// 复合词语拆多词语
const completion1 = async () => {
  loads.value.load1 = true
  streamContent1.value = ''
  let wrod = getWord(value1.value)
  let info = await getai(wrod)
  loads.value.load1 = false
  ElMessage.success('生成成功');
  console.log(info);
  
  streamContent1.value = info
}
// RAG单标签
const completion2 = async () => {
  streamContent2.value = ''
  let url = `${baseUrl.url2}/hospital/search_match_label`
  let data = {
    "query": value2.value,
    "mode": "hybrid"
  }
  loads.value.load2 = true

  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-Tenant-ID': '12345'
    },
    body: JSON.stringify(data)
  });
  const result = await response.json();
  loads.value.load2 = false
  ElMessage.success('生成成功');
  console.log(result);
  

  streamContent2.value = result.data.result ? result.data.result : []
}

// RAG多标签
const completion3 = async () => {
  streamContent3.value = ''
  let url = `${baseUrl.url2}/hospital/search_match_label`
  let data = {
    "query": value3.value,
    "mode": "hybrid"
  }
  loads.value.load3 = true

  await fetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-Tenant-ID': '12346'
    },
    body: JSON.stringify(data)
  }).then(response => response.json()).then(res => {
    loads.value.load3 = false
    ElMessage.success('生成成功');

    streamContent3.value = res.data
    console.log(res);
    
  }).catch(error => {
    loads.value.load3 = false
  ElMessage.warning('生成失败');

  });
}



// RAG最新单标签接口
onMounted(() => {
  getSessionId()
})
const getSessionId = () => {
  let url = `${baseUrl.url1}/api/v1/chats/253acb78525911f0a1df0e0ff21c6e0a/sessions`
  let data = {
    name: 'test1',
  }
  fetch(url, {
    method: 'POST',
    headers: {
      'Authorization': 'Bearer ragflow-E3Y2Y0NmYwNTI1NzExZjA5OTI0MGUwZm'
    },
    body: JSON.stringify(data)
  }).then(response => response.json()).then(res => {
    session_id.value = res.data.id
  }).catch(error => {
  ElMessage.warning('生成失败');

  })
}
const completion4 = () => {
  loads.value.load4 = true
  streamContent4.value = ""
  let names = value4.value.trim().split('，').map(s => s.trim()).filter(Boolean)
  const namesStr = names.map(n => `“${n}”`).join(', ')
  if (session_id.value) {

    let url = `${baseUrl.url1}/api/v1/chats/253acb78525911f0a1df0e0ff21c6e0a/completions`
    let data = {
      session_id: session_id.value,
      question: `分别各自搜索 ${namesStr} 的所有同义词或相关的 \no think,`,
      stream: false,
    }
    
    fetch(url, {
      method: 'POST',
      headers: {
        'Authorization': 'Bearer ragflow-E3Y2Y0NmYwNTI1NzExZjA5OTI0MGUwZm'
      },
      body: JSON.stringify(data)
    }).then(response => response.json()).then(res => {
      completion4Again()
    }).catch(error => {
      loads.value.load4 = false
    })
  } else {
    getSessionId()
  }
}
const completion4Again = () => {
  let url = `${baseUrl.url1}/api/v1/chats/253acb78525911f0a1df0e0ff21c6e0a/completions`
  let data = {
    session_id: session_id.value,
    question: `将上述搜索显示的内容整理为list[dict1, dict2,...]的json数据结构，比如：分别对列表里每一个search内容进行检索，并对每一个结果放到“subtag”的list里[<search检索结果1>， <search检索结果2>, ...] // 则输出应为: [ { "query": search1, "subtag": [{"result": <search1检索结果1>, "cite":}， {"result": <search1检索结果2>, , "cite":}, ...], }, { "query": search2, "subtag": [{"result": <search2检索结果1>, "cite":}， {"result": <search2检索结果2>, , "cite":}, ...], } { "query": "search3", "subtag": [], # search3没有结果则为空list } ]`,
    stream: false,
  }
  fetch(url, {
    method: 'POST',
    headers: {
      'Authorization': 'Bearer ragflow-E3Y2Y0NmYwNTI1NzExZjA5OTI0MGUwZm'
    },
    body: JSON.stringify(data)
  }).then(response => response.json()).then(res => {
    loads.value.load4 = false
    streamContent4.value = res.data.answer
  ElMessage.success('生成成功');

  }).catch(error => {
    loads.value.load4 = false
  ElMessage.warning('生成失败');

  })
}


const getWord = (tags) => {
  // let word =`## 角色：你是一个专业的精神病学领域助手，擅长分析和拆分复杂的症状描述。
  //       ## 核心任务：
  //       你的任务是根据我提供的精神病症标签，将其拆分为逻辑上独立、语义明确的子标签。
  //
  //       ### 标签类型定义：
  //       1.  **复合标签**： 包含多个并列的症状或特征，可能通过顿号、逗号、"及"、"和"等连接词连接，或语义上是多个并列概念。
  //       2.  **原子标签**： 单一的、不可再拆分的症状或特征。
  //       ### 拆分与处理规则：
  //       1.  **独立性原则**： 每个子标签都必须是一个独立的、完整的症状或特征描述。
  //       2.  **修饰语复制**： 如果原标签中包含修饰语（如："否认"、"拒绝"、"部分"、"持续性"、"反复性"、"伴有"、"无"等），这个修饰语需要被复制到每个对应的子标签中。
  //       3.  **原子标签处理**： 如果一个标签本身是原子化的、不可再分的单一症状，它将作为其自身的唯一子标签。在这种情况下，只会生成一个 \`sub_label\`。
  //       4.  **避免冗余**： 确保拆分后的子标签不包含重复信息，且与原标签的含义完全一致。
  //       5.  **子标签数量**： 只有当复合标签能够被拆分出多个独立概念时，才生成多个子标签。
  //       ### 验证与修正机制：
  //       1.  **输出校验**：在生成最终的JSON输出后，你 **必须** 执行一步最终校验。检查你生成的每一个JSON对象中的 \`"compound_label"\` 字段，是否与下方 \`${tags}\` 中提供的原始标签 **完全对应**。
  //       2.  **错误修正**：如果校验发现输出与输入不符（例如，输出了示例中的内容，而不是实际输入的内容），你 **必须立刻废弃** 错误的输出，并 **严格且仅基于** \`${tags}\` 的内容重新生成一次，确保结果的绝对准确性。
  //       ### 输出格式：
  //       - 严格遵循JSON格式，将所有对象放入一个JSON数组中。
  //       - 输出的JSON不要包含换行符。
  //       - **格式示例 (此示例仅用于展示格式，其内容不应在最终输出中出现，除非输入标签与之完全相同):**
  //       \`[{"compound_label": "否认幻听、幻视","sub_label": ["否认幻听", "否认幻视"]},{"compound_label": "妄想","sub_label": ["妄想"]}]\`
  //       ## 待处理标签输入：
  //       你的 **唯一** 输入源是下面的 \`${tags}\` 变量。请 **忽略示例中的具体内容**，只处理 \`${tags}\` 传入的标签。每个标签用中文逗号【，】分隔。
  //       ${tags}
  // 			/ no think
  // 		`
  // word1.value.replace(/{tags}/g, tags)

  return word1.value
}

const getai = async (text) => {

  let url = `${baseUrl.ai_url}`
  let token_key = "sk-a11f833afe094bb9971932a5ea001834"

  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Cache-Control': 'no-cache',
      'Authorization': 'Bearer ' + token_key,
    },
    body: JSON.stringify({
      messages: [{ role: "user", content: text }],
      temperature: 0.0,
      model: 'qwen3-32b',
      enable_thinking: false,
      stream: false
    })
  });
  const result = await response.json();
  let str = result.choices[0]['message']['content']
  return str
}

</script>

<style scoped>
.text {
  white-space: pre-line;
}

.item {
  border: 1px solid #eeeeee;
  padding: 15px;
}
.result-box {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 12px 16px;
  margin-bottom: 10px;
  min-height: 60px;
  max-height: 200px;
  overflow-y: auto;
  background: #fafbfc;
  font-size: 14px;
  color: #333;
  word-break: break-all;
}
</style>