<template>
  <div class="tableData">
    <h3>{{ route.meta.title }}</h3>
        <div class="topMenu">
            <div class="menuBox">
                <el-button @click="showtag">病症标签统计</el-button>
                <el-select
                  v-model="selectedDepartment"
                  placeholder="筛选病区"
                  @change="getTagPage1"
                  style="width: 200px;padding-left: 10px;"
                  :disabled="isDisabled"
                  clearable
                >
                    <el-option
                      :label="item.item_name"
                      :value="item.item_no"
                      v-for="item in select_list"
                      :key="item.id"
                    />
                </el-select>
            </div>
        </div>
    <el-table class="table"  :data="user_list" style="width: 100%;" :max-height="tableHeight" v-loading="load">
      <el-table-column type="index" label="生成" width="100"></el-table-column>
      <el-table-column label="姓名" width="200">
        <template #header>
          <el-input v-model="search" placeholder="输入姓名" />
        </template>
        <template #default="scope">
            {{scope.row.patient_name}}
        </template>
      </el-table-column>
      <el-table-column label="标签">
        <template #default="scope">
          <div v-for="(item,index) in scope.row.list" :key="index">
            <div style="margin-bottom: 15px;">
              <h3>{{item.label}}--{{item.update_time}}</h3>
              <el-tag v-for="row in item.content.split(',')">{{row}}</el-tag>
              <el-button type="primary" size="small" @click="showEditTag(item, scope.row)">修改标签</el-button>
            </div>
          </div>
        </template>
      </el-table-column>

    </el-table>
     <Pagination style="margin-top: 10px; display: flex; justify-content: flex-end;" :currentPage="currentPage"
      :pageSize="pageSize" :total="totalItems" @current-change="handleCurrentChange"
      @size-change="handleSizeChange" />
  </div>

<el-dialog
    v-model="show_disease"
    title="病种"
    width="75%"
>
  <div style="width: 100%;height:400px;overflow-y: scroll">
    <div v-for="(item,index) in disease" :key="index">
      {{item.name}} ({{item.num}}人)
    </div>
  </div>
</el-dialog>

<el-dialog
    v-model="show_tag"
    title="结果"
    width="75%"
>
  <div style="width: 100%;height:400px;overflow-y: scroll" v-loading="load">
    <div v-if="info.ai_case">
      <h3>结果</h3>
      <div>{{info.ai_case}}</div>
      <h3>病种</h3>
      <div>{{info.disease}}</div>
      <h3>分析</h3>
      <div>{{info.ret_tmp}}</div>
      <h3>模版</h3>
      <div>{{info.template}}</div>
      <h3>标签</h3>
      <div>
        <h4>正常症状</h4>
        <div>{{info.labels['正常症状']}}</div>
      </div>
      <div>
        <h4>异常症状</h4>
        <div>{{info.labels['异常症状']}}</div>
      </div>
    </div>
  </div>
</el-dialog>


<el-dialog
    v-model="show_tag_list"
    title="病症标签"
    width="75%"
>
  <div>
    <h3>正常症状标签:{{zc_newArrs.length}}个</h3>
    <h3>异常标签症状标签:{{yc_newArrs.length}}个</h3>
  </div>
  <div class="tag_list tag_lists">
    <h4 style="margin-bottom: 15px;">正常症状标签</h4>
    <el-tag @close="delTag(row)" closable v-for="row in zc_newArrs" size="large" style="font-size: 16px;margin-left: 15px;margin-bottom: 15px;">{{row}}</el-tag>
    <h4 style="margin-bottom: 15px;">异常标签症状标签</h4>
    <el-tag @close="delTag(row)" closable v-for="row in yc_newArrs" size="large" style="font-size: 16px;margin-left: 15px;margin-bottom: 15px;">{{row}}</el-tag>
  </div>
</el-dialog>

<el-dialog
    v-model="show_edit_tag_dialog"
    title="修改标签"
    width="50%"
>
  <div>
    <el-form :model="editTagForm" label-width="100px">
      <el-form-item label="标签类型">
        <span>{{editTagForm.label}}</span>
      </el-form-item>
      <el-form-item label="标签内容">
        <el-input
            v-model="editTagForm.content"
            type="textarea"
            :rows="4"
            placeholder="请输入标签内容，多个标签用逗号分隔"
        />
      </el-form-item>
    </el-form>
  </div>
  <template #footer>
    <span class="dialog-footer">
      <el-button @click="show_edit_tag_dialog = false">取消</el-button>
      <el-button type="primary" @click="updateTag">确认</el-button>
    </span>
  </template>
</el-dialog>

</template>

<script setup>
import { ref,computed,onMounted,onUnmounted } from 'vue';
import { post } from '@/utils/request.js'
import { confirm, message } from '@/utils/message'
import { useRoute } from 'vue-router';

const route = useRoute();

const load = ref(false)
const tableData = ref([])
const search = ref("")
const show_tag = ref(false)
const info = ref({})
const zc_newArrs = ref([])
const yc_newArrs = ref([])
const tableHeight = ref(0)
const show_tag_list = ref(false)
const show_edit_tag_dialog = ref(false)
// 分页相关数据
const currentPage = ref(1)
const pageSize = ref(10)
const totalItems = ref(0)
// 病区
const selectedDepartment = ref('')
const select_list = ref([])

const editTagForm = ref({
id: '',
label: '',
content: ''
})
import c from "@/utils/config";
const api_url = c.api_url;
// 删除标签
const delTag = (row)=>{

}
// 计算表格高度的函数
const calculateTableHeight = () => {
// 获取视窗高度
const windowHeight = window.innerHeight
// 预留其他元素的空间（如页头、页尾等），这里预留 100px
tableHeight.value = windowHeight - 150
}

const user_list = computed(() => {
  // 使用后端分页，直接返回tableData
  // 如果需要本地搜索，可以保留过滤逻辑
  if (search.value) {
    return tableData.value.filter(data_list =>
      data_list.patient_name.toLowerCase().includes(search.value.toLowerCase())
    )
  }
  console.log('tableData.value', tableData.value)
  return tableData.value
})

const getWord = (row)=>{
console.log(row)
show_tag.value = true
load.value = true;
console.log({
  pid:parseInt(row.pid),
  pvid:parseInt(row.pvid)
})
post(`${api_url}/patient/createMedical`, JSON.stringify({
  pid:row.pid,
  pvid:row.pvid,
})).then(res => {
  info.value = res.data
  load.value = false
}).catch(error => {
  load.value = false
});
}

const showtag = ()=>{
show_tag_list.value = true
let list = tableData.value
let tag_zc_str = ""
let tag_yc_str = ""
list.forEach((item)=>{
  item.list = item.list.forEach((items)=>{
    if(items.label==='正常症状'){
      tag_zc_str+=","+items.content
    }
    if(items.label==='异常症状'){
      tag_yc_str+=","+items.content
    }
  })
})
//去掉重复的
let zc_newArr = [...new Set(tag_zc_str.split(",").filter(Boolean))]
let yc_newArr = [...new Set(tag_yc_str.split(",").filter(Boolean))]
console.log('正常标签：',zc_newArr.join(","))
console.log('异常标签：',yc_newArr.join(","))
zc_newArrs.value = zc_newArr
yc_newArrs.value = yc_newArr
}

const getTag = (row)=>{
  load.value = true;
  const params = {
    page: currentPage.value,
    limit: pageSize.value
  }
  if (selectedDepartment.value) {
    params.dept_id = selectedDepartment.value
  }
  post(`${api_url}/patient/getAllLabels`, JSON.stringify(params)).then(res => {
    load.value = false;
  let arr = []
  // res.forEach((item)=>{
  //   let index = arr.findIndex((row)=>row.pid==item.pid)
  //   arr[index]['']
  // })
  // 假设后端返回格式为 {data: [...], total: 100}
  tableData.value = !res.data.length ? res.data.data : res.data // 兼容不同返回格式
  totalItems.value = res.data.total ? res.data.total : res.total // 设置总条数
  // window.localStorage.setItem('tableData.value',JSON.stringify(tableData.value))
  // tableData.value = tableData.value.map((item)=>{
  //   row.tag_list =res.data_list
  //   return row
  // })
}).catch(error => {
  load.value = false;
  console.error('获取数据失败', error)
});
}
const getTagPage1 = ()=>{
  currentPage.value = 1
  getTag()
}
const showEditTag = (item, row) => {
item.content = item.content.replace(/,,,/g,'')

editTagForm.value = {
  id: item.id,
  label: item.label,
  content: item.content
}
show_edit_tag_dialog.value = true
}

const updateTag = () => {
post(`${api_url}/patient/updateLabels`, JSON.stringify({
  id: editTagForm.value.id,
  content: editTagForm.value.content
})).then(res => {
  if (res.error_code === 0) {
    ElMessage.success('修改成功')
    show_edit_tag_dialog.value = false
    getTag() // 刷新列表
  } else {
    ElMessage.error(res.message || '修改失败')
  }
}).catch(error => {
  ElMessage.error('修改失败')
})
}

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val
  // 页码变化时重新获取数据
  getTag()
}

// 处理每页显示条数变化
const handleSizeChange = (val) => {
  pageSize.value = val
  // 重置为第一页
  currentPage.value = 1
  // 重新获取数据
  getTag()
}

// 获取病区
const getWardList = ()=>{
  let url = `${api_url}/AiCase/getWardList`
  load.value = true
  post(url, JSON.stringify({})).then(res => {
    select_list.value = res.data
    load.value = false
  }).catch(error => {
    load.value = false
    console.error('发生错误:', error);
  });
}

const generateMedical = ()=>{
  load.value = true
  post(`${api_url}/patient/createMedical`, JSON.stringify({})).then(res => {
    load.value = false
    message("生成成功！")
  }).catch(error => {
    load.value = false
    console.error('发生错误:', error);
  });
}

const getPatientLabels = ()=>{
  load.value = true
  post(`${api_url}/patient/getAllLabels`, JSON.stringify({})).then(res => {
    console.log(' res.data', res.data);
    
    user_list.value = res.data
    load.value = false
  }).catch(error => {
    load.value = false
    console.error('发生错误:', error);
  });
}

const updateLabels = ()=>{
  load.value = true
  post(`${api_url}/patient/updateLabels`, JSON.stringify({
    pid: select_row.value.pid,
    labels: select_row.value.labels
  })).then(res => {
    load.value = false
    message("保存成功！")
    dialogVisible.value = false
    getPatientLabels()
  }).catch(error => {
    load.value = false
    console.error('发生错误:', error);
  });
}

const getUser = ()=>{
  load.value = true
  let url = `${api_url}/AiCase/getPatientList`
  post(url, JSON.stringify({ward_id:selectedDepartment.value})).then(res => {
    user_list.value = res.data
    load.value = false
  }).catch(error => {
    load.value = false
    console.error('发生错误:', error);
  });
}

onMounted(() => {
  
getWardList()
getTag()
calculateTableHeight()
window.addEventListener('resize', calculateTableHeight)
})
onUnmounted(() => {
window.removeEventListener('resize', calculateTableHeight)
})
</script>
<style scoped>
.tableData {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    background-color: #ffffff;
    padding: 10px;
    box-sizing: border-box;
}
h3 {
    height: 40px;
}
.topMenu {
    display: flex;
    justify-content: flex-start;
    margin-bottom: 10px;
    flex-wrap: wrap;
    gap: 5px;
}

.menuBox {
    display: flex;
    justify-content: flex-start;
    margin-bottom: 10px;
    flex-wrap: wrap;
    gap: 5px;
}

.table {
    flex: 1;
    overflow-y: auto;
}
.tag_list{
  padding: 24px;
}
.tag_lists{
  height: 600px;
  overflow-y: scroll;
}
</style>