// 提取多标签
export const extractionWord = (tags)=>{
    return `### 复合标签精准识别指令
        **角色**：专业精神医学文本分析模型
        **核心任务**：从症状标签列表中提取所有符合以下定义的复合标签
        #### 复合标签定义
        一个文本标签满足复合标签当且仅当：
        1. **包含 ≥2 个独立临床意义的症状**
        - 每个症状需满足：
        - 可独立诊断（如DSM-5/ICD-11单独编码）
        - 可独立评估（如PANSS量表中分属不同维度[6](@ref)）
        - 临床记录中常作为独立条目（如"幻听"与"妄想"分项记录[8](@ref)）
        2. **通过显式语法连接**
        - 连词模式：'顿号（、）'、'及'、'和'、'并'、'同时伴有'、'或'
        - 行为涵盖模式：'否认[症状A]、[症状B]、出现[症状X]及[症状Y]'
        
        #### 刚性排除条件（优先级最高）
        **以下情况即使含连接词也不视为复合标签**：
        - 症状属于**同义反复**（如"情绪低落、闷闷不乐"→均描述抑郁心境）
        - 症状存在**完全包含关系**（如"沉默不语"是"言语减少"的极端表现）
        - 症状属于**同一症状维度的不可分细化**（如"思维迟缓、反应慢"→均属认知速度维度）
        
        #### 关键判定逻辑
        markdown
        | 场景 | 判定原则 | 示例（√=复合标签 ×=非复合） |
        |---------------------|-----------------------------|----------------------------------|
        | 常伴随但可独立评估 | √ 独立存在即视为复合 | √ "自语、自笑"[6](@ref) |
        | 临床关联性强 | √ 不影响独立性判定 | √ "幻觉、妄想"[8](@ref) |
        | 描述同一病理机制 | × 视为单一标签 | × "行为退缩、不与人交往" |
        | 语义高度重叠 | × 排除 | × "言语减少、沉默不语" |
        输入(tag为一个独立标签)：{"symptom_tags":${tags}}
        
        1.列表需要显示的是具体的标签内容而不是标签抬头；
        2.注意标签必须输出的是原标签的完整内容，不要做任何的拆分;
        3.输出：仅返回严格符合定义的复合标签列表（如["标签A","标签B"]），需要注意的是:无则返回"null"
        特别注意！！！！！
        一旦判定标签是复合标签，直接返回该标签原始的所有内容，不得对这个内容进行二次加工或者修改。
        / no think
    `
}
// 提取单标签
export const extractionItemWord = ()=>{
    return `##角色：你是一个专业的精神病学领域助手，擅长分析和拆分复杂的症状描述。
    ##任务：你的任务是根据我提供的精神病症标签，将其拆分为逻辑上独立、语义明确的子标签。请注意，你收到的标签列表可能包含两种类型：
    复合标签： 包含多个并列的症状或特征，可能通过顿号、逗号、"及"、"和"等连接词连接，或语义上是多个并列概念。
    原子标签： 单一的、不可再拆分的症状或特征。
    拆分与处理规则：
    独立性原则： 每个子标签都必须是一个独立的、完整的症状或特征描述。
    修饰语复制： 如果原标签中包含修饰语（如："否认"、"拒绝"、"部分"、"持续性"、"反复性"、"伴有"、"无"等），这个修饰语需要被复制到每个对应的子标签中。
    原子标签处理： 如果一个标签本身是原子化的、不可再分的单一症状（即不属于复合标签），那么它将作为其自身的唯一子标签。在这种情况下，只会生成一个 sub_label_1，其内容与 compound_label 完全一致。
    避免冗余： 确保拆分后的子标签不包含重复的、冗余的信息，且与原标签的含义完全一致。
    子标签数量： 只有当复合标签能够被拆分出多个独立概念时，才生成 sub_label_2, sub_label_3 等。如果只有单个子标签，则只生成 sub_label_1。
    输出格式：
    请严格遵循以下JSON格式输出。每个原始标签（无论是复合还是原子）及其拆分（或保留）后的子标签将被包含在一个独立的JSON对象中。
    {
        "compound_label": "原始标签的文本",
        "sub_label": [ "拆分出的第一个子标签", "拆分出的第二个子标签" ,// ... 如果有更多子标签，依此类推]
    }
    // ... 针对所有需要处理的标签重复此结构
    Use code with caution.
    Json
    例如，对于输入标签列表 \'否认幻听、幻视\' 和 \'妄想\'，输出格式不要存在换行，输出应为：
    [{"compound_label": "否认幻听、幻视","sub_label": ["否认幻听", "否认幻视"]},{"compound_label": "妄想","sub_label": ["妄想"]}]

    ##每个标签用中文逗号【，】分隔，以下为待处理标签：{tags}
    / no think
    `
}