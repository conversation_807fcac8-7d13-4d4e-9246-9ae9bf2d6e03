<template>
    <div class="tableData">
        <h3>{{ route.meta.title }}</h3>
        <div class="topMenu">
            <div class="menuBox">
                <el-button type="primary" @click="handleGenerate">生成</el-button>
            </div>
        </div>
        <el-table ref="tableRef" class="table" :data="filteredTableData" v-loading="load">
            <el-table-column type="selection" width="55" />
            <el-table-column label="患者名称" prop="pat_name"></el-table-column>
            <el-table-column label="结果" prop="result">
                <template #default="{ row }">
                    <p>{{ resultMap[row.pid] }}</p>
                </template>
            </el-table-column>
            <el-table-column label="数据源" prop="document">
                <template #default="{ row }">
                    <el-button type="primary" @click="lookContent(row)">查看</el-button>
                </template>
            </el-table-column>
        </el-table>
    </div>
    <el-dialog v-model="dialogVisible" title="病例内容" width="50%" draggable>
        <template #default>
            <p>{{ currentContent }}</p>
        </template>
    </el-dialog>
    <!-- 标签查看对话框 -->
    <el-dialog :closeOnClickModal="false" v-model="tagDialogVisible" title="患者标签信息" width="50%" draggable>
        <div class="tag-section-box">
            <div v-loading="newLoad" style="width: 100%; display: flex; flex-direction: column;">
                <el-scrollbar height="400px">
                    <!-- 文档标签列表 -->
                    <div v-if="newDocumentTagList.length > 0">
                        <h3 class="tag-section-title">新病历标签</h3>
                        <div v-for="(docItem, docIndex) in newDocumentTagList" :key="docIndex"
                            class="document-tag-group">
                            <div class="document-info">
                                <span class="document-type">{{ docItem.type }}</span>
                                <span class="document-date">{{ docItem.date }}</span>
                            </div>
                            <h5>内容</h5>
                            <p>{{ docItem.document }}</p>
                            <h5>标签</h5>
                            <div class="tag-container">
                                <el-tag v-for="(tag, key, tagIndex) in docItem.tags" :key="tagIndex"
                                    class="tag-item document-tag" effect="plain">
                                    {{ key + tag }}
                                </el-tag>
                            </div>
                            <h5>无效标签</h5>
                            <div class="tag-container">
                                <el-tag v-for="(item, tagIndex) in docItem.invalid_list" :key="tagIndex"
                                    class="tag-item document-tag" effect="plain">
                                    {{ item.tag }}
                                </el-tag>
                            </div>
                            <h5>未找到标签</h5>
                            <div class="tag-container">
                                <el-tag v-for="(item, tagIndex) in docItem.not_found_list" :key="tagIndex"
                                    class="tag-item document-tag" effect="plain">
                                    {{ item.tag }}
                                </el-tag>
                            </div>
                        </div>
                        <div v-for="(docItem, docIndex) in templateList" :key="docIndex" class="document-tag-group">
                            <div v-if="docItem.name === '模板内容'">
                                <h5>{{ docItem.name }}</h5>
                                <div class="tag-container">
                                    <p>{{ docItem.value }}</p>
                                </div>
                            </div>
                            <div v-else-if="docItem.name === '模板分类'">
                                <h5>{{ docItem.name }}</h5>
                                <div class="tag-container">
                                    <div v-for="(item, key, tagIndex) in docItem.value" :key="tagIndex"
                                        class="tag-container" style="width: 100%;">
                                        <div style="width: 100%;">
                                            <h6>{{ key }}</h6>
                                        </div>
                                        <el-tag v-for="(item1, tagIndex) in item" :key="tagIndex"
                                            class="tag-item document-tag" effect="plain">
                                            {{ item1.tag ? item1.tag : item1 }}
                                        </el-tag>
                                    </div>
                                </div>
                            </div>
                            <div v-else-if="docItem.name === '结果'">
                                <h5>{{ docItem.name }}</h5>
                                <div class="tag-container">
                                    <p>{{ docItem.value }}</p>
                                </div>
                            </div>
                            <div v-else>
                                <h5>{{ docItem.name }}</h5>
                                <div class="tag-container">
                                    <el-tag v-for="(item, tagIndex) in docItem.value" :key="tagIndex"
                                        class="tag-item document-tag" effect="plain">
                                        {{ item }}
                                    </el-tag>
                                </div>
                            </div>

                        </div>

                    </div>
                </el-scrollbar>

                <!-- 待处理标签 -->
                <div v-if="newPendingTagList.length > 0" class="pending-tag-section">
                    <h3 class="tag-section-title">待处理标签</h3>
                    <div class="tag-container">
                        <el-tag v-for="(tag, index) in newPendingTagList" :key="index" class="tag-item pending-tag"
                            type="warning" effect="plain">
                            {{ tag }}
                        </el-tag>
                    </div>
                </div>

                <!-- 无标签数据提示 -->
                <div v-if="newDocumentTagList.length === 0 && !newLoad" class="no-tags">
                    暂无标签数据
                </div>
            </div>
        </div>
    </el-dialog>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import { useRoute } from 'vue-router';
import { baseUrl } from '../utils/baseUrl';

const route = useRoute();
const tableRef = ref(null);


// 新增：标记上传状态
const resultMap = ref({}) // { [row.id]: true/false }

const handleGenerate = async () => {
    const selectedRows = tableRef.value?.getSelectionRows() || [];
    // 先将所有选中行的结果设为“获取中”
    selectedRows.forEach(row => {
        resultMap.value[row.pid] = '获取中...'
    })
    for (const row of selectedRows) {
        try {
            await createDocumentByPatientSerial(row)
        } catch (e) {
            resultMap.value[row.pid] = '获取失败'
            ElMessage.error(`患者 ${row.pat_name} 获取结果失败`)
        }
    }
    ElMessage.success('生成操作已完成')
}

const createDocumentByPatientSerial = async (row) => {
    let url = `${baseUrl.url7}/final?pid=${row.pid}&pvid=${row.pvid}`
    await fetch(url).then((response) => {
        if (!response.ok) {
            throw new Error('Network response was not ok ' + response.statusText)
        }
        return response.json()
    })
        .then((data) => {
            resultMap.value[row.pid] = data.result // 或根据实际返回内容设置
        })
        .catch(() => {
            resultMap.value[row.pid] = '获取失败'
        })
}

const tagDialogVisible = ref(false)
const newDocumentTagList = ref([])
const templateList = ref([])
const newPendingTagList = ref([])
const newLoad = ref(false)

// 完全仿照 task_list 的 getTag 逻辑
const lookContent = (row) => {
    tagDialogVisible.value = true
    newLoad.value = true
    newDocumentTagList.value = []
    newPendingTagList.value = []
    templateList.value = []


    // 注意：这里 row.pid/row.pvid
    let url = `${baseUrl.url7}/?pid=${row.pid}&pvid=${row.pvid}`
    fetch(url).then((response) => {
        if (!response.ok) {
            throw new Error('Network response was not ok ' + response.statusText)
        }
        return response.json()
    })
        .then((data) => {
            const { newTagDatas, pendingData } = getNewTagDatas(data)
            newDocumentTagList.value = newTagDatas
            newPendingTagList.value = pendingData
            newLoad.value = false

        })
        .catch((error) => {
            console.error('获取新标签数据出错:', error)
            newLoad.value = false
        })

}

// 新标签数据处理函数
const getNewTagDatas = (data) => {

    let newTagDatas = []
    let pendingData = []
    for (let key in data) {
        let keyArr = key.split("&")
        let date = keyArr[0]
        let type = keyArr[1]
        let tags = data[key].tags
        let invalid_list = data[key].invalid_list
        let document = data[key].document
        invalid_list = invalid_list.filter((item) => {
            return !item.tag.includes('手签') && !item.tag.includes('医师签名')
        })
        let not_found_list = data[key].not_found_list
        not_found_list.forEach((item) => {
            pendingData.push(item.tag)
        })
        newTagDatas.push({
            date,
            type,
            tags,
            invalid_list,
            not_found_list,
            document
        })
    }

    return { newTagDatas, pendingData }
}


// 当前搜索医师姓名
const curUserName = ref('')
// 当前搜索医师账号
const curHisUser = ref('')
// 计算属性：根据 curUserName 过滤医生名称
const filteredTableData = computed(() => {
    if (!curUserName.value && !curHisUser.value) {
        return tableData.value
    }
    if (curUserName.value || curHisUser.value) {
        return tableData.value.filter(item =>
            item.username && item.username.includes(curUserName.value) &&
            item.his_user && item.his_user.includes(curHisUser.value)
        )
    }
})

const load = ref(false)
const tableData = ref([])



// 重置弹窗
const dialogVisible = ref(false)






const getGridData = () => {
    load.value = true

    const params = {
        // page: currentPage.value,
        // page_size: pageSize.value,
    }
    const queryString = new URLSearchParams(params).toString();
    console.log(params);


    load.value = true;
    fetch(`${baseUrl.prod_test_zhixuee}` + `/v1/api/rpa_task_list/patient_list?${queryString}`, {
        method: 'GET',
        headers: {
            'x-api-key': 'API655c18174462a06ede7ffbfa5aca4270c645b9b78ca30817e3a1bd496f34e04b',
        },
    })
        .then(response => response.json())
        .then(res => {
            console.log(res);
            tableData.value = res.data
            load.value = false;
            // 默认全选
            nextTick(() => {
                if (tableRef.value) {
                    tableData.value.forEach(row => {
                        tableRef.value.toggleRowSelection(row, true)
                    })
                }
            })
        })
        .catch(error => {
            load.value = false;
            console.error('获取数据失败', error)
            ElMessage.error('获取数据失败，请稍后重试')
        });
}



// 更新状态
onMounted(() => {
    getGridData()
})
onUnmounted(() => {
})
</script>
<style scoped>
.tableData {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    background-color: #ffffff;
    padding: 10px;
    box-sizing: border-box;
}

h3 {
    height: 40px;
}

.topMenu {
    display: flex;
    justify-content: flex-start;
    margin-bottom: 10px;
    flex-wrap: wrap;
    gap: 5px;
}

.table {
    flex: 1;
    overflow-y: auto;
}

.tag_list {
    padding: 24px;
}

.tag_lists {
    height: 600px;
    overflow-y: scroll;
}

.menu {
    width: 200px;
}

.pass_select {
    display: flex;
    justify-content: center;
    align-items: center;
}


.tag-section-box {
    display: flex;
    flex-direction: row;
    gap: 10px;
    min-height: 400px;
}

.document-tag-group {
    margin-bottom: 16px;
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 8px;
}

.tag-section-title {
    font-size: 16px;
    font-weight: bold;
    margin: 10px 0 8px 0;
}

.tag-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 6px;
}

.tag-item {
    margin-bottom: 4px;
}

.document-info {
    color: #909399;
    font-size: 13px;
    margin-bottom: 4px;
}

.no-tags {
    color: #bbb;
    text-align: center;
    margin: 30px 0;
}

.pending-tag-section {
    margin-top: 10px;
}

.el-tag.document-tag {
    background: #f4f8fb;
    color: #409EFF;
    border: 1px solid #b3d8ff;
}

.el-tag.pending-tag {
    background: #fff7e6;
    color: #e6a23c;
    border: 1px solid #ffe1b8;
}
</style>