<template>
  <el-container class="medical-record">
    <!-- 左侧病人信息栏 -->
    <el-aside width="240px" class="patient-sidebar">
      <div class="sidebar-header">
        <el-select v-model="selectedDepartment" @change="getUser" placeholder="筛选病区" class="ward-select">
          <el-option :label="item.item_name" :value="item.item_no" v-for="item in select_list" :key="item.id" />
        </el-select>
        <div style="padding-top: 15px;">
          <el-input placeholder="输入患者姓名" @input="searchUser" v-model="user_name" type="text"></el-input>
        </div>
      </div>
      <el-scrollbar height="100%" v-loading="patientLoad">
        <el-menu class="patient-list" >
          <el-menu-item v-for="patient in user_list" @click="selectUser(patient)" :key="patient.pid" :index="patient.pid"
            v-show="patient.show">
            <span>{{ patient.pat_name }}（主治医生：{{ patient.doctor_name }}）</span>
          </el-menu-item>
        </el-menu>
      </el-scrollbar>
    </el-aside>

    <!-- 主要内容区 -->
    <el-main>
      <div class="content-header">
        <h3>
          本次入院最新病历
          <el-select v-model="selectedPvid" @change="getTask" placeholder="选择入院次数" class="ward-select">
            <el-option :value="row" :key="row" v-for="row in parseInt(select_row.pvid)">
              {{ '第' + row + '入院' }}
            </el-option>
          </el-select>
        </h3>
        <div>
          <el-button v-if="select_row.pid" type="primary" @click="getTag">查看标签</el-button>
          <el-button v-if="select_row.pid" type="primary" @click="getDoctorTest">检查检验</el-button>
          <el-button v-if="select_row.pid" type="primary" @click="viewAdviceHistory">查看医嘱</el-button>
          <el-button v-if="select_row.pid" type="primary" @click="viewHistory">查看病历记录</el-button>
          <el-button type="primary" @click="diseaseTypes">查看病种</el-button>
        </div>
      </div>
      <!-- 最新病历表格 -->
      <el-table :data="his_list" border style="margin-bottom: 20px;height: 500px">
        <el-table-column prop="type" label="病历类型" width="180">
          <template #default="scope">
            {{ scope.row.WBMC }}
          </template>
        </el-table-column>
        <el-table-column prop="type" label="创建时间" width="180">
          <template #default="scope">
            {{ scope.row.CREATE_DATE.replace("T", " ")}}
          </template>
        </el-table-column>
        <el-table-column prop="time" label="最后编辑时间" width="180">
          <template #default="scope">
            {{ scope.row.UPDATE_DATE.replace("T", " ") }}
          </template>
        </el-table-column>
        <el-table-column prop="admissionCount" label="对应入院次数" width="180">
          {{ select_row.pvid }}
        </el-table-column>
        <el-table-column prop="admissionTime" label="入院时间">
          {{ select_row.indept_time }}
        </el-table-column>
      </el-table>

      <!-- 任务列表部分 -->
      <div class="task-section">
        <div class="task-header">
          <span>自动创建的病历任务</span>
          <!--          <el-select v-model="taskFilter" style="width: 120px">-->
          <!--            <el-option label="全部" value="all" />-->
          <!--          </el-select>-->
        </div>
        <el-table v-if="taskList && taskList.length > 0" :data="taskList" border style="height: 600px" v-loading="load">
          <el-table-column prop="state" label="病历类型" width="150" />
          <el-table-column prop="create_time" label="任务创建时间" width="200" />
          <el-table-column prop="edit_time" label="计划启动书写时间" width="200" />
          <el-table-column label="创建类别" width="100">
            <template #default="scope">
              <el-tag v-if="scope.row.is_sys === 0">手动</el-tag>
              <el-tag v-if="scope.row.is_sys === 1">自动</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <div v-if="scope.row.status == -1">
                <el-tag type="warning">已删除</el-tag>
              </div>
              <div v-else>
                <div v-if="scope.row.document">
                  <div v-if="scope.row.status === 0 || scope.row.status == 9">
                    <el-tag>待上传</el-tag>
                  </div>
                  <div v-else>
                    <el-tag v-if="scope.row.upload_status === 1 && scope.row.status === 0">上传中</el-tag>
                    <el-tag v-if="scope.row.status === 2">上传成功</el-tag>
                    <el-tag v-if="scope.row.status === -1">取消上传</el-tag>
                    <el-tag v-if="scope.row.status === 3 || scope.row.status === 4">上传失败</el-tag>
                  </div>
                </div>
                <div v-else>
                  <div v-if="scope.row.status == -1">
                    <el-tag>任务已删除</el-tag>
                  </div>
                  <div v-else>
                    <el-tag>待书写</el-tag>
                  </div>
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="create_reason" label="创建原因">
            <template #default="scope">
              <div style="height: 200px;overflow-y: auto">
                {{ scope.row.create_reason }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="create_reason" label="删除原因">
            <template #default="scope">
              <div style="height: 200px;overflow-y: auto">
                {{ scope.row.content }}
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="status" label="操作" width="100">
            <template #default="scope">
              <div>
                <el-button v-if="scope.row.is_sys === 0" type="warning" @click="deleteCas(scope.row)">删除</el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-main>
  </el-container>
  <!--   查看病例-->
  <el-dialog v-model="dialogVisible" title="病例记录" width="80%" draggable>
    <template #default>
      <div v-loading="load">
        <div class="content">
          <div class="list-item" v-for="(item, index) in listings" :key="index">
            <h4>{{ item.WBMC }}</h4>
            <!-- <div class="text">{{ item.WBNR }}</div> -->
            <div class="text" v-html="formatDocument(item.WBNR)" style="white-space: pre-wrap;word-break: break-all;"></div>
          </div>
        </div>
        <!--        <div>-->
        <!--          <el-pagination background :current-page="page" layout="prev, pager, next" @current-change="tabClick" :page-size="1" :total="parseInt(select_row.pvid)" />-->
        <!--        </div>-->
      </div>
    </template>
  </el-dialog>

  <!--  查看医嘱-->
  <el-dialog v-model="showAdvice" title="医嘱信息" width="80%" draggable>
    <div v-loading="load">
      <el-table :data="adviceList" style="width: 100%;height: 400px">
        <el-table-column type="index" width="50" />
        <el-table-column prop="order_content" label="医嘱内容" :filters="[
          { text: '检验', value: 'C' },
          { text: '检查', value: 'D' },
          { text: '治疗', value: 'E' },
          { text: '手术', value: 'F' },
          { text: '麻醉', value: 'G' },
          { text: '护理', value: 'H' },
          { text: '膳食', value: 'I' },
          { text: '输血', value: 'K' },
          { text: '输氧', value: 'L' },
          { text: '材料', value: 'M' },
          { text: '其他', value: 'Z' },
          { text: '用药', value: '5' },
        ]" :filter-method="filterCitemType" filter-placement="bottom-end" />
        <el-table-column prop="order_freq_name" label="用法" />
        <el-table-column prop="order_once_qunt" label="单次用量">
          <template #default="scope">
            <div v-if="scope.row.order_once_qunt">{{ scope.row.order_once_qunt }} {{ scope.row.order_once_qunt_unit }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="order_total_qunt" label="总给予量">
          <template #default="scope">
            <div v-if="scope.row.order_total_qunt">{{ scope.row.order_total_qunt }} {{ scope.row.order_once_qunt_unit }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="apply_time" label="开嘱时间" />
        <el-table-column prop="order_exe_time_start" label="开始执行时间" />
        <el-table-column prop="order_exe_time_end" label="执行终止时间" />
        <el-table-column prop="orderstp_time" label="停嘱时间" />
        <el-table-column prop="order_expidate_type" label="医嘱期效" :filters="[
          { text: '长嘱', value: 0 },
          { text: '临嘱', value: 1 },
        ]" :filter-method="filterExpidate" filter-placement="bottom-end">
          <template #default="scope">
            <span v-if="scope.row.order_expidate_type == 1">临嘱</span>
            <span v-else>长嘱</span>
          </template>
        </el-table-column>

        <el-table-column prop="order_status" label="医嘱状态">
          <template #default="scope">
            <span v-if="scope.row.order_status == -1">未生效的暂存医嘱</span>
            <span v-if="scope.row.order_status == 1">新开</span>
            <span v-if="scope.row.order_status == 2">校对疑问</span>
            <span v-if="scope.row.order_status == 3">已校对</span>
            <span v-if="scope.row.order_status == 4">已作废</span>
            <span v-if="scope.row.order_status == 5">已重整</span>
            <span v-if="scope.row.order_status == 6">已暂停</span>
            <span v-if="scope.row.order_status == 7">已启用</span>
            <span v-if="scope.row.order_status == 8">已停止</span>
            <span v-if="scope.row.order_status == 9">已确认停止</span>
          </template>
        </el-table-column>
      </el-table>

      <div>
        <el-pagination background :current-page="advicePage" layout="prev, pager, next" @current-change="tabAdviceClick"
          :page-size="1" :total="parseInt(select_row.pvid)" />
      </div>
    </div>
  </el-dialog>

  <el-dialog v-model="diseaseShow" title="当前病区病种统计" width="80%" draggable>
    <div style="overflow-y: scroll">
      <h3>总{{ user_list.length }}人数 <el-button type="primary" @click="diseaseExport">导出</el-button></h3>
      <el-table :data="disease_list" style="width: 100%;height: 400px">
        <el-table-column type="index" width="50" />
        <el-table-column prop="病种名称" label="病种名称" />
        <el-table-column prop="人数" label="人数" />
        <el-table-column prop="患者名称" label="患者名称" />
      </el-table>
    </div>
  </el-dialog>

  <!-- 标签查看对话框 -->
  <el-dialog :closeOnClickModal="false" v-model="tagDialogVisible" title="患者标签信息" width="90%" draggable>
    <div class="tag-section-box">
      <div v-loading="load" style="width: 50%;">
        <el-scrollbar height="400px">
          <!-- 文档标签列表 -->
          <div v-if="documentTagList.length > 0">
            <h3 class="tag-section-title">病历标签</h3>
            <div v-for="(docItem, docIndex) in documentTagList" :key="docIndex" class="document-tag-group">
              <div class="document-info">
                <span class="document-type">{{ docItem.document_type }}</span>
                <span class="document-date">{{ docItem.date }}</span>
              </div>
              <div class="tag-container">
                <el-tag v-for="(tag, tagIndex) in docItem.keywordsList" :key="tagIndex" class="tag-item document-tag"
                  effect="plain">
                  {{ tag }}
                </el-tag>
              </div>
            </div>
          </div>
       </el-scrollbar>
  
        <!-- 待处理标签 -->
        <el-scrollbar height="200px">

          <div v-if="pendingTagList.length > 0" class="pending-tag-section">
            <h3 class="tag-section-title">待处理标签</h3>
            <div class="tag-container">
              <el-tag v-for="(tag, index) in pendingTagList" :key="index" class="tag-item pending-tag" type="warning"
                effect="plain">
                {{ tag }}
              </el-tag>
            </div>
          </div>
        </el-scrollbar>
  
        <!-- 无标签数据提示 -->
        <div v-if="documentTagList.length === 0 && pendingTagList.length === 0 && !load" class="no-tags">
          暂无标签数据
        </div>
      </div>
      <div v-loading="newLoad" style="width: 50%;">
        <el-scrollbar height="400px">
          <!-- 文档标签列表 -->
          <div v-if="newDocumentTagList.length > 0">
            <h3 class="tag-section-title">新病历标签</h3>
            <div v-for="(docItem, docIndex) in newDocumentTagList" :key="docIndex" class="document-tag-group">
              <div class="document-info">
                <span class="document-type">{{ docItem.type }}</span>
                <span class="document-date">{{ docItem.date }}</span>
              </div>
              <h5>标签</h5>
              <div class="tag-container">
                <el-tag v-for="(tag,key, tagIndex) in docItem.tags" :key="tagIndex" class="tag-item document-tag"
                  effect="plain">
                  {{ key + tag }}
                </el-tag>
              </div>
              <h5>无效标签</h5>
              <div class="tag-container">
                <el-tag v-for="(item, tagIndex) in docItem.invalid_list" :key="tagIndex" class="tag-item document-tag"
                  effect="plain">
                  {{ item.tag }}
                </el-tag>
              </div>
              <h5>未找到标签</h5>
              <div class="tag-container">
                <el-tag v-for="(item, tagIndex) in docItem.not_found_list" :key="tagIndex" class="tag-item document-tag"
                  effect="plain">
                  {{ item.tag }}
                </el-tag>
              </div>
            </div>
            <div v-for="(docItem, docIndex) in templateList" :key="docIndex" class="document-tag-group">
              <div v-if="docItem.name === '模板内容'">
                <h5>{{ docItem.name }}</h5>
                <div class="tag-container">
                  <p>{{docItem.value}}</p>
                </div>
              </div>
              <div v-else-if="docItem.name === '模板分类'">
                <h5>{{ docItem.name }}</h5>
                <div class="tag-container">
                  <div v-for="(item,key, tagIndex) in docItem.value" :key="tagIndex" class="tag-container" style="width: 100%;">
                    <div style="width: 100%;">
                      <h6>{{key}}</h6>
                    </div>
                    <el-tag v-for="(item1, tagIndex) in item" :key="tagIndex" class="tag-item document-tag"
                      effect="plain">
                      {{ item1.tag ? item1.tag : item1 }}
                    </el-tag>
                  </div>
                </div>
              </div>
              <div v-else-if="docItem.name === '结果'">
                <h5>{{ docItem.name }}</h5>
                <div class="tag-container">
                  <p>{{docItem.value}}</p>
                </div>
              </div>
              <div v-else>
                <h5>{{ docItem.name }}</h5>
                <div class="tag-container">
                  <el-tag v-for="(item, tagIndex) in docItem.value" :key="tagIndex" class="tag-item document-tag"
                    effect="plain">
                    {{ item }}
                  </el-tag>
                </div>
              </div>
              
            </div>

          </div>
       </el-scrollbar>
  
        <!-- 待处理标签 -->
         <el-scrollbar height="200px">  
           <div v-if="newPendingTagList.length > 0" class="pending-tag-section">
             <h3 class="tag-section-title">待处理标签</h3>
             <div class="tag-container">
               <el-tag v-for="(tag, index) in newPendingTagList" :key="index" class="tag-item pending-tag" type="warning"
                 effect="plain">
                 {{ tag }}
               </el-tag>
             </div>
           </div>
         </el-scrollbar>
  
        <!-- 无标签数据提示 -->
        <div v-if="newDocumentTagList.length === 0 && !newLoad" class="no-tags">
          暂无标签数据
        </div>
      </div>
    </div>
  </el-dialog>

  <el-dialog v-model="test_show_see" title="检查信息" width="80%" draggable>
    <div style="width: 100%;height:500px;" v-loading="load">
      <el-tabs v-model="activeName" class="demo-tabs">
        <el-tab-pane label="检验" name="first">
          <el-table :data="doctorTestList.jy" style="width: 100%;height: 400px">
            <el-table-column prop="order_content" label="检验内容" width="100" />
            <el-table-column prop="order_content" label="检验内容" width="100" />
            <el-table-column prop="apply_time" label="申请时间" width="200" sortable />
            <el-table-column label="检验结果">
              <template #default="scope">
                <div v-for="item in scope.row.result">
                  <span class="title_item">结果时间：{{ item.chk_time }}</span>
                  <span class="title_item">名称：{{ item.loitem_cname }}</span>
                  <span class="title_item">指标参考值：{{ item.loitem_rv }}</span>
                  <span class="title_item">指标结果值：{{ item.order_rpt_result }}</span>

                  <!--                    <div v-if="item.oaflag!=0">-->
                  <!--                      <span class="title_item">名称：{{item.loitem_cname}}</span>-->
                  <!--                      <span class="title_item">指标参考值：{{item.loitem_rv}}</span>-->
                  <!--                      <span class="title_item">指标结果值：{{item.order_rpt_result}}</span>-->
                  <!--                      <span class="title_item">-->
                  <!--                      结果异常标志:-->
                  <!--                      <span v-if="item.oaflag==1">正常 </span>-->
                  <!--                      <span v-if="item.oaflag==2">偏低</span>-->
                  <!--                      <span v-if="item.oaflag==3">偏高</span>-->
                  <!--                      <span v-if="item.oaflag==4">阳性（异常）</span>-->
                  <!--                      <span v-if="item.oaflag==5">警戒下限</span>-->
                  <!--                      <span v-if="item.oaflag==6">警戒上限</span>-->
                  <!--                      <span v-if="item.oaflag==7">复查下限</span>-->
                  <!--                      <span v-if="item.oaflag==8">复查上限</span>-->
                  <!--                      <span v-if="item.oaflag==9">线性异常</span>-->
                  <!--                    </span>-->
                  <!--                    </div>-->
                </div>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="检查" name="second">
          <el-table :data="doctorTestList.jc" style="width: 100%;height: 400px">
            <el-table-column type="index" width="50" />
            <el-table-column prop="order_content" label="检查内容" />
            <el-table-column prop="apply_time" width="180" label="申请时间" sortable />
            <el-table-column label="检验结果">
              <template #default="scope">
                <div v-for="item in scope.row.result">
                  <span class="title_item">报告时间：{{ item.report_time }}</span>
                  <span class="title_item">检查结果：{{ item.order_rpt_result }}</span>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>
    </div>
  </el-dialog>


</template>

<script setup>
import * as XLSX from 'xlsx';
import { ref, reactive } from 'vue'
import { confirm, message } from '@/utils/message'
import { post, get } from '@/utils/request.js'
import c from "@/utils/config";
import { baseUrl } from '../utils/baseUrl';
const api_url = c.api_url;
const selectedDepartment = ref('')
const taskFilter = ref('all')
const load = ref(false)
const newLoad = ref(false)
const patientLoad = ref(false) //左侧患者列表加载
const select_list = ref([])
const user_list = ref([])
const dialogVisible = ref(false)
const select_row = ref({})
const page = ref(1)
const listings = ref([])
const his_list = ref({})
const user_name = ref("")
const test_show_see = ref(false)

const showAdvice = ref(false)
const adviceList = ref([]) //  医嘱列表
const advicePage = ref(1)
const taskList = ref([]) // 任务列表
const selectedPvid = ref("")
const disease_list = ref({})
const diseaseShow = ref(false)
const select_name = ref("")
const activeName = ref("first")
const doctorTestList = ref({
  jc: [],
  jy: []
})
// 标签对话框相关变量
const tagDialogVisible = ref(false)
const documentTagList = ref([])
const newDocumentTagList = ref([])
const templateList = ref([])
const pendingTagList = ref([])
const newPendingTagList = ref([])
// 查看标签
const getTag = () => {
  tagDialogVisible.value = true;
  load.value = true;
  newLoad.value = true;
  documentTagList.value = [];
  pendingTagList.value = [];

  post(`${api_url}/tags/getPatientData`, JSON.stringify({
    pid: select_row.value.pid,
    pvid: select_row.value.pvid,
    pat_name: select_row.value.pat_name
  })).then(data => {
    // 处理 pending 数组
    pendingTagList.value = data.pending || [];

    // 处理 data 数组中的 keywords
    if (data.data && Array.isArray(data.data)) {
      documentTagList.value = data.data.map(item => {
        // 将 JSON 字符串解析为数组
        let keywordsList = [];
        try {
          if (item.keywords) {
            keywordsList = JSON.parse(item.keywords);
          }
        } catch (e) {
          console.error('解析 keywords 出错:', e);
        }

        return {
          ...item,
          keywordsList
        };
      });
    }

    load.value = false;
  }).catch(error => {
    load.value = false;
    console.error('获取标签数据出错:', error);
  });

  // let data = {"2025-05-27 12:30:00&首次病程记录":{"tags":{"患者神志清楚":["1.1"],"注意力集中":["5.2"],"语量适中":["32.39"],"衣着整洁":["33.1"],"引出言语性幻听":["41.6"],"未引出感知觉障碍、妄想等精神病性症状":["41.3","42.8"],"定向力完整":["2.1"],"自知力无":["10.1"],"社会功能部分受损":["12.1"]},"invalid_list":[{"tag":"接触被动","source":"inv","cache_key":"接触"},{"tag":"对答切题","source":"inv","cache_key":"切题"},{"tag":"年貌相符","source":"dep","cache_key":"年貌相符"},{"tag":"智能记忆好","source":"dep","cache_key":"智能记忆好"}],"not_found_list":[]},"2025-05-27 13:00:00&上级医师查房记录(主任)":{"tags":{"生命体征正常":["13.2"],"未见药物副反应":["21.5"],"精神症状部分残留":["42.1"],"引出抑郁情绪":["9.13"],"悲观绝望":["8.14"],"还不如死了算了":["43.32"],"自知力无":["10.1"],"进食尚可":["14.1"]},"invalid_list":[{"tag":"2025年05月27日13时00分\t段敏主任医师查房记录\n    今日段敏主任医师查房后指出：患者意识清晰","source":"inv","cache_key":"05"},{"tag":"接触被动","source":"inv","cache_key":"接触"},{"tag":"对答切题","source":"inv","cache_key":"切题"},{"tag":"持续约1+周","source":"inv","cache_key":"+"},{"tag":"自诉情绪低落、高兴不起来","source":"inv","cache_key":"自诉"},{"tag":"觉得活着没有任何意义","source":"inv","cache_key":"觉得"},{"tag":"既往有吞药、割手自伤、自杀行为","source":"inv","cache_key":"自杀"},{"tag":"查体见右手手腕处疤痕","source":"inv","cache_key":"右手"},{"tag":"患者自诉是入院前自己用刀割伤的","source":"inv","cache_key":"入院"},{"tag":"入院时有隐瞒","source":"inv","cache_key":"入院"},{"tag":"大小便正常","source":"inv","cache_key":"小便"},{"tag":"服药需督促","source":"dep","cache_key":"服药需督促"},{"tag":"据患者目前情况","source":"dep","cache_key":"据患者目前情况"},{"tag":"目前考虑：抑郁状态","source":"inv","cache_key":"："},{"tag":"加用盐酸舍曲林片 50mg 口服 Am抗抑郁药物治疗","source":"inv","cache_key":"g"},{"tag":"定期复查相关检查","source":"dep","cache_key":"定期复查相关检查"},{"tag":"注意密切观察患者病情变化","source":"dep","cache_key":"注意密切观察患者病情变化"},{"tag":"医师签名：罗叶\t手签：\t上级医师签名：_段敏______","source":"inv","cache_key":"上级医师"}],"not_found_list":[{"tag":"未描述","source":"","cache_key":""},{"tag":"治疗上据患者目前病情","source":"","cache_key":""},{"tag":"余维持当前治疗方案","source":"","cache_key":""}]},"2025-05-28 12:06:00&上级医师查房记录":{"tags":{"患者神志清楚":["1.1"],"注意力集中":["5.2"],"语量适中":["32.39"],"引出言语性幻听":["41.6"],"定向力完整":["2.1"],"自知力无":["10.1"],"社会功能部分受损":["12.1"]},"invalid_list":[{"tag":"接触被动","source":"inv","cache_key":"接触"},{"tag":"对答切题","source":"inv","cache_key":"切题"},{"tag":"凭空听到有人在和自己说话","source":"inv","cache_key":"说"},{"tag":"智能记忆好","source":"dep","cache_key":"智能记忆好"}],"not_found_list":[]},"2025-05-29 12:08:00&上级医师查房记录":{"tags":{"患者神志清楚":["1.1"],"注意力集中":["5.2"],"语量适中":["32.39"],"引出言语性幻听":["41.6"],"引出被害妄想":["41.5"],"定向力完整":["2.1"],"自知力无":["10.1"],"社会功能部分受损":["12.1"]},"invalid_list":[{"tag":"接触被动","source":"inv","cache_key":"接触"},{"tag":"对答切题","source":"inv","cache_key":"切题"},{"tag":"凭空听到有人在和自己说话","source":"inv","cache_key":"说"},{"tag":"坚信有人要整自己","source":"inv","cache_key":"坚信"},{"tag":"操纵自己做事情","source":"dep","cache_key":"操纵自己做事情"},{"tag":"让自己吃东西长胖","source":"dep","cache_key":"让自己吃东西长胖"},{"tag":"智能记忆好","source":"dep","cache_key":"智能记忆好"}],"not_found_list":[]},"2025-05-29 14:21:00&日常病程记录":{"tags":{},"invalid_list":[{"tag":"2025年05月29日14时21分\n    检验检查结果：2025-05-28：血常规(五分类)：血小板压积 0.29 %；肝功15项：间接胆红素 10.53 umol/L,*碱性磷酸酶 101.40 U/L,*门冬氨酸氨基转移酶 55.50 U/L,AST/ALT 0.7 ,*丙氨酸氨基转移酶 75.30 U/L；肾功：*尿酸 431.20 umol/L；血脂4项：*高密度脂蛋白胆固醇 0.93 mmol/L","source":"inv","cache_key":"-"},{"tag":"电解质(3项)、葡萄糖、钙、心肌酶谱四项等未见明显异常","source":"inv","cache_key":"电解质"},{"tag":"心电图：窦性心动过缓（59次/分）","source":"inv","cache_key":"/"},{"tag":"患者肝功异常","source":"inv","cache_key":"肝功"},{"tag":"目前考虑：肝功不全","source":"inv","cache_key":"肝功"},{"tag":"治疗上予以联苯双酯滴丸 7.5mg 口服 tid改善肝功","source":"inv","cache_key":"g"},{"tag":"密切观察患者病情变化","source":"dep","cache_key":"密切观察患者病情变化"},{"tag":"医师签名：罗叶","source":"inv","cache_key":"签名"},{"tag":"手签：","source":"inv","cache_key":"："}],"not_found_list":[]},"2025-06-01 14:57:00&日常病程记录":{"tags":{"患者意识清楚":["1.1"],"查及言语性幻听":["41.6"],"时有自语自笑":["32.35","15.3"],"情感反应欠协调":["8.8"],"生活督促下自行料理":["24.3"],"进食及服药配合":["14.1","11.1"],"夜间睡眠改善":["16.12"],"二便通畅":["30.1"]},"invalid_list":[{"tag":"今日查房","source":"dep","cache_key":"今日查房"},{"tag":"接触被动","source":"inv","cache_key":"接触"},{"tag":"对答简单切题","source":"inv","cache_key":"切题"},{"tag":"否认妄想体验","source":"dep","cache_key":"否认妄想体验"},{"tag":"治疗上暂无调整","source":"dep","cache_key":"治疗上暂无调整"},{"tag":"密切观察患者病情变化","source":"dep","cache_key":"密切观察患者病情变化"},{"tag":"注意复查相关辅助检查","source":"dep","cache_key":"注意复查相关辅助检查"},{"tag":"医师签名：罗叶","source":"inv","cache_key":"签名"},{"tag":"手签：","source":"inv","cache_key":"："}],"not_found_list":[]},"2025-06-03 12:00:00&上级医师查房记录(主任)":{"tags":{"生命体征正常":["13.2"],"未见药物副反应":["21.5"],"精神症状部分残留":["42.1"],"引出抑郁情绪":["9.13"],"自己就是想去死":["43.32"],"自知力无":["10.1"],"进食尚可":["14.1"]},"invalid_list":[{"tag":"2025年06月03日12时00分\t段敏主任医师查房记录\n    今日段敏主任医师查房后指出：患者意识清晰","source":"inv","cache_key":"主任"},{"tag":"接触被动","source":"inv","cache_key":"接触"},{"tag":"对答切题","source":"inv","cache_key":"切题"},{"tag":"自诉情绪低落、高兴不起来","source":"inv","cache_key":"自诉"},{"tag":"询问医生要怎么办","source":"inv","cache_key":"医生"},{"tag":"既往有吞药、割手自伤、自杀行为","source":"inv","cache_key":"自杀"},{"tag":"目前患者仍存在自伤、自杀观念","source":"inv","cache_key":"自杀"},{"tag":"大小便正常","source":"inv","cache_key":"小便"},{"tag":"服药需督促","source":"dep","cache_key":"服药需督促"},{"tag":"据患者目前情况","source":"dep","cache_key":"据患者目前情况"},{"tag":"治疗上继续加量盐酸舍曲林片 100mg 口服 Am抗抑郁药物治疗","source":"inv","cache_key":"g"},{"tag":"定期复查相关检查","source":"dep","cache_key":"定期复查相关检查"},{"tag":"注意密切观察患者病情变化","source":"dep","cache_key":"注意密切观察患者病情变化"},{"tag":"医师签名：罗叶\t手签：\t上级医师签名：_段敏______","source":"inv","cache_key":"上级医师"}],"not_found_list":[{"tag":"余维持当前治疗方案","source":"","cache_key":""}]},"2025-06-06 11:14:00&日常病程记录":{"tags":{"患者意识清楚":["1.1"],"未诉躯体不适":["18.3"],"言语性幻听及被害妄想等精神病性症状较前明显好转":["41.27","41.23"],"仍存在消极言语":["43.32"],"情感反应较前协调":["8.21"],"生活懒散":["25.1"],"个人生活督促自理":["24.3"],"进食及服药配合":["14.1","11.1"],"夜间睡眠改善":["16.12"],"二便通畅":["30.1"]},"invalid_list":[{"tag":"今日查房","source":"dep","cache_key":"今日查房"},{"tag":"接触被动","source":"inv","cache_key":"接触"},{"tag":"对答基本切题","source":"inv","cache_key":"切题"},{"tag":"自笑情况减轻","source":"dep","cache_key":"自笑情况减轻"},{"tag":"继续当前治疗方案","source":"dep","cache_key":"继续当前治疗方案"},{"tag":"注意密切观察患者病情变化","source":"dep","cache_key":"注意密切观察患者病情变化"},{"tag":"医师签名：罗叶","source":"inv","cache_key":"签名"},{"tag":"手签：","source":"inv","cache_key":"："}],"not_found_list":[]},"2025-06-09 10:02:00&上级医师查房记录(主任)":{"tags":{"生命体征正常":["13.2"],"未见药物副反应":["21.5"],"时自语自笑":["32.35","15.3"],"否认幻觉、妄想等精神病性症状":["41.3"],"情感反应欠协调":["8.8"],"仍存在消极言语":["43.32"],"自知力无":["10.1"],"能配合检查治疗":["26.5"],"进食睡眠尚可":["14.1","16.1"]},"invalid_list":[{"tag":"2025年06月09日10时02分\t张艳主治医师查房记录\n    今日张艳主治医师查房后指出：患者意识清晰","source":"inv","cache_key":"09"},{"tag":"接触被动","source":"inv","cache_key":"接触"},{"tag":"对答切题","source":"inv","cache_key":"切题"},{"tag":"大小便正常","source":"inv","cache_key":"小便"},{"tag":"服药需督促","source":"dep","cache_key":"服药需督促"},{"tag":"维持当前治疗方案","source":"dep","cache_key":"维持当前治疗方案"},{"tag":"定期复查相关检查","source":"dep","cache_key":"定期复查相关检查"},{"tag":"注意密切观察患者病情变化","source":"dep","cache_key":"注意密切观察患者病情变化"},{"tag":"医师签名：罗叶\t手签：\t上级医师签名：_张艳_____","source":"inv","cache_key":"上级医师"}],"not_found_list":[]},"2025-06-11 09:47:00&日常病程记录":{"tags":{"患者目前精神症状部分残留":["42.1"]},"invalid_list":[{"tag":"患者自诉咽喉不适","source":"inv","cache_key":"咽喉"},{"tag":"咳嗽、咳痰","source":"inv","cache_key":"咳嗽"},{"tag":"目前考虑：咳嗽","source":"inv","cache_key":"咳嗽"},{"tag":"治疗上予以复方甘草口服溶液 10ml 口服 tid对症处理","source":"inv","cache_key":"id"},{"tag":"观察病情变化","source":"dep","cache_key":"观察病情变化"},{"tag":"完善利培酮血药浓度检查","source":"inv","cache_key":"利培酮"},{"tag":"明确情况","source":"dep","cache_key":"明确情况"},{"tag":"医师签名：罗叶","source":"inv","cache_key":"签名"},{"tag":"手签：","source":"inv","cache_key":"："}],"not_found_list":[]}}
  // const {newTagDatas, pendingData} = getNewTagDatas(data);
  // newDocumentTagList.value = newTagDatas;
  // newPendingTagList.value = pendingData;
  // newLoad.value = false;

  let url = `${baseUrl.url7}/?pid=${select_row.value.pid}&pvid=${select_row.value.pvid}`
  fetch(url).then((response) => {
    if (!response.ok) {
      throw new Error('Network response was not ok ' + response.statusText);
    }
    return response.json(); // 或者 response.text()，response.blob()，取决于你的响应体内容
  })
  .then((data) => {
    const {newTagDatas, pendingData} = getNewTagDatas(data);
    newDocumentTagList.value = newTagDatas;
    newPendingTagList.value = pendingData;
    newLoad.value = false;
  })
  .catch((error) => {
    console.error('获取新标签数据出错:', error);
    newLoad.value = false;
  });

  // let data1 = {"template_document":"2025年06月09日10时02分\t张艳主治医师查房记录\n    今日张艳主治医师查房后指出：患者意识清晰，生命体征正常，未见药物副反应。接触被动，对答切题，时自语自笑，否认幻觉、妄想等精神病性症状，情感反应欠协调，仍存在消极言语，自知力无。能配合检查治疗，进食睡眠尚可，大小便正常，服药需督促。维持当前治疗方案，定期复查相关检查，注意密切观察患者病情变化。\t医师签名：罗叶\t手签：\t上级医师签名：_张艳_____","template_category":{"tags":{"生命体征正常":["13.2"],"未见药物副反应":["21.5"],"时自语自笑":["32.35","15.3"],"否认幻觉、妄想等精神病性症状":["41.3"],"情感反应欠协调":["8.8"],"仍存在消极言语":["43.32"],"自知力无":["10.1"],"能配合检查治疗":["26.5"],"进食睡眠尚可":["14.1","16.1"]},"invalid_list":[{"tag":"2025年06月09日10时02分\t张艳主治医师查房记录\n    今日张艳主治医师查房后指出：患者意识清晰","source":"inv","cache_key":"09"},{"tag":"接触被动","source":"inv","cache_key":"接触"},{"tag":"对答切题","source":"inv","cache_key":"切题"},{"tag":"大小便正常","source":"inv","cache_key":"小便"},{"tag":"服药需督促","source":"dep","cache_key":"服药需督促"},{"tag":"维持当前治疗方案","source":"dep","cache_key":"维持当前治疗方案"},{"tag":"定期复查相关检查","source":"dep","cache_key":"定期复查相关检查"},{"tag":"注意密切观察患者病情变化","source":"dep","cache_key":"注意密切观察患者病情变化"},{"tag":"医师签名：罗叶\t手签：\t上级医师签名：_张艳_____","source":"inv","cache_key":"上级医师"}],"not_found_list":[]},"template":[["13.2"],["21.5"],["32.35","15.3"],["41.3"],["8.8"],["43.32"],["10.1"],["26.5"],["14.1","16.1"]],"merge_result":["43.32","24.3","42.1","41.3","13.2","25.1","21.5","8.8","16.1","5.2","33.1","9.13","15.3","26.5","30.1","10.1","14.1","12.1","18.3","11.1","32.35","2.1","1.1"],"result":"生命体征正常，未见明显药物不反应，时自言自语、自笑，凭空闻声，情感反应不协调，未找到，对疾病无自知力，未找到，饮食睡眠尚可。"}
  // let newData = [
  //     {name: '模板内容', value: data1.template_document},
  //     {name: '模板标签分类', value: data1.template},
  //     {name: '模板分类', value: {
  //       '模板标签' : data1.template_category.tags,
  //       '无效标签' : data1.template_category.invalid_list,
  //       '未找到标签' : data1.template_category.tags,
  //     }},
  //     {name: '分类', value: data1.merge_result},
  //     {name: '结果', value: data1.result.split('，')}
  //   ]
  // console.log(newData);
  // templateList.value = newData
  
  let url1 = `${baseUrl.url7}/final?pid=${select_row.value.pid}&pvid=${select_row.value.pvid}`
  fetch(url1).then((response) => {
    if (!response.ok) {
      throw new Error('Network response was not ok ' + response.statusText);
    }
    return response.json(); // 或者 response.text()，response.blob()，取决于你的响应体内容
  })
  .then((data) => {
    let newData = [
      {name: '模板内容', value: data.template_document},
      {name: '模板标签分类', value: data.template},
      {name: '模板分类', value: {
        '模板标签' : data.template_category.document.split('，'),
        '无效标签' : data.template_category.invalid_list,
        '未找到标签' : data.template_category.not_found_list,
      }},
      {name: '分类', value: data.merge_result},
      {name: '结果', value: data.result}
    ]
    templateList.value = newData
    newLoad.value = false;
  })
  .catch((error) => {
    console.error('获取新标签数据出错:', error);
    newLoad.value = false;
  });


}

const getNewTagDatas = (data) => {
  
  let newTagDatas = []
  let pendingData = []
  for(let key in data){
    let keyArr = key.split("&")
    let date = keyArr[0]
    let type = keyArr[1]
    let tags = data[key].tags
    let invalid_list = data[key].invalid_list
    invalid_list = invalid_list.filter((item) => {
      return !item.tag.includes('手签') && !item.tag.includes('医师签名')
    })

    let not_found_list = data[key].not_found_list
    not_found_list.forEach((item) => {
      pendingData.push(item.tag)
    })
    newTagDatas.push({
      date,
      type,
      tags,
      invalid_list,
      not_found_list
    })
  }
  console.log(newTagDatas);
  return {newTagDatas, pendingData}
}

// 检查检验
const getDoctorTest = () => {
  test_show_see.value = true;
  load.value = true
  doctorTestList.value = {
    jc: [],
    jy: []
  }
  post(`${api_url}/patient/getYzRs`, JSON.stringify({ pid: select_row.value.pid, pvid: selectedPvid.value })).then(res => {
    let jc = []
    let jy = []
    load.value = false;
    res.jc.forEach((item) => {
      let index = jc.findIndex((row) => row.order_conten === item.order_content)
      // if(index===-1){
      jc.push(item)
      // }
    })

    res.jy.forEach((item) => {
      let index = jy.findIndex((row) => row.order_content === item.order_content)

      // if(item.order_content=="电解质(3项)(静脉血)"){
      //   console.log(item)
      // }
      jy.push(item)
      // if(index===-1){
      //   jy.push(item)
      // }
    })



    doctorTestList.value = {
      jc,
      jy
    }
  }).catch(error => {
    load.value = false;
    console.error('发生错误:', error);
  });

}

const diseaseExport = () => {
  // 创建工作簿
  const wb = XLSX.utils.book_new();
  // 将数据转换为工作表
  const ws = XLSX.utils.json_to_sheet(disease_list.value);
  // 将工作表添加到工作簿
  XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');
  // 导出 Excel 文件
  XLSX.writeFile(wb, select_name.value + '.xlsx');
}
//病种统计
const diseaseTypes = () => {
  let disease = {}
  user_list.value.forEach((row) => {
    if (!row.disease) row.disease = "无病种"
    if (disease[row.disease]) {
      disease[row.disease].push(row)
    } else {
      disease[row.disease] = [row]
    }
  })
  // disease_list.value = disease
  diseaseShow.value = true;

  let list = []
  for (let key in disease) {
    list.push({
      "病种名称": key,
      "人数": disease[key].length,
      "患者名称": disease[key].map((row) => row.pat_name).join(",")
    })
  }
  disease_list.value = list

}
// 删除
const deleteCas = (row) => {
  confirm("是否要删除？").then((c) => {
    if (c) {
      load.value = true
      post(`${api_url}/Patient/deleteCase`, JSON.stringify({ task_id: row.id })).then(res => {
        message("删除成功！")
        load.value = false
        getTag()
      }).catch(error => {
        load.value = false
        console.error('发生错误:', error);
      });
    }
  })
}

//搜索患者
const searchUser = (value) => {
  user_list.value = user_list.value.map((row) => {
    if (!user_name.value || row.pat_name.toLowerCase().includes(user_name.value.toLowerCase())) {
      row.show = true;
    } else {
      row.show = false;
    }
    return row;
  })

  console.log(value)
}
// 分页获取医嘱
const tabAdviceClick = (index) => {
  advicePage.value = index
  getDoctorAdvice(select_row.value.pid, index)
}
// 筛选状态
const filterExpidate = (value, row) => {
  return row.order_expidate_type == value
}
// 筛选
const filterCitemType = (value, row) => {
  return row.citem_type === value
}
// 获取医嘱信息
const getDoctorAdvice = (pid, pvid) => {
  adviceList.value = []
  load.value = true
  post(`${api_url}/patient/getYz`, JSON.stringify({ pid, pvid })).then(res => {
    adviceList.value = res.map((item) => {
      let {
        order_content,
        order_drask,
        order_once_qunt,
        order_total_qunt,
        order_once_qunt_unit,
        order_exe_time_start,
        order_exe_time_end,
        order_freq_name,
        conterm_time,
        order_expidate_type,
        citem_type,
        order_status,
        apply_time,
        orderstp_time,
      } = item
      return {
        order_freq_name,
        conterm_time,
        orderstp_time,
        order_content,
        order_drask,
        order_once_qunt,
        order_total_qunt,
        order_once_qunt_unit,
        order_exe_time_start,
        order_exe_time_end,
        order_expidate_type,
        citem_type,
        order_status,
        apply_time
      }
    })
    load.value = false
  }).catch(error => {
    load.value = false
    console.error('发生错误:', error);
  });
}

// 显示医嘱
const viewAdviceHistory = () => {
  showAdvice.value = true;
  getDoctorAdvice(select_row.value.pid, select_row.value.pvid)
}

// 分页获取病例
const tabClick = (index) => {
  page.value = index;
  getPatientCase(select_row.value.pid, index)
}
// 获取病例
const getPatientCase = (pid, pvid) => {
  load.value = true
  let url = `${api_url}/AiCase/getPatientCase`
  post(url, JSON.stringify({ pid, pvid })).then(res => {
    listings.value = res.data;
    load.value = false
  }).catch(error => {
    load.value = false
    console.error('发生错误:', error);
  });
}

// 查看病例
const viewHistory = () => {
  dialogVisible.value = true;
  listings.value = his_list.value

  console.log(his_list.value)

  // getPatientCase(select_row.value.pid,select_row.value.pvid)
}

function formatDocument(doc) {
    if (!doc) return '';
    let obj = doc;
    if (typeof doc === 'string') {
        try {
            obj = JSON.parse(doc);
        } catch {
            return doc; // 不是JSON就原样返回
        }
    }
    // 优化缩进和换行，key加粗，内容自动换行，缩进更明显，移除空格，序号换行
    function pretty(obj, indent = 0) {
        if (typeof obj !== 'object' || obj === null) return String(obj);
        if (Array.isArray(obj)) {
            return obj.map(item => pretty(item, indent + 1)).join('<br>');
        }
        return Object.entries(obj).map(([k, v]) => {
            if (typeof v === 'object' && v !== null) {
                return `<div style="padding-left:${indent * 24}px; font-weight:bold;">${k}：</div>` + pretty(v, indent + 1);
            } else {
                // 移除所有空格
                let content = String(v).replace(/\s+/g, '');
                // 将（1）（2）等序号前加换行
                content = content.replace(/(\（\d+\）)/g, '<br>$1');
                // 将以符号或非汉字/字母结尾后跟1. 1、①、一、等序号前加换行（包括行首）
                content = content.replace(
                    /([^\u4e00-\u9fa5a-zA-Z0-9]|^)((\d+[\.、])|[①②③④⑤⑥⑦⑧⑨⑩]|[一二三四五六七八九十][\.、])/g,
                    '$1<br>$2'
                );
                // 将类似2025-06-19的日期前加换行（包括行首和符号后）
                content = content.replace(
                    /([^\d]|^)(\d{4}-\d{2}-\d{2})/g,
                    '$1<br>$2'
                );
                // 保留原有换行符，内容缩进更明显
                content = content
                    .replace(/\\n/g, '<br>' + '&nbsp;'.repeat((indent + 1) * 4))
                    .replace(/\n/g, '<br>' + '&nbsp;'.repeat((indent + 1) * 4));
                return `<div style="padding-left:${indent * 24}px;"><b>${k}：</b>${content}</div>`;
            }
        }).join('');
    }
    return pretty(obj);
}

// 选择患者
const selectUser = (row) => {
  select_row.value = row
  selectedPvid.value = row.pvid;
  page.value = parseInt(row.pvid)
  advicePage.value = parseInt(row.pvid)
  getTask(row.pid, row.pvid)
}
// 获取任务列表
const getTask = (pid, pvid) => {
  load.value = true
  let url = `${api_url}/Patient/getCaseList`
  console.log(444, { pid: select_row?.value?.pid, pvid: selectedPvid?.value });

  post(url, JSON.stringify({ pid: select_row?.value?.pid, pvid: selectedPvid?.value })).then(res => {
    taskList.value = res.data.ai_list
    if (res.data.his_list.length > 0) {
      his_list.value = res.data.his_list
    } else {
      his_list.value = {
        WBMC: "",
        document_time: ""
      }
    }
    load.value = false
  }).catch(error => {
    load.value = false
    console.error('发生错误:', error);
  });
}

// 获取患者
const getUser = () => {
  user_name.value = ""
  let s_index = select_list.value.findIndex((row) => row.item_no == selectedDepartment.value);
  select_name.value = select_list.value[s_index]['item_name']

  load.value = true
  patientLoad.value = true
  // let url = `${api_url}/AiCase/getPatientList`
  let url = `${api_url}/AiCase/getPatientListByDeptId`

  post(url, JSON.stringify({ item_no: selectedDepartment.value })).then(res => {
    user_list.value = res.data.map((row) => {
      row.show = true
      return row
    })
    patientLoad.value = false
  }).catch(error => {
    patientLoad.value = false
    console.error('发生错误:', error);
  });
}

// 获取病区
const getWardList = () => {
  let url = `${api_url}/AiCase/getWardList`
  load.value = true
  post(url, JSON.stringify({})).then(res => {
    console.log(res)
    select_list.value = res.data
    load.value = false
  }).catch(error => {
    load.value = false
    console.error('发生错误:', error);
  });
}
getWardList()


// 模拟病人列表数据
const patients = ref([
  { id: '1', name: '患者姓名' },
  { id: '2', name: '患者姓名' },
  { id: '3', name: '患者姓名' },
  { id: '4', name: '患者姓名' },
])

// 最新病历数据
// const latestRecord = ref([{
//   type: '三级查房-主治医师',
//   time: '2024-12-12 09:01',
//   admissionCount: 10,
//   admissionTime: '2024-12-10 09:30'
// }])
</script>

<style scoped>
.tag-container {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 20px;
  margin-top: 6px;
}

.tag-item {
  font-size: 16px;
  padding: 8px 12px;
  margin-right: 10px;
  height: 30px;
  line-height: 30px;
}

.document-tag {
  background-color: rgb(242, 247, 251);
}

.pending-tag {
  background-color: rgba(255, 236, 217, 0.4);
}
.tag-section-box {
  display: flex;
  align-items: flex-start;
}

.tag-section-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 10px;
  color: #606266;
}

.document-tag-group {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px dashed #ebeef5;
}

.document-info {
  margin-bottom: 10px;
  display: flex;
  align-items: center;
}

.document-type {
  font-weight: bold;
  color: #409EFF;
  margin-right: 10px;
}

.document-date {
  color: #909399;
  font-size: 13px;
}

.pending-tag-section {
  margin-top: 30px;
}

.no-tags {
  color: #909399;
  font-size: 14px;
  text-align: center;
  margin-top: 20px;
}

.content {
  height: 60vh;
  overflow: auto;
}

.medical-record {
  height: 100%;
}

.patient-sidebar {
  border-right: 1px solid #e6e6e6;
  background-color: #f5f7fa;
  display: flex;
  flex-direction: column;
}

.sidebar-header {
  padding: 20px;
  border-bottom: 1px solid #e6e6e6;
}

.ward-select {
  width: 100%;
}

.patient-list {
  flex: 1;
  border-right: none;
  /* overflow-y: scroll; */
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.task-section {
  margin-top: 20px;
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.el-main {
  padding: 20px;
  background-color: #fff;
}
</style>