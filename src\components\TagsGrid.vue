<template>
  <el-scrollbar class="pending-tags-grid" style="height: 100%;">
    <div
      v-for="(tag, index) in tags"
      :key="tag.id"
      :class="['pending-tag-item', { 'tag-selected': isTagSelected(tag.id) }]"
      :style="{ backgroundColor: getRandomColor(tag.id) }"
      @click.prevent="isMultiSelectMode ? toggleTagSelection(tag.id) : showTagActions($event, tag.id)"
      :draggable="draggable"
      @dragstart="onPendingTagDragStart($event, tag)"
      @dragend="onPendingTagDragEnd"
    >
      <!-- 选择状态下显示的选中标记 -->
      <div class="selection-indicator" v-if="isMultiSelectMode">
        <el-checkbox v-model="tag.selected"></el-checkbox>
      </div>
      <span @click="handleFilterKeys(tag.content)">{{ tag.content }}</span>
      <slot name="end" :tag="tag"></slot>
      <!-- 操作按钮插槽 -->
      <div class="tag-actions" v-show="activeTagId === tag.id && !isMultiSelectMode">
        <slot name="actions" :tag="tag"></slot>
        <button class="action-btn copy-btn" @click.stop="copy(tag.name)">复制</button>
      </div>
    </div>
    <!-- 拖动时的自定义拖拽预览 -->
    <div
      v-if="isMultiSelectMode && draggingTagIds.length > 0"
      class="drag-preview"
      :style="{ position: 'fixed', left: dragPreviewPos.x + 'px', top: dragPreviewPos.y + 'px', pointerEvents: 'none', zIndex: 9999 }"
    >
      <div class="drag-preview-item" v-for="id in draggingTagIds" :key="id">
        {{ tags.find(t => t.id === id)?.content }}
      </div>
    </div>
    <!-- 加载状态提示 -->
    <div v-if="loading" class="loading-more">加载中...</div>
  </el-scrollbar>
</template>

<script setup>
import { ref, onBeforeUnmount } from 'vue'
// import { ElMessage } from 'element-plus';


const props = defineProps({
  tags: { type: Array, required: true },
  isMultiSelectMode: Boolean,
  activeTagId: [Number, String, null],
  loading: Boolean,
  isTagSelected: { type: Function, required: true },
  draggable: { type: Boolean, default: false },
  
})

const emit = defineEmits([
  'toggleTagSelection',
  'showTagActions',
  'classifyTag',
  'abandonTag',
  'dragstart',
  'dragend',
  'filter-keys',
])


// 温馨色系色板
const warmColors = [
  '#FFE4E1', // 浅粉
  '#FFF8DC', // 浅米
  '#FFFACD', // 柠檬黄
  '#E0FFD6', // 浅绿
  '#D6F0FF', // 浅蓝
  '#FFF0F5', // 薰衣草
  '#FFEFD5', // 浅橙
  '#F0FFF0', // 蜜瓜绿
  '#F5F5DC', // 米色
  '#F0F8FF', // 爱丽丝蓝
  '#FFF5EE', // 海贝壳
  '#FDF6E3', // 柔和米
  '#FDEBD0', // 柔和橙
  '#E8DAEF', // 柔和紫
  '#F9E79F', // 柔和黄
];

function getRandomColor(seed) {
  // 用tag id做种子，保证同一标签颜色一致
  let hash = 0;
  for (let i = 0; i < String(seed).length; i++) {
    hash = String(seed).charCodeAt(i) + ((hash << 5) - hash);
  }
  const idx = Math.abs(hash) % warmColors.length;
  return warmColors[idx];
}
const copy = (text) => {
  if (navigator.clipboard) {
    navigator.clipboard.writeText(text);
  } else {
    // 兼容旧浏览器
    const textarea = document.createElement('textarea');
    textarea.value = text;
    textarea.style.position = 'fixed';
    textarea.style.opacity = '0';
    document.body.appendChild(textarea);
    textarea.focus();
    textarea.select();
    try {
      document.execCommand('copy');
    } catch {}
    document.body.removeChild(textarea);
  }
  ElMessage.success('已复制到剪贴板');
}

// 拖拽相关
const draggingTagIds = ref([])
const dragPreviewPos = ref({ x: 0, y: 0 })
let dragPreviewListener = null
let rafId = null


function handleFilterKeys(content) {
  emit('filter-keys', content)
}

function onPendingTagDragStart(event, tag) {
  // 多选拖拽
  if (props.isMultiSelectMode && props.isTagSelected(tag.id)) {
    draggingTagIds.value = props.tags.filter(t => t.selected).map(t => t.id)
    event.dataTransfer.effectAllowed = 'move'
    event.dataTransfer.setData('text/plain', JSON.stringify(draggingTagIds.value))
    dragPreviewPos.value = { x: event.clientX + 10, y: event.clientY + 10 }
    dragPreviewListener = (e) => {
      if (rafId) return
      rafId = requestAnimationFrame(() => {
        dragPreviewPos.value = { x: e.clientX + 10, y: e.clientY + 10 }
        rafId = null
      })
    }
    document.addEventListener('dragover', dragPreviewListener)
    if (event.dataTransfer.setDragImage) {
      const img = document.createElement('div')
      img.style.display = 'none'
      document.body.appendChild(img)
      event.dataTransfer.setDragImage(img, 0, 0)
      setTimeout(() => document.body.removeChild(img), 0)
    }
  } else {
    draggingTagIds.value = [tag.id]
    event.dataTransfer.effectAllowed = 'move'
    event.dataTransfer.setData('text/plain', JSON.stringify([tag.id]))
  }
  emit('dragstart', event, tag)
}

function onPendingTagDragEnd() {
  draggingTagIds.value = []
  dragPreviewPos.value = { x: 0, y: 0 }
  if (dragPreviewListener) {
    document.removeEventListener('dragover', dragPreviewListener)
    dragPreviewListener = null
  }
  if (rafId) {
    cancelAnimationFrame(rafId)
    rafId = null
  }
  emit('dragend')
}

onBeforeUnmount(() => {
  if (dragPreviewListener) {
    document.removeEventListener('dragover', dragPreviewListener)
    dragPreviewListener = null
  }
  if (rafId) {
    cancelAnimationFrame(rafId)
    rafId = null
  }
})

// 事件透传
function toggleTagSelection(tagId) {
  emit('toggleTagSelection', tagId)
}
function showTagActions(event, tagId) {
  emit('showTagActions', event, tagId)
}
</script>

<style scoped>
.pending-tags-grid {
  display: flex;
  flex-wrap: wrap;
  align-content: start;
  flex: 1 1 0;
  /* overflow: auto; */
  /* padding: 10px; */
}
.pending-tag-item {
  position: relative;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  border-radius: 4px;
  padding: 8px 12px;
  height: 18px;
  margin-bottom: 8px;
  margin-right: 8px;
  white-space: nowrap;
  overflow: visible;
  text-overflow: ellipsis;
  background-color: rgb(242, 247, 251);
  border: 1px solid rgb(181, 181, 181);
  transition: all 0.2s ease;
}
.pending-tag-item span {
  user-select: text;
  -webkit-user-select: text;
  cursor: text;
}
.pending-tag-item.tag-selected {
  background-color: #ecf5ff;
  border-color: #409eff;
  color: #409eff;
}
.selection-indicator {
  margin-right: 5px;
}
.tag-actions {
  position: absolute;
  top: 100%;
  left: 0;
  background-color: #fff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1);
  border-radius: 4px;
  z-index: 1000;
  display: flex;
  overflow: visible;
  margin-top: 5px;
}
.action-btn {
  border: none;
  padding: 6px 12px;
  cursor: pointer;
  font-size: 12px;
  text-align: center;
  white-space: nowrap;
}
.action-btn.classify-btn {
  background-color: #f0f9eb;
  color: #67c23a;
}
.action-btn.classify-btn:hover {
  background-color: #e1f3d8;
}
.action-btn.abandon-btn {
  background-color: #fef0f0;
  color: #f56c6c;
}
.action-btn.abandon-btn:hover {
  background-color: #fde2e2;
}
.action-btn.copy-btn {
  background-color: #FFFACD;
  color: #00f;
}
.action-btn.copy-btn:hover {
  background-color: #FFFACD;
}
.loading-more {
  width: 100%;
  text-align: center;
  color: #999;
  margin-top: 20px;
}
.drag-preview {
  background: rgba(64,158,255,0.9);
  color: #fff;
  border-radius: 4px;
  padding: 6px 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
  min-width: 80px;
  pointer-events: none;
  font-size: 13px;
}
.drag-preview-item {
  padding: 2px 0;
}
:deep(.el-scrollbar__view) {
  padding: 10px
}
</style>