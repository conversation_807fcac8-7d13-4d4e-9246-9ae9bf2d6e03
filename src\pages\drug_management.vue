<template>
    <div class="tableData">
        <h3>{{ route.meta.title }}</h3>
        <div class="topMenu">
            <div class="menuBox">
                <el-input style="display: none;"></el-input>
                <el-input class="menu" placeholder="名称" v-model="curName" clearable @input="debouncedGetGridDataAgain"></el-input>
                <el-input class="menu" placeholder="药品分类1" v-model="curCategory" clearable @input="debouncedGetGridDataAgain"></el-input>
                <el-input class="menu" placeholder="作用功效" v-model="curEfficacy" clearable @input="debouncedGetGridDataAgain"></el-input>
            </div>
            <el-button type="primary" @click="tagDownload">新增</el-button>
        </div>
        <el-table ref="tableRef" class="table" :data="filteredTableData" v-loading="load" @selection-change="handleSelectionChange">
            <el-table-column width="150" label="名称" prop="name"></el-table-column>
            <el-table-column width="150" label="处理过的名称" prop="name_h"></el-table-column>
            <el-table-column width="90" label="药品分类1" prop="category_level1"></el-table-column>
            <el-table-column width="90" label="药品分类2" prop="category_level2"></el-table-column>
            <el-table-column width="80" label="药品类型" prop="category_type"></el-table-column>
            <el-table-column width="80" label="药品机型" prop="dosage_form"></el-table-column>
            <el-table-column width="300" label="作用功效" prop="efficacy"></el-table-column>
            <el-table-column width="120" label="国家医保名称" prop="insurance_name"></el-table-column>
            <el-table-column width="120" label="产地" prop="manufacturer"></el-table-column>
            <el-table-column width="120" label="规格" prop="specification"></el-table-column>
            <el-table-column width="60" label="计量单位" prop="unit"></el-table-column>
            <el-table-column width="120" label="更新时间" prop="updated_at"></el-table-column>
            <el-table-column label="操作"  >
                <template #default="{ row }">
                    <div>
                        <el-button type="primary" @click="editData(row)">编辑</el-button>
                        <el-button type="danger" @click="deleteData(row)">删除</el-button>
                    </div>
                </template>
            </el-table-column>
        </el-table>
        <Pagination style="margin-top: 10px; display: flex; justify-content: flex-end;" :currentPage="currentPage"
            :pageSize="pageSize" :total="totalItems" :pageSizes="[20, 30, 40, 50, 100]" @current-change="handleCurrentChange"
            @size-change="handleSizeChange" />
        
        <!-- 编辑/新增弹窗 -->
        <el-dialog v-model="dialogVisible" :title="isEditMode ? '编辑药品信息' : '新增药品信息'" width="60%" :before-close="handleClose">
            <el-form :model="editForm" :rules="rules" ref="editFormRef" label-width="120px">
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="名称" prop="name">
                            <el-input v-model="editForm.name" placeholder="请输入药品名称"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="处理过的名称" prop="name_h">
                            <el-input v-model="editForm.name_h" placeholder="请输入处理过的名称"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="药品分类1" prop="category_level1">
                            <el-input v-model="editForm.category_level1" placeholder="请输入药品分类1"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="药品分类2" prop="category_level2">
                            <el-input v-model="editForm.category_level2" placeholder="请输入药品分类2"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="药品类型" prop="category_type">
                            <el-input v-model="editForm.category_type" placeholder="请输入药品类型"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="药品机型" prop="dosage_form">
                            <el-input v-model="editForm.dosage_form" placeholder="请输入药品机型"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-form-item label="作用功效" prop="efficacy">
                    <el-input v-model="editForm.efficacy" type="textarea" :rows="3" placeholder="请输入作用功效"></el-input>
                </el-form-item>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="国家医保名称" prop="insurance_name">
                            <el-input v-model="editForm.insurance_name" placeholder="请输入国家医保名称"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="产地" prop="manufacturer">
                            <el-input v-model="editForm.manufacturer" placeholder="请输入产地"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="规格" prop="specification">
                            <el-input v-model="editForm.specification" placeholder="请输入规格"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="计量单位" prop="unit">
                            <el-input v-model="editForm.unit" placeholder="请输入计量单位"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="handleConfirm" :loading="submitLoading">确认</el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Loading, Warning, Document } from '@element-plus/icons-vue';
import c from "@/utils/config";
import Pagination from '@/components/Pagination.vue';
import dayjs from 'dayjs'
import { json } from 'd3';
import { useRoute } from 'vue-router';
import { baseUrl } from '../utils/baseUrl';
import * as XLSX from 'xlsx';
import config from '../utils/config';

// 防抖函数
const debounce = (func, delay) => {
    let timeoutId;
    return function (...args) {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => func.apply(this, args), delay);
    };
};

const route = useRoute();
const api = config.py_api_url
const api1 = baseUrl.url10
const selectedRows = ref([]) // 选中的行
// 处理表格选择变化
const handleSelectionChange = (selection) => {
    selectedRows.value = selection
}

// 当前搜索名称
const curName = ref('')
// 当前搜索分类
const curCategory = ref('')
// 当前搜索功效
const curEfficacy = ref('')
// 计算属性：根据 curUserName 过滤医生名称
const filteredTableData = computed(() => {
    return tableData.value
    //   if (!curName.value && !curCategory.value) { 
    //   }

    //   if (curName.value || curCategory.value) {
    //     return tableData.value.filter(item =>
    //       item.name && item.name.includes(curName.value) && 
    //       item.category_level1 && item.category_level1.includes(curCategory.value)
    //     )
    //   }
})

const load = ref(false)
const tableData = ref([])

// 表格引用和多选相关
const tableRef = ref(null)

// 分页相关数据
const currentPage = ref(1)
const pageSize = ref(50)
const totalItems = ref(0)



const getGridData = () => {
    load.value = true

    const params = {
        page: currentPage.value,
        page_size: pageSize.value,
        name: curName.value,
        category_level1: curCategory.value, 
        efficacy: curEfficacy.value, 
    }
    // 过滤空参数
    const filteredParams = Object.entries(params).filter(([key, value]) => value !== '' && value !== undefined && value !== null);
    const queryString = new URLSearchParams(filteredParams).toString();

    fetch(api + `/v1/api/medicines?${queryString}`, {
        method: 'GET',
        headers: {
            'x-api-key': 'API655c18174462a06ede7ffbfa5aca4270c645b9b78ca30817e3a1bd496f34e04b',
            'authorization': 'Bearer 6787c226c3eae7.874922141Z3hNBMuGI'
        },
    })
        .then(response => response.json())
        .then(res => {
            tableData.value = res.data.records
            totalItems.value = res.data.page_info.total_count
            load.value = false;
        })
        .catch(error => {
            load.value = false;
            ElMessage.error('获取数据失败，请稍后重试')
        });
}
const deleteData = (row) => {
    ElMessageBox.confirm(
        `确定要删除药品"${row.name}"吗？`,
        '确认删除',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        }
    )
        .then(() => {
            // 用户确认删除
            load.value = true
            
            fetch(api + `/v1/api/medicines/delete/${row.id}`, {
                method: 'DELETE',
                headers: {
                    'x-api-key': 'API655c18174462a06ede7ffbfa5aca4270c645b9b78ca30817e3a1bd496f34e04b',
                    'authorization': 'Bearer 6787c226c3eae7.874922141Z3hNBMuGI'
                },
            })
                .then(response => response.json())
                .then(res => {
                    ElMessage.success('删除成功')
                    getGridData()
                    load.value = false
                })
                .catch(error => {
                    ElMessage.error('删除失败，请稍后重试')
                    load.value = false
                });
        })
        .catch(() => {
            // 用户取消删除
            ElMessage.info('已取消删除')
        })
}

const dialogVisible = ref(false)
const editFormRef = ref(null)
const submitLoading = ref(false)
const isEditMode = ref(false) // 区分编辑和新增模式

// 编辑表单数据
const editForm = ref({
    id: '',
    name: '',
    name_h: '',
    category_level1: '',
    category_level2: '',
    category_type: '',
    dosage_form: '',
    efficacy: '',
    insurance_name: '',
    manufacturer: '',
    specification: '',
    unit: ''
})

// 表单验证规则
const rules = {
    name: [
        { required: true, message: '请输入药品名称', trigger: 'blur' }
    ],
    category_level1: [
        { required: true, message: '请输入药品分类1', trigger: 'blur' }
    ]
}

const editData = (row) => {
    isEditMode.value = true
    // 将行数据复制到编辑表单
    editForm.value = {
        id: row.id,
        name: row.name || '',
        name_h: row.name_h || '',
        category_level1: row.category_level1 || '',
        category_level2: row.category_level2 || '',
        category_type: row.category_type || '',
        dosage_form: row.dosage_form || '',
        efficacy: row.efficacy || '',
        insurance_name: row.insurance_name || '',
        manufacturer: row.manufacturer || '',
        specification: row.specification || '',
        unit: row.unit || ''
    }
    dialogVisible.value = true
}

// 确认编辑
const confirmEdit = async () => {
    try {
        await editFormRef.value.validate()
        submitLoading.value = true
        
        const response = await fetch(api + `/v1/api/medicines/update/${editForm.value.id}`, {
            method: 'PATCH',
            headers: {
                'Content-Type': 'application/json',
                'x-api-key': 'API655c18174462a06ede7ffbfa5aca4270c645b9b78ca30817e3a1bd496f34e04b',
                'authorization': 'Bearer 6787c226c3eae7.874922141Z3hNBMuGI'
            },
            body: JSON.stringify(editForm.value)
        })
        
        const result = await response.json()
        
        if (response.ok) {
            ElMessage.success('编辑成功')
            dialogVisible.value = false
            getGridData() // 刷新表格数据
        } else {
            ElMessage.error(result.message || '编辑失败')
        }
    } catch (error) {
        ElMessage.error('编辑失败，请稍后重试')
    } finally {
        submitLoading.value = false
    }
}

// 新增药品
const addData = () => {
    isEditMode.value = false
    // 重置表单数据
    editForm.value = {
        name: '',
        name_h: '',
        category_level1: '',
        category_level2: '',
        category_type: '',
        dosage_form: '',
        efficacy: '',
        insurance_name: '',
        manufacturer: '',
        specification: '',
        unit: ''
    }
    dialogVisible.value = true
}

// 确认新增
const confirmCreate = async () => {
    try {
        await editFormRef.value.validate()
        submitLoading.value = true
        
        const response = await fetch(api + `/v1/api/medicines/create`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'x-api-key': 'API655c18174462a06ede7ffbfa5aca4270c645b9b78ca30817e3a1bd496f34e04b',
                'authorization': 'Bearer 6787c226c3eae7.874922141Z3hNBMuGI'
            },
            body: JSON.stringify(editForm.value)
        })
        
        const result = await response.json()
        
        if (response.ok) {
            ElMessage.success('新增成功')
            dialogVisible.value = false
            getGridData() // 刷新表格数据
        } else {
            ElMessage.error(result.message || '新增失败')
        }
    } catch (error) {
        ElMessage.error('新增失败，请稍后重试')
    } finally {
        submitLoading.value = false
    }
}

// 确认按钮的统一处理
const handleConfirm = () => {
    if (isEditMode.value) {
        confirmEdit()
    } else {
        confirmCreate()
    }
}

// 关闭弹窗
const handleClose = () => {
    editFormRef.value?.resetFields()
    dialogVisible.value = false
    isEditMode.value = false
}

// 新增药品
const tagDownload = () => {
    addData()
}


const getGridDataAgain = () => {
    currentPage.value = 1
    getGridData()
}

// 创建防抖版本的 getGridDataAgain
const debouncedGetGridDataAgain = debounce(getGridDataAgain, 500);

// 处理页码变化
const handleCurrentChange = (val) => {
    currentPage.value = val
    // 页码变化时重新获取数据
    getGridData()
}

// 处理每页显示条数变化
const handleSizeChange = (val) => {
    pageSize.value = val
    // 重置为第一页
    currentPage.value = 1
    // 重新获取数据
    getGridData()
}





onMounted(() => {
  getGridData()
})
onUnmounted(() => {
})
</script>
<style scoped>
.tableData {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    background-color: #ffffff;
    padding: 10px;
    box-sizing: border-box;
}

h3 {
    height: 40px;
}

.topMenu {
    display: flex;
    justify-content: flex-start;
    margin-bottom: 10px;
    flex-wrap: wrap;
    gap: 5px;
}

.menuBox {
    display: flex;
    justify-content: flex-start;
    margin-bottom: 10px;
    flex-wrap: wrap;
    gap: 5px;
}

.table {
    flex: 1;
    overflow-y: auto;
}

.menu {
    width: 200px;
}
</style>

