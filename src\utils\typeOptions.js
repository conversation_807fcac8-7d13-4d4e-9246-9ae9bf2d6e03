

export const typeOptions = {
    options:[
        {
            value: 1,
            label: '入院记录(合作)',
            children: [
                {value: 14, label: '主诉',},
                {value: 15, label: '现病史',},
                {value: 16, label: '既往史',},
                {value: 17, label: '个人史',},
                // {value: 18, label: '月经史',},
                {value: 17, label: '婚育史',},
                {value: 18, label: '家族史',},
                {value: 19, label: '体格检查',},
                {value: 20, label: '初步诊断',},
                {value: 21, label: '入院诊断',},
                {value: 22, label: '补充诊断',}
            ],
        },
        {
            value: 2,
            label: '入院记录(不合作)',
            children:[
                {value: 23, label: '主诉',},
                {value: 24, label: '现病史',},
                {value: 25, label: '既往史',},
                {value: 26, label: '个人史',},
                // {value: 27, label: '月经史',},
                {value: 28, label: '婚育史',},
                {value: 29, label: '家族史',},
                {value: 30, label: '体格检查',},
                {value: 31, label: '初步诊断',},
                // {value: 32, label: '入院诊断',},
                // {value: 33, label: '补充诊断',}
            ]
        },
        {
            value: 3,
            label: '入院记录(内转)',
            children:[
                {value: 34, label: '主诉',},
                {value: 35, label: '现病史',},
                {value: 36, label: '既往史',},
                {value: 37, label: '个人史',},
                // {value: 38, label: '月经史',},
                {value: 39, label: '婚育史',},
                {value: 40, label: '家族史',},
                {value: 41, label: '体格检查',},
                {value: 42, label: '初步诊断',},
                // {value: 43, label: '入院诊断',},
                // {value: 44, label: '补充诊断',}
            ]
        },
        {
            value: 4,
            label: '入院记录(复发)',
            children:[
                {value: 45, label: '主诉',},
                {value: 46, label: '现病史',},
                {value: 47, label: '既往史',},
                {value: 48, label: '个人史',},
                // {value: 49, label: '月经史',},
                {value: 50, label: '婚育史',},
                {value: 51, label: '家族史',},
                {value: 52, label: '体格检查',},
                {value: 53, label: '初步诊断',},
                // {value: 54, label: '入院诊断',},
                // {value: 55, label: '补充诊断',}
            ]
        },
        {
            value: 5,
            label: '首次病程记录',
            children:[
                {value: 56, label: '概况',},
                {value: 57, label: '主要表现',},
                {value: 58, label: '既往史',},
                {value: 59, label: '个人史',},
                {value: 62, label: '家族史',},
                {value: 60, label: '入院查体',},
                {value: 61, label: '精神检查',},
                {value: 63, label: '辅助检查',},
                {value: 64, label: '初步诊断',},
                {value: 67, label: '诊断依据',},
                {value: 65, label: '鉴别诊断',},
                {value: 66, label: '诊疗计划',}
            ]
        },
        {
            value: 6,
            label: '首次病程记录(内转)',
            children:[
                {value: 67, label: '概况',},
                {value: 68, label: '主要表现',},
                {value: 69, label: '既往史',},
                {value: 70, label: '个人史',},
                {value: 71, label: '家族史',},
                {value: 72, label: '入院查体',},
                {value: 73, label: '精神检查',},
                {value: 74, label: '辅助检查',},
                {value: 75, label: '初步诊断',},
                {value: 76, label: '诊断依据',},
                {value: 77, label: '鉴别诊断',},
                {value: 78, label: '诊疗计划',}
            ]
        },
        {
            value: 1288,
            label: '首次病程记录(复发)',
            children:[
                {value: 67, label: '概况',},
                {value: 68, label: '主要表现',},
                {value: 69, label: '既往史',},
                {value: 70, label: '个人史',},
                {value: 71, label: '家族史',},
                {value: 72, label: '入院查体',},
                {value: 73, label: '精神检查',},
                {value: 74, label: '辅助检查',},
                {value: 75, label: '初步诊断',},
                {value: 76, label: '诊断依据',},
                {value: 77, label: '鉴别诊断',},
                {value: 78, label: '诊疗计划',}
            ]
        },
        {
            value: 7,
            label: '三级医师查房(主治)',
            children:[
                {value: 88, label: '个人史',},
                {value: 86, label: '主要表现',},
                {value: 89, label: '家族史',},
                {value: 87, label: '既往史',},
                {value: 85, label: '查体',},
                {value: 90, label: '查房',},
                {value: 91, label: '治疗方案',},
                {value: 92, label: '精神检查',},
                {value: 93, label: '诊断',},
                {value: 94, label: '诊断依据',},
                {value: 95, label: '辅助检查',},
                {value: 114, label: '鉴别诊断',},
            ]
        },
        {
            value: 8,
            label: '三级医师查房(主任)',
            children:[
                {value: 96, label: '个人史',},
                {value: 97, label: '主要表现',},
                {value: 98, label: '家族史',},
                {value: 99, label: '既往史',},
                {value: 100, label: '查体',},
                {value: 101, label: '查房',},
                {value: 102, label: '治疗方案',},
                {value: 103, label: '精神检查',},
                {value: 104, label: '诊断',},
                {value: 105, label: '诊断依据',},
                {value: 106, label: '辅助检查',},
                {value: 115, label: '鉴别诊断',},
            ]
        },
        {value: 9,label: '日常病程记录',},
        {value: 10,label: '日常病程记录(异常)',},
        {value: 11,label: '上级医师查房记录',},
        {
            value: 12,
            label: '阶段小结',
            children:[
                {value: 79, label: '入院情况',},
                {value: 80, label: '入院诊断',},
                {value: 81, label: '目前情况',},
                {value: 82, label: '目前诊断',},
                {value: 83, label: '诊疗经过',},
                {value: 84, label: '诊疗计划',},

            ]
        },
        {value: 13,label: '出院查房记录',},
        {
            value: 14,
            label: '出院记录',
            children:[
                {value: 107, label: '入院情况',},
                {value: 108, label: '入院诊断',},
                {value: 109, label: '出院医嘱',},
                {value: 110, label: '出院时情况',},
                {value: 111, label: '出院诊断',},
                {value: 112, label: '诊疗经过',},
                {value: 113, label: '辅助检查',},
            ]
        },
    ]
}
